import axios from './axios'
import { getBrowserType, extractUaValues, getExeVersion, getElectronVersion, getOSType, getCookie, webpFormat, getOSVersion } from '../index'
import { useToast } from 'vue-toastification'
const toast = useToast()

let defaultParams = null
let heyboxPcDefaultParams = null
const getDefaultParams = async () => {
  let version = window.versionAPI.version
	if(defaultParams) {
		return defaultParams
	}
	defaultParams = {
		client_type: 'heybox_pc',
		x_client_type: 'pc',
		os_type: 'web',
		x_os_type: getOSType(),
		device_info: getBrowserType(),
		x_app: 'heybox_pc',
		version,
		web_version: '1.0.0',
		exe_version: getExeVersion(),
		electron_version: getElectronVersion(),
		client_bit: window.exe_bit,
		win_version: window.windows_version,
		heybox_id: window.electronAPI.getStoreData('user_id', 'account'),
	}
	return defaultParams
}

const getHeyboxPcDefaultParams = async () => {
  if(heyboxPcDefaultParams) {
    return heyboxPcDefaultParams
  }
	let version = window.versionAPI.version
	let device_id = await window.electronAPI.getDeviceId()
	heyboxPcDefaultParams = {
		x_client_type: 'pc',
		x_os_type: getOSType(),
		x_app: 'heybox_pc',
		version,
		exe_version: getExeVersion(),
		electron_version: getElectronVersion(),
		client_bit: window.exe_bit,
		os_version: getOSVersion(),
		heybox_id: window.electronAPI.getStoreData('heybox_id', 'account'),
		device_id,
	}
	return heyboxPcDefaultParams
}

// 移除 top-level await，改为函数调用
const initDefaultParams = async (url) => {
	if(includeHeyboxpc(url)) {
		return getHeyboxPcDefaultParams()
	} else {
		return getDefaultParams()
	}
}

const {BASE_XHH} = extractUaValues()

window.SUPPORT_WEBP = undefined

export async function axiosGetHandler (url, args = {}, apps = {}) {
	const defaultParams = await initDefaultParams(url)
	let params = Object.assign({
		heybox_id: window.electronAPI.getStoreData('heybox_id', 'account')
	}, defaultParams,  apps, args)
  if (window.SUPPORT_WEBP == undefined) {
    return Promise.all([httpRequest('GET', url, params), webpFormat()]).then(res_list => res_list[0])
  } else {
    return httpRequest('GET', url, params)
  }
}

export async function axiosPostHandler (url, args = {}, formdata, apps = {}) {
	const defaultParams = await initDefaultParams(url)
	let params = Object.assign({
		heybox_id: window.electronAPI.getStoreData('heybox_id', 'account')
	}, defaultParams, apps, args)
  if (window.SUPPORT_WEBP == undefined) {
    return Promise.all([httpRequest('POST', url, params, formdata), webpFormat()]).then(res_list => res_list[0])
  } else {
    return httpRequest('POST', url, params, formdata)
  }
}

function httpRequest (method, url, args, formdata) {
  let ContentType = "application/x-www-form-urlencoded;charset=utf-8"
	let path = '', upload
	let promise;
	if (url.indexOf(BASE_XHH) === 0) {
		try {
			let u = new URL(url)
			path = u.pathname
		} catch (e) {
			path = url.replace(BASE_XHH, '')
		}
	} else {
		path = url
	}
	if (includeV2(path) || includeHeyboxpc(path)) { // v2及以上，pc小黑盒的接口传JSON
		ContentType = "application/json;charset=utf-8"
	}
	if (method === 'POST') {
		if (args._is_upload) {
			ContentType = 'multipart/form-data;'
			upload = args._is_upload
		}
		promise = axios.post(url, formdata, {
			headers: {
				"Content-Type": ContentType
			},
			timeout: 30000,
			responseType: "json",
			params: args,
			data: formdata,
			upload,
		})
	} else {
		promise = axios.get(url, {
			headers: {
				"Content-Type": ContentType
			},
			timeout: 30000,
			responseType: "json",
			params: args
		})
	}
	promise.then(res => {
		if([400, 401, 402, 403, 404].includes(res.status)) {
			toast.error("发生了一些错误，请检查网络或稍后重试")
		} else if([500, 501, 502, 503, 504].includes(res.status)) {
			toast.error("发生了一些错误，请稍后重试")
		}
	})
	return promise
}

function includeV2(str) {
  const regex = /\/v([2-9]|[1-9]\d+)\//;
  return regex.test(str);
}

function includeHeyboxpc(str) {
  const regex = /\/heyboxpc\//;
  return regex.test(str);
}
  