<template>
  <div class="cpt-steam-login-dialog">
    <div
      class="dialog-container"
      @click.stop
    >
      <div class="view-steam-login">
        <div class="cpt-header">
          <div class="back">
            <i 
              v-if="currentPageInfo.back" 
              class="iconfont icon-common-arrow-left-line"
              @click="toPage(currentPageInfo.back)"
            ></i>
          </div>
          <div class="title">
            {{ currentPageInfo.title }}
          </div>
          <div class="close" @click="close">
            <i class="iconfont icon-common-close-line"></i>
          </div>
        </div>

        <component 
          class="cpt-content"
          :is="currentPageInfo.component" 
          ref="currentPageRef" 
          :params="currentPageParams"
          @toPage="toPage"
          @close="close"
          @login="handleLogin"
          @retry="handleRetry"
        />

        <div class="cpt-footer">
          <div v-tooltip="{text: describeText, placement: 'top', popperClass: 'describe-tooltip'}" class="describe">
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M14.3242 3.41732L8.32417 0.0839998C8.12217 -0.0279999 7.87816 -0.0279999 7.67616 0.0839998L1.67616 3.41732C1.46483 3.53466 1.3335 3.75732 1.3335 3.99932C1.3335 4.46199 1.4095 15.3326 8.00017 15.3326C14.5908 15.3326 14.6668 4.46199 14.6668 3.99932C14.6668 3.75732 14.5355 3.53466 14.3242 3.41732ZM12.0805 5.99821C12.4477 5.58567 12.4109 4.95358 11.9984 4.58638C11.5859 4.21918 10.9538 4.25593 10.5866 4.66846L7.19614 8.47746L6.09468 7.18477C5.73649 6.76439 5.10533 6.71398 4.68495 7.07217C4.26458 7.43036 4.21416 8.06152 4.57235 8.4819L6.41851 10.6486C6.60606 10.8687 6.8797 10.9968 7.16886 10.9999C7.45801 11.0031 7.73436 10.8809 7.92663 10.6649L12.0805 5.99821Z" fill="#32B846"/>
            </svg>
            <span>小黑盒Steam登录说明</span>
            <i class="iconfont icon-common-help-line"></i>
          </div>
          <div class="steam-client-login" @click="handleSteamClientLogin">
            <i class="iconfont icon-game-steam-platform-filled"></i>
            <span>Steam客户端登录</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SteamLoginDialog">
import { ref, onMounted, defineExpose, computed, nextTick, defineProps, defineEmits } from 'vue';
import {
  useSubComponent,
  getDefaultSubProps,
} from '../../composables/useSubComponent';
import SteamLogin from './view/SteamLogin.vue';
import SteamApprove from './view/SteamApprove.vue';
import SteamCode from './view/SteamCode.vue';
import SteamTimeout from './view/SteamTimeout.vue';
import { useToast } from 'vue-toastification'

const toast = useToast()
const loginData = ref({})

const describeText = '小黑盒Steam登录使用Steam本地接口唤起登录，本地的账号信息存储完全由steam管理。\nSteam登录信息仅用作本地Steam登录，不会经过网络传输，与本地Steam客户端登录状态安全性一致。'

const props = defineProps(getDefaultSubProps());
const messageTarget = ref('')

const { show, handleClose, Dialog } = useSubComponent({
  initDialog: (options) => {
    messageTarget.value = 'all'
    show.value = true;
  },
});

const currentPageRef = ref(null)
const userInfo = ref({})
const currentPage = ref('steam-login')
const currentPageParams = ref({})

const pageList = [
  {
    name: 'steam-login',
    title: '添加Steam账号',
    component: SteamLogin,
  },
  {
    name: 'steam-approve',
    title: '使用Steam令牌批准登录',
    component: SteamApprove,
    back: 'steam-login',
  },
  {
    name: 'steam-code',
    title: '使用Steam令牌验证码登录',
    component: SteamCode,
    back: 'steam-login',
  },
  {
    name: 'steam-timeout',
    title: '添加Steam账号',
    component: SteamTimeout,
    back: 'steam-login',
  },
]

const currentPageInfo = computed(() => {
  return pageList.find(item => item.name === currentPage.value)
})

const close = (data) => {
  handleClose();
};

const toPage = (page, params) => {
  currentPage.value = page
  if(params) {
    currentPageParams.value = params
  }
  if(page === 'steam-login') {
    currentPageParams.value = {
      account: loginData.value.accountName,
      password: loginData.value.password,
    }
  }
}

const handleRetry = (totpCode) => {
  handleLogin({
    ...loginData.value,
    totpCode
  })
}

const handleLogin = async (data) => {
  loginData.value = data
  try {
    window.steamAPI.onAppConfirmRequired(() => {
      console.log('onAppConfirmRequired', data)
      toPage('steam-approve', {
        account: data.accountName,
      })
    })
    window.steamAPI?.onGuardCodeRequired((params) => {
      console.log('onGuardCodeRequired', currentPage.value)
      if(currentPage.value === 'steam-code') {
        nextTick(() => {
          if(currentPageRef.value) {
            currentPageRef.value.handleError()
          }
        })
      } else {
        // 跳转到验证码输入页面
        toPage('steam-code', {
          account: data.accountName,
          type: 'guard',
          count: params.count
        })
      }
    })

    const result = await window.steamAPI.login(data)
    
    console.log('login result', result)
    if (result.status === 'ok') {
      window.webViewAPI.postMessage('library', 'steamInfoUpdate')
      window.webViewAPI.postMessage('library', 'toast', {
        type: 'success',
        message: 'Steam登录成功'
      })
      close()
    } else {
      console.log('handleError')
      if(result.msg === 'Invalid Password') {
        currentPageRef.value.handleError('请核对您的密码和账户名称并重试')
      } else if(result.msg === 'Rate Limit Exceeded') {
        currentPageRef.value.handleError('尝试次数过多，请稍后再试')
      } else if(result.msg === 'Timeout') {
        toPage('steam-timeout')
      } else if(result.msg !== ''){
        currentPageRef.value.handleError(result.msg)
      }
    }
  } catch (error) {
    console.error('Steam登录错误:', error)
    toast.error('登录过程中发生错误')
  } finally {
    window.steamAPI.removeAppConfirmRequired()
    window.steamAPI?.removeLoginProgressListener?.()
  }
}
const handleSteamClientLogin = () => {
  window.steamAPI.switchAccount('')
}
</script>

<style lang="scss">
.cpt-steam-login-dialog {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 1;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  .dialog-container {
    width: 640px;
    height: 559px;
    border-radius: 12px;
    background-color: $general-color-bg-3;
    border: 1px solid $general-color-stroke-0;
    overflow: hidden;
  }
}
.view-steam-login {
  width: 100%;
  height: 100%;
  background-color: $general-color-bg-3;
  .cpt-header {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 24px;
    background-color: $general-color-bg-4;
    .back { 
      width: 18px;
      height: 18px;
      line-height: 18px;
      .iconfont {
        font-size: 18px;
        color: $general-color-text-1;
        cursor: pointer;
      }
    }
    .title {
      color: $general-color-text-1;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 18px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
    .close {
      .iconfont {
        font-size: 18px;
        color: $general-color-text-4;
      }
    }
  }
  .cpt-content {
    height: calc(100% - 110px);
  }
  .cpt-footer {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 40px 40px;
    background-color: $general-color-bg-3;
    .describe {
      color: $general-color-text-3;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      display: flex;
      align-items: center;
      gap: 4px;
      line-height: 16px;
      .iconfont {
        font-size: 16px;
      }
    }
    .steam-client-login {
      display: flex;
      align-items: center;
      gap: 4px;
      color: $general-color-text-3;
      line-height: 16px;
      cursor: pointer;
      .iconfont {
        font-size: 16px;
      }
    }
  }
}
.describe-tooltip {
  width: 244px;
  white-space: pre-line;
  .tooltip-text {
    font-size: 14px;
    color: $general-color-text-1;
  }
}
</style>