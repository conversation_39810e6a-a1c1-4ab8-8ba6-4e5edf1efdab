<template>
  <div
    class="cpt-account-avatar"
    :style="{
      width: `${width}px`,
      height: `${width}px`,
    }"
  >
    <div class="user-avatar">
      <img
        :src="avatar"
        alt="avatar"
      />
    </div>
    <div
      class="user-decoration"
      v-if="showDecoration && decoration"
    >
      <img
        :src="decoration"
        alt="decoration"
      />
    </div>
    <div
      class="online-status-circle"
      v-if="onlineStatus"
      :class="onlineStatus"
    ></div>
  </div>
</template>
<script setup name="AccountCardAvatar">
import { defineProps } from 'vue';
const props = defineProps({
  avatar: {
    type: String,
    required: true,
  },
  decoration: {
    type: String,
    default: '',
  },
  width: {
    type: Number,
    default: 72,
  },
  showDecoration: {
    type: Boolean,
    default: false,
  },
  onlineStatus: {
    type: String,
    default: '',
    validator: (value) => ['', 'playing', 'online'].includes(value)
  },
});
</script>

<style lang="scss">
.cpt-account-avatar {
  width: 72px;
  height: 72px;
  aspect-ratio: 1/1;
  position: relative;
  .user-avatar {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 5px;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .user-decoration {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 116%;
    height: 116%;
    pointer-events: none;
    z-index: 1;
    object-fit: cover;
    border-radius: initial;
    img {
      width: 100%;
      height: 100%;
      pointer-events: none;
    }
  }

  .online-status-circle {
    position: absolute;
    right: -2px;
    bottom: -2px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    z-index: 2;

    // 游戏中状态
    &.playing {
      background-color: var(---general-color-success, #32B846);
      border: 2px solid var(---general-color-primary-0, #fff);
    }

    // 在线状态
    &.online {
      background-color: #008EE8;
      border: 2px solid var(---general-color-bg-4, #FFF);
    }
  }
}
</style>
