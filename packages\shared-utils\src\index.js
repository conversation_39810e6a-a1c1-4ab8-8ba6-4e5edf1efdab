import { ipcService } from '@heybox-app-web-shared/ipc';
import encrypt from './axios/encrypt/jsencrypt';

/**
 * 创建防抖函数
 * @param {Function} fn 要执行的函数
 * @param {Number} delay 延迟时间，单位毫秒
 * @returns {Function} 防抖处理后的函数
 */
export function debounce(fn, delay) {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}

/**
 * 创建节流函数
 * @param {Function} fn 要执行的函数 
 * @param {Number} interval 间隔时间，单位毫秒
 * @returns {Function} 节流处理后的函数
 */
export function throttle(fn, interval) {
  let last = 0;
  return function (...args) {
    const now = Date.now();
    if (now - last >= interval) {
      last = now;
      fn.apply(this, args);
    }
  };
}

/**
 * 格式化时间
 * @param {Date|Number|String} date 日期对象、时间戳或日期字符串
 * @param {String} format 格式化模板，如 'YYYY-MM-DD HH:mm:ss'
 * @returns {String} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  const d = new Date(date);
  const year = d.getFullYear();
  const month = d.getMonth() + 1;
  const day = d.getDate();
  const hour = d.getHours();
  const minute = d.getMinutes();
  const second = d.getSeconds();

  return format
    .replace(/YYYY/g, year)
    .replace(/MM/g, month.toString().padStart(2, '0'))
    .replace(/DD/g, day.toString().padStart(2, '0'))
    .replace(/HH/g, hour.toString().padStart(2, '0'))
    .replace(/mm/g, minute.toString().padStart(2, '0'))
    .replace(/ss/g, second.toString().padStart(2, '0'));
}

/**
 * 格式化游戏最后运行时间
 * @param {Number|String} timestamp 时间戳（秒或毫秒）
 * @returns {String} 格式化后的日期字符串，如 '2025.04.12'
 */
export function formatLastPlayed(timestamp) {
  if (!timestamp) {
    return '暂无记录';
  }

  try {
    // 判断是秒级还是毫秒级时间戳
    const ts = timestamp.toString().length === 10 ? timestamp * 1000 : timestamp;
    return formatDate(new Date(ts), 'YYYY.MM.DD');
  } catch (error) {
    console.warn('formatLastPlayed error:', error);
    return '暂无记录';
  }
}

/**
 * 格式化时长
 * @param {Number} duration 时长，单位秒
 * @param {Boolean} calHour 是否计算小时
 * @returns {String} 格式化后的时长字符串
 */
export function formatDuration(duration, calHour = false) {
  if (isNaN(duration)) {
    throw new Error("Invalid input. Expected a number.");
  }
  if (Number(duration) > 0 && Number(duration) < 1) {
    return '00:01'
  }

  if (calHour) {
    const hours = Math.floor(duration / 3600);
    const minutes = Math.floor((duration % 3600) / 60);
    const secs = duration % 60;
    const formattedHours = String(hours).padStart(2, "0");
    const formattedMinutes = String(minutes).padStart(2, "0");
    const formattedSeconds = String(secs).padStart(2, "0");

    if (hours > 0) {
      return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
    } else {
      return `${formattedMinutes}:${formattedSeconds}`;
    }
  } else {
    let totalSeconds = Math.floor(duration);
    let minutes = Math.floor(totalSeconds / 60);
    let remainingSeconds = totalSeconds % 60;

    return (
      String(minutes).padStart(2, "0") +
      ":" +
      String(remainingSeconds).padStart(2, "0")
    );
  }
}

// 获取最长公共子序列
export const getLongestCommonSubstring = (str1, str2) => {
  const m = str1.length;
  const n = str2.length;
  if(!m || !n || m === 1 || n === 1) return ''
  let dp = Array.from({ length: m + 1 }, () => Array(n + 1).fill(0));
  let maxLength = 0;
  let endIndex = 0;

  for (let i = 1; i <= m; i++) {
      for (let j = 1; j <= n; j++) {
          if (str1[i - 1] === str2[j - 1]) {
              dp[i][j] = dp[i - 1][j - 1] + 1;
              if (dp[i][j] > maxLength) {
                  maxLength = dp[i][j];
                  endIndex = i;
              }
          } else {
              dp[i][j] = 0;
          }
      }
  }

  const longestCommonSubstr = str1.substring(endIndex - maxLength, endIndex);
  return longestCommonSubstr;
}

export function formatRelativeTime(start, end) {
  const msPerSecond = 1000;
  const msPerMinute = 45 * msPerSecond;
  const msPerHour = 45 * msPerMinute;
  const msPerDay = 22 * msPerHour;
  const msPerMonth = 28 * msPerDay;
  const msPerYear = 345 * msPerDay;

  let delta = end - start;
  let relativeTime = "";

  if (delta < 0) {
    delta = -delta; // Take absolute value for past dates
  }
  let suffix = start < end ? "前" : "后";
  if (delta < msPerMinute) {
    relativeTime = "刚刚";
  } else if (delta < msPerHour) {
    const minutes = Math.round(delta / msPerMinute);
    relativeTime = minutes + " 分钟" + suffix;
  } else if (delta < msPerDay) {
    const hours = Math.round(delta / msPerHour);
    relativeTime = hours + " 小时" + suffix;
  } else if (delta < msPerMonth) {
    const days = Math.round(delta / msPerDay);
    relativeTime = days + " 天" + suffix;
  } else if (delta < msPerYear) {
    const months = Math.round(delta / msPerMonth);
    relativeTime = months + " 月" + suffix;
  } else {
    const years = Math.round(delta / msPerYear);
    relativeTime = years + " 年" + suffix;
  }

  return relativeTime;
}

/**
 * 获取应用系统信息
 * @returns {Promise<Object>} 系统信息对象
 */
export async function getSystemInfo() {
  try {
    const systemInfo = await ipcService.invoke('system:getInfo');
    return {
      ...systemInfo,
      timestamp: Date.now(),
      formattedTime: formatDate(new Date())
    };
  } catch (error) {
    console.error('获取系统信息失败:', error);
    return {
      platform: 'unknown',
      version: 'unknown',
      timestamp: Date.now(),
      formattedTime: formatDate(new Date()),
      error: error.message
    };
  }
}

/**
 * 通知主进程记录日志
 * @param {String} level 日志级别: 'info', 'warn', 'error'
 * @param {String} message 日志消息
 * @param {Object} meta 额外的元数据
 */
export function logToMain(level = 'info', message, meta = {}) {
  const logData = {
    level,
    message,
    meta: {
      ...meta,
      timestamp: Date.now(),
      formattedTime: formatDate(new Date())
    }
  };
  
  ipcService.send('logger:write', logData);
}

/**
 * 发送应用统计数据
 * @param {String} event 事件名称
 * @param {Object} data 事件数据
 */
export function sendAnalytics(event, data = {}) {
  const analyticsData = {
    event,
    data: {
      ...data,
      timestamp: Date.now(),
      formattedTime: formatDate(new Date())
    }
  };
  
  ipcService.send('analytics:track', analyticsData);
}

export const getParams = function (url) {
  var url = url || window.location.search;
  if (url.indexOf("?") < 0) {
    return false;
  }
  return url.match(/([^?=&]+)(=([^&]*))/g).reduce(function (a, v) {
    return (a[v.slice(0, v.indexOf("="))] = v.slice(v.indexOf("=") + 1)), a;
  }, {});
};

// 拿到url中的具体某个参数的值
export const getUrlParam = function (key, url) {
  var url = url ? new URL(url).search : window.location.search;
  var params = getParams(url);
  return params[key] ? params[key] : "";
};

// 获取ua中的接口base路由
export function extractUaValues() {
  // 如果已经缓存过，直接返回缓存结果
  if (window._UA_VALUES_CACHE) {
    return window._UA_VALUES_CACHE;
  }

  const userAgent = window.navigator.userAgent;
  const result = {};

  // 使用正则表达式匹配 key/value 模式
  // 匹配格式：key/value，其中 key 可以包含字母、数字、@、-、.，value 可以包含任意字符直到空格
  const regex = /([a-zA-Z0-9@\-\_\.\/]+)\/([^\s]+)/g;
  let match;

  while ((match = regex.exec(userAgent)) !== null) {
    const key = match[1];
    let value = match[2];
    
    // 对 value 进行 URL 解码
    try {
      value = decodeURIComponent(value);
    } catch (e) {
      // 如果解码失败，保持原值
      console.warn('Failed to decode UA value:', value);
    }
    
    result[key] = value;
  }

  // 缓存结果到 window 对象
  window._UA_VALUES_CACHE = result;
  
  return result;
}

export function getExeVersion() {
  if (window._EXE_VERSION !== undefined) {
    return window._EXE_VERSION;
  }
  let EXE_VERSION = "";
  const regex = /EXE_VERSION\/([^ ]+)/;
  const matches = window.navigator.userAgent.match(regex);
  if (matches && matches.length === 2) {
    EXE_VERSION = decodeURIComponent(matches[1]);
  }
  window._EXE_VERSION = EXE_VERSION;
  return window._EXE_VERSION;
}

export function getElectronVersion() {
  if (window._ELECTRON_VERSION !== undefined) {
    return window._ELECTRON_VERSION;
  }
  let ELECTRON_VERSION = "";
  const regex = /ELECTRON_VERSION\/([^ ]+)/;
  const matches = window.navigator.userAgent.match(regex);
  if (matches && matches.length === 2) {
    ELECTRON_VERSION = decodeURIComponent(matches[1]);
  }
  window._ELECTRON_VERSION = ELECTRON_VERSION;
  return window._ELECTRON_VERSION;
}

export function getOSType() {
  if (window && window.navigator.userAgent) {
    const ua = window.navigator.userAgent.toLocaleLowerCase();
    if (/(iphone|ipad|ios)/i.test(ua)) {
      return "iOS";
    } else if (/mac os/i.test(ua)) {
      return "Mac";
    } else if (/(android|harmony)/i.test(ua)) {
      return "Android";
    } else {
      return "Windows";
    }
  }
  return "";
}

export function getOSVersion() {
  let os_version = getOSType();
  if (os_version === "Windows") {
    return window.navigator.userAgent.toLocaleLowerCase().match(/windows nt ([0-9.]+)/)?.[1] || "";
  }
  return "";
}

export function getBrowserType() {
  if (window && window.navigator.userAgent) {
    const ua = window.navigator.userAgent.toLocaleLowerCase();
    let browserType = "";
    if (ua.match(/msie/) != null || ua.match(/trident/) != null) {
      browserType = "IE";
    } else if (ua.match(/edg|edge/) != null) {
      browserType = "Edge";
    } else if (ua.match(/firefox/) != null) {
      browserType = "firefox";
    } else if (ua.match(/ucbrowser/) != null) {
      browserType = "UC";
    } else if (ua.match(/opera/) != null || ua.match(/opr/) != null) {
      browserType = "opera";
    } else if (ua.match(/baidubrowser/) != null) {
      browserType = "baidu";
    } else if (ua.match(/metasr/) != null) {
      browserType = "sougou";
    } else if (
      ua.match(/tencenttraveler/) != null ||
      ua.match(/qqbrowse/) != null
    ) {
      browserType = "QQ";
    } else if (ua.match(/maxthon/) != null) {
      browserType = "maxthon";
    } else if (ua.match(/lbbrowser/) != null) {
      browserType = "liebao";
    } else if (ua.match(/2345explorer/) != null) {
      browserType = "2345";
    } else if (ua.match(/qihu 360ee/) != null) {
      browserType = "360";
    } else if (ua.match(/chrome/) != null) {
      browserType = "Chrome";
    } else if (ua.match(/safari/) != null) {
      browserType = "Safari";
    }
    return browserType;
  }
}

export async function checkLoginStatus(check_axios) {
  if (!check_axios) {
    throw new Error('check_axios is required')
  }
  if (window._loginPromise) {
    return window._loginPromise;
  }
  const currentLoginPromise = new Promise(async (resolve, reject) => {
    try {
      let res = await check_axios({ _notip: true });
      res = res.data || res
      const { status, result } = res
      if (status === "ok") {
        const { account_detail: d } = result;
        resolve({
          user_id: d.userid,
          nickname: d.username,
          avatar: d.avatar,
          avatar_decoration: d.avatar_decoration || {},
          chat_intro: result.profile?.chat_intro || "",
          count_change_nickname: result.profile?.count_change_nickname || 0,
          level: d.level_info.level,
        });
      } else {
        resolve(false);
      }
    } catch (_) {
      resolve(false);
    }
  });
  window._loginPromise = currentLoginPromise;
  return window._loginPromise;
}

export function copyToClipboard(text) {
  // 现代浏览器推荐方式
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text)
      .then(() => {
        console.log('复制成功')
      })
      .catch(err => {
        console.error('复制失败:', err);
      });
    return;
  }
}

export function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

export function compareVersions(version1, version2) {
  const arr1 = version1.split('.').map(Number);
  const arr2 = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(arr1.length, arr2.length); i++) {
    const num1 = arr1[i] || 0;
    const num2 = arr2[i] || 0;

    if (num1 < num2) {
      return -1; // version1 < version2
    } else if (num1 > num2) {
      return 1; // version1 > version2
    }
  }

  return 0; // version1 = version2
}
/**
 * 格式化游戏时长
 * @param {number} playtime - 游戏时长（分钟）
 * @returns {string} 格式化后的时长（小时，保留一位小数）
 */
export function formatPlaytime(playtime) {
  return (playtime / 60).toFixed(1);
}

/**
 * 格式化成就完成时间
 * @param {Number|String} achievedTime 成就完成时间戳（秒）或日期时间字符串（如 "2021-08-11 20:08"）
 * @param {Boolean} achieved 是否已完成
 * @returns {String} 格式化后的时间字符串
 */
export function formatAchievedTime(achievedTime, achieved) {
  if (!achieved || achievedTime === 0 || !achievedTime) {
    return '未完成';
  }

  let date;

  // 判断是时间戳还是日期字符串
  if (typeof achievedTime === 'string') {
    // 处理日期时间字符串格式，如 "2021-08-11 20:08"
    date = new Date(achievedTime.replace(' ', 'T')); // 转换为ISO格式
  } else {
    // 处理时间戳（秒）
    date = new Date(achievedTime * 1000);
  }

  if (isNaN(date.getTime())) {
    return '未完成';
  }

  const now = new Date();
  const diffTime = now - date;
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    const diffHours = Math.floor(diffTime / (1000 * 60 * 60));
    if (diffHours === 0) {
      const diffMinutes = Math.floor(diffTime / (1000 * 60));
      return diffMinutes <= 0 ? '刚刚' : `${diffMinutes}分钟前`;
    }
    return `${diffHours}小时前`;
  } else if (diffDays < 30) {
    return `${diffDays}天前`;
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'numeric',
      day: 'numeric'
    });
  }
}

export * from './cookie';
export * from './store';
export * from './img';
export * from './steam';
export * from './media';
export * from './axios/encrypt/compress-encrypt';
export { handleLogout } from './axios/logout';
export { axiosPostHandler, axiosGetHandler } from './axios/config';
export { encrypt }
