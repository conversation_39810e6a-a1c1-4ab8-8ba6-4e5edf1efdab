const { session, ipcMain } = require('electron')
const { log } = require('@heybox/electron-utils')
const utils = require('@heybox/electron-utils')
const store = utils.store
const default_domain = global.IS_PROD_URL ? '.xiaoheihe.cn' : '.debugmode.cn'
const default_cookie_obj = {
  url: BASE_API,
  sameSite: 'no_restriction',
  secure: true,
  path: "/",
  domain: default_domain,
}
const { USER_COOKIES, PKEY_ENCRYPT_KEY } = require('../assets/constant/cookie')
const ONE_YEAR_AFTER_TS = getOneYearAfterTs()
class Cookies_Listener {
  constructor() {
    session.defaultSession.cookies.on('changed', (_, cookie) => {
      if (USER_COOKIES.includes(cookie.name)) {
        this.getUserCookie()
      }
    })
    ipcMain.handle('set-cookie', this.setCookie)
  }
  init() {
    return new Promise((resolve, reject) => {
      if (main_config.keep_login_state) {
        let user_cookies = utils.getStoreCookies()
        if (user_cookies.length > 0) {
          let flag = 0
          user_cookies.forEach(cookie => {
            if (USER_COOKIES.includes(cookie.name)) {
              cookie = {
                ...cookie,
                ...default_cookie_obj,
                expirationDate: ONE_YEAR_AFTER_TS
              }
            }
            session.defaultSession.cookies.set(cookie).then(() => {}, (error) => {
              console.error(error)
            }).finally(() => {
              flag += 1
              if (flag === user_cookies.length) {
                session.defaultSession.cookies.flushStore().finally(() => {
                  resolve(1)
                })
              }
            })
          });
          resolve(1)
        } else {
          this.getUserCookie()
          resolve(1)
        }
      } else {
        resolve(1)
      }
    })
  
  
  }
  async getUserCookie(cb) {
    if (this.getUserCookieLock) return
    this.getUserCookieLock = true
    session.defaultSession.cookies.get({})
    .then((cookies) => {
      this.getUserCookieLock = false
      let user_cookies = []
      for (let cookie of cookies) {
        if (USER_COOKIES.includes(cookie.name) && cookie.domain.includes(default_domain)) {
          if (utils.isHeyboxId(cookie.name)) {
            setTimeout(() => store.set('heybox_id', cookie.value))
          } else if (utils.isPkey(cookie.name)) { // pkey 加密
            cookie.value = utils.encrypt(cookie.value, PKEY_ENCRYPT_KEY)
          }
          user_cookies.push({
            "name": cookie.name,
            "value": cookie.value,
            "hostOnly": cookie.hostOnly,
            "httpOnly": cookie.httpOnly,
            "session": cookie.session,
          })
        }
      }
      store.set('cookies', user_cookies)
      cb && cb()
    }).catch((error) => {
      log.info(error)
    })
  }
  setCookie(_, v) {
    return session.defaultSession.cookies.set({
      ...default_cookie_obj,
      ...v,
    }).then(() => {}).catch(r => {
      console.error(r)
    })
  }
}

function getOneYearAfterTs() {
  let d = new Date();
  d.setFullYear(d.getFullYear() + 1);  //加一年
  return d.getTime()
}

module.exports = new Cookies_Listener()