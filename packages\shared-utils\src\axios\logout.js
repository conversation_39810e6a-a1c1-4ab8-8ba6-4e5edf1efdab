import { logout } from '../api/login'
import { useEventBus } from '@heybox-app-web-shared/eventbus'
import { ipcService } from '@heybox-app-web-shared/ipc';
// import AccManager from '_js/acc-manager'

const { emit: eventBusEmit } = useEventBus()
const auto_reload_ts_key = 'hc_auto_reload_ts'

export function handleLogout() {
  return new Promise(async (resolve, reject) => {
    try {
      let auto_reload_ts =( await window.electronAPI.getStoreData(auto_reload_ts_key)) || []
      if (Array.isArray(auto_reload_ts) && auto_reload_ts.length >= 3) {
        let early_ts = auto_reload_ts.sort().shift(), ts = new Date().getTime()
        if (ts - early_ts > 10 * 1000) {
          resolve(logout().finally(() => {
            logoutAccount(auto_reload_ts)
          }))
        } else {
          window.electronAPI.setStoreData(auto_reload_ts_key, '')
        }
      } else {
        logout().finally(() => {
          logoutAccount(auto_reload_ts)
        })
      }
    } catch(e) {
      console.error(e);
      reject()
    }
  })
}

async function logoutAccount(auto_reload_ts) {
  ipcService.sendToMain('logout-acc')
  // await AccManager.logoutAcc()
  auto_reload_ts.push(new Date().getTime())
  window.electronAPI.setStoreData(auto_reload_ts_key, auto_reload_ts)
  window.electronAPI.clearStoreCookies()
  setTimeout(() => {
    window.electronAPI.setStoreData('', {}, 'account'); 
  }) 
  // 隐藏 library webview，让骨架屏显示
  window.webViewAPI.destroy('library');
  eventBusEmit('logout-complete', {
    isLogin: false
  });
}