<!DOCTYPE html>
<html lang="zh-cmn-Hans">
  <head>
    <meta charset="utf-8" />
    <meta
      httpequiv="Content-Type"
      content="text/html; charset=utf-8"
    />
    <meta
      httpequiv="Cache-Control"
      content="no-transform"
    />
    <meta
      http-equiv="X-UA-Compatible"
      content="IE=edge"
    />
    <meta
      name="renderer"
      content="webkit"
    />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title></title>
    <style>
      * {
        margin: 0;
        border: none;
        box-sizing: border-box;
        user-select: none;
      }

      html {
        width: 320px;
        height: 366px;
        margin: 0;
        overflow: hidden;
      }

      body {
        width: 100%;
        height: 100%;
      }

      img {
        pointer-events: none;
      }

      #app {
        width: 100%;
        height: 100%;
        padding-bottom: 20px;
        color: #fff;
        border-radius: 8px 8px 0 0;
        overflow: hidden;
        position: relative;
      }

      .notice-content {
        width: 100%;
        height: 100%;
        padding: 16px;
        animation: notice-show 150ms 500ms cubic-bezier(0, 0, 0.58, 1) forwards;
        opacity: 0;
      }

      .notice-container {
        width: 320px;
        height: 100%;
        animation: notice-in 500ms cubic-bezier(0, 0, 0.58, 1) forwards;
        background: #0c162d;
        border-radius: 8px;
        position: relative;
        overflow: hidden;
      }

      .notice-container.out {
        animation: notice-out 300ms cubic-bezier(0, 0, 0.58, 1) forwards;
      }

      .notice-container::after {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        border: 1px solid rgba(255, 255, 255, 0.08);
        pointer-events: none;
      }

      .green {
        width: 100%;
        height: 100%;
        background: var(--brand-text, #7dd95e);
        position: absolute;
        top: 0;
        right: 0;
        z-index: 1;
        animation: green-slider 500ms cubic-bezier(0, 0, 0.58, 1) forwards;
        border-radius: 8px;
      }

      .bg-img {
        width: 100%;
        height: 240px;
        position: absolute;
        left: 0;
        top: 0;
      }

      .bg-img img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .bg-img .cover {
        width: 320px;
        height: 126px;
        position: absolute;
        left: 0;
        bottom: 0;
      }

      .mini-pro-img {
        width: 60px;
        height: 60px;
        margin-bottom: 16px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.28);
        position: relative;
      }

      .mini-pro-img .mini-icon {
        width: 60px;
        height: 60px;
      }

      .mini-pro-img .chat-icon {
        width: 22px;
        height: 22px;
        border-radius: 4px;
        position: absolute;
        bottom: -2px;
        right: -4px;
      }

      .main-content {
        width: 100%;
        padding: 16px;
        position: absolute;
        bottom: 0;
        left: 0;
      }

      .main-content .title {
        color: #fff;
        font-family: 'Microsoft YaHei UI';
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
        /* 137.5% */
        letter-spacing: 0.16px;
        margin-bottom: 8px;
      }

      .main-content .desc {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        overflow: hidden;
        -webkit-line-clamp: 2;
        /* 限制在三行 */
        color: rgba(255, 255, 255, 0.72);
        font-size: 14px;
        line-height: 24px;
        /* 171.429% */
        letter-spacing: 0.56px;
        margin-bottom: 16px;
      }

      .main-content .btn {
        height: 42px;
        width: 100%;
        color: #36393e;
        font-size: 14px;
        line-height: 42px;
        font-weight: 700;
        letter-spacing: 0.14px;
        text-align: center;
        border-radius: 5px;
        background: #fff;
        cursor: pointer;
      }

      .title-bar {
        position: absolute;
        top: 8px;
        right: 8px;
        display: flex;
      }

      .title-bar .icon-btn {
        width: 24px;
        height: 24px;
        margin-right: 8px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
      }

      .title-bar .icon-btn:hover,
      .title-bar .icon-btn.open {
        border-radius: 13px;
        background: rgba(255, 255, 255, 0.08);
      }

      .title-bar .icon-btn svg {
        width: 14px;
        height: 14px;
      }

      .title-bar .icon-btn .menu {
        width: 188px;
        padding: 8px;
        border-radius: 8px;
        background: rgba(26, 28, 31, 0.99);
        box-shadow: 0px 3px 15px 0px rgba(0, 0, 0, 0.35);
        position: absolute;
        top: 28px;
        right: 0;
      }

      .title-bar .icon-btn .menu-item {
        height: 32px;
        width: 100%;
        padding: 6px 8px;
        overflow: hidden;
        color: #d2d3d7;
        text-overflow: ellipsis;
        font-size: 14px;
        font-style: normal;
        line-height: 18px;
        /* 128.571% */
        letter-spacing: 0.14px;
        cursor: pointer;
      }

      .title-bar .icon-btn .menu-item:hover {
        color: #fff;
        background: #2d7d46;
        border-radius: 5px;
      }

      @keyframes green-slider {
        0% {
          transform: translateY(366px);
        }

        100% {
          transform: translateY(-368px);
        }
      }

      @keyframes notice-in {
        0% {
          transform: translateY(366px);
        }

        100% {
          transform: translateY(0);
        }
      }

      @keyframes notice-show {
        0% {
          opacity: 0;
        }

        100% {
          opacity: 1;
        }
      }

      @keyframes notice-out {
        0% {
          transform: translateY(0);
        }

        100% {
          transform: translateY(366px);
        }
      }
    </style>
  </head>

  <body>
    <div id="app">
      <template v-if="show">
        <div
          class="notice-container"
          :class="{
            out: hide
          }"
        >
          <div
            class="notice-content"
            :style="{
          background: main_color
        }"
          >
            <div class="bg-img">
              <img
                class="bg-img"
                v-if="pic"
                :src="pic"
              />
              <div
                class="cover"
                :style="{
              background: `linear-gradient(180deg, transparent 0%, ${main_color} 49.82%, ${main_color} 100%)`
            }"
              ></div>
            </div>
            <div class="title-bar" @mouseenter="freezeeCloseTime" @mouseleave="continueCloseTime">
              <div
                class="icon-btn"
                :class="{open: showMenu}"
                @click="showMenu = !showMenu"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                >
                  <path
                    fill-rule="evenodd"
                    clip-rule="evenodd"
                    d="M2.91667 7.00033C2.91667 7.80574 2.26375 8.45866 1.45833 8.45866C0.652918 8.45866 0 7.80574 0 7.00033C0 6.19491 0.652918 5.54199 1.45833 5.54199C2.26375 5.54199 2.91667 6.19491 2.91667 7.00033ZM8.45833 7.00033C8.45833 7.80574 7.80541 8.45866 7 8.45866C6.19458 8.45866 5.54167 7.80574 5.54167 7.00033C5.54167 6.19491 6.19458 5.54199 7 5.54199C7.80541 5.54199 8.45833 6.19491 8.45833 7.00033ZM12.5417 8.45866C13.3471 8.45866 14 7.80574 14 7.00033C14 6.19491 13.3471 5.54199 12.5417 5.54199C11.7363 5.54199 11.0833 6.19491 11.0833 7.00033C11.0833 7.80574 11.7363 8.45866 12.5417 8.45866Z"
                    fill="white"
                    fill-opacity="0.5"
                  />
                </svg>
                <div
                  class="menu"
                  v-if="showMenu"
                >
                  <div
                    class="menu-item"
                    @click="neverShow"
                  >
                    不再提示
                  </div>
                </div>
              </div>
              <div
                class="icon-btn"
                @click="close"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 14 14"
                  fill="none"
                >
                  <path
                    d="M10.7198 2.01253C11.0699 1.66249 11.6374 1.66249 11.9875 2.01253C12.3375 2.36258 12.3375 2.93011 11.9875 3.28015L8.26762 7L11.9875 10.7198C12.3375 11.0699 12.3375 11.6374 11.9875 11.9875C11.6374 12.3375 11.0699 12.3375 10.7198 11.9875L7 8.26762L3.28015 11.9875C2.93011 12.3375 2.36258 12.3375 2.01253 11.9875C1.66249 11.6374 1.66249 11.0699 2.01253 10.7198L5.73238 7L2.01253 3.28015C1.66249 2.93011 1.66249 2.36258 2.01253 2.01253C2.36258 1.66249 2.93011 1.66249 3.28015 2.01253L7 5.73238L10.7198 2.01253Z"
                    fill="white"
                    fill-opacity="0.5"
                  />
                </svg>
              </div>
            </div>
            <div class="main-content">
              <div class="mini-pro-img" @mouseenter="freezeeCloseTime" @mouseleave="continueCloseTime">
                <img
                  class="mini-icon"
                  :src="icon"
                />
                <img
                  class="chat-icon"
                  src="data:image/png;base64,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"
                />
              </div>
              <div class="title" @mouseenter="freezeeCloseTime" @mouseleave="continueCloseTime">{{title}}</div>
              <div class="desc" @mouseenter="freezeeCloseTime" @mouseleave="continueCloseTime">{{desc}}</div>
              <div
                class="btn"
                @click="handler"
                @mouseenter="freezeeCloseTime"
                @mouseleave="continueCloseTime"
              >
                {{button_desc}}
              </div>
            </div>
          </div>
        </div>
        <div class="green"></div>
      </template>

    </div>

    <script src="../assets/js/vue.min.js"></script>

    <script>
      const CLOSE_TIME = 10660
      const app = new Vue({
        el: '#app',
        data() {
          return {
            title: '',
            desc: '',
            button_desc: '',
            icon: '',
            pic: '',
            main_color: '',
            mini_pro_id: '',
            protocol: '',

            hide: false,
            showMenu: false,
            closeTimer: null,
            startCloseTimerTs: null,
            spendTs: 0,
            show: false,
          }
        },
        created() {
          this.init()
        },
        beforeDestroyed() {},
        watch: {
          showMenu(v) {
            if (v) {
              window.addEventListener('click', this.hideMenu)
              this.freezeeCloseTime()
            } else {
              window.removeEventListener('click', this.hideMenu)
              this.continueCloseTime()
            }
          },
        },
        methods: {
          init() {
            let showFunc = () => {
              if (!this.show) {
                this.show = true
                this.start()
              }
            }
            window.electronAPI.onShow(showFunc)
            setTimeout(showFunc, 5000)

            let params = getParam()
            for (let k in params) {
              this[k] = params[k]
            }
          },
          start() {
            this.closeTimer = setTimeout(() => {
              this.close()
            }, CLOSE_TIME)
            this.startCloseTimerTs = new Date().getTime()
          },
          hideMenu() {
            this.showMenu = false
          },
          handler() {
            window.electronAPI?.click(this.protocol)
            this.close()
          },
          close() {
            this.hide = true
            setTimeout(() => {
              window.electronAPI?.hide()
            }, 300)
          },
          neverShow() {
            window.electronAPI?.neverShow({
              mini_program_id: this.mini_pro_id,
            })
            this.close()
          },
          continueCloseTime() {
            console.log('continueCloseTime')
            if (this.closeTimer) return
            this.closeTimer = setTimeout(() => {
              this.close()
            }, CLOSE_TIME - this.spendTs)
          },
          freezeeCloseTime() {
            console.log('freezeeCloseTime')
            if (!this.closeTimer) return
            clearTimeout(this.closeTimer)
            this.closeTimer = null
            let now = new Date().getTime()
            this.spendTs += now - this.startCloseTimerTs
            this.startCloseTimerTs = now
          },
        },
      })
      function getParam() {
        var url = window.location.href
        if (url.indexOf('?') < 0) {
          return {}
        }
        return url.match(/([^?=&]+)(=([^&]*))/g).reduce(function (a, v) {
          return (a[v.slice(0, v.indexOf('='))] = decodeURIComponent(v.slice(v.indexOf('=') + 1))), a
        }, {})
      }
    </script>
  </body>
</html>
