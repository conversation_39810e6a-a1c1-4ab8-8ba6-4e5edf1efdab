import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import cdn from '../../scripts/build/vite-plugin-cdn-import'

export default defineConfig({
  plugins: [
    vue(),
    cdn({
      prodUrl: '{path}', // 使用完整路径而不是 CDN URL
      enableInDevMode: false, // 开发模式下不使用 CDN
      modules: [
        {
          name: 'vue',
          var: 'Vue',
          path: '../../weblibs/vue.global.prod.js',
        }
      ]
    })
  ],
  css: {
    preprocessorOptions: {
      scss: {
        additionalData(context, loaderContext) {
          let result = '';
          result += '@use "../index.scss" as *;';
          result += '@use "@heybox-webapp/hb-theme" as *;';
          return result + context;
        }
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  build: {
    lib: {
      entry: resolve(__dirname, 'src/index.js'),
      name: 'HeyboxSharedComponents',
      fileName: (format) => `index.${format === 'es' ? 'esm' : format}.js`
    },
    minify: 'terser',
    emptyOutDir: true,
    cssCodeSplit: true, // 禁用 CSS 代码分割，将所有 CSS 打包到一个文件中
    rollupOptions: {
      external: [
        'vue',
      ],
      output: [
        // 标准输出到dist目录（用于npm发布）
        {
          format: 'es',
          dir: 'dist',
          entryFileNames: 'index.js',
          globals: { vue: 'Vue' },
        },
      ]
    }
  }
}) 