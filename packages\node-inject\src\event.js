const EVENTS = {}
const COMMON_PID = '*'

function eventDispatcher(event, payload) {
  let events = EVENTS[event], { pid, windowId } = payload
  if (!windowId && payload.message) {
    try {
      windowId = JSON.parse(payload.message).windowId
    } catch (e) {
      console.error(e)
    }
  }
  if (events) {
    if (!pid) {
      for (let windows of Object.values(events)) {
        for (let cb of Object.values(windows)) {
          cb && cb(payload)
        }
      }
    } else if (!windowId) {
      let windows = {
        ...events[pid] || {},
        ...events[COMMON_PID] || {},
      }
      for (let cb of Object.values(windows)) {
        cb && cb(payload)
      }
    } else {
      let cb = events[pid]?.[windowId]
      cb && cb(payload)
      let common_pid_cb = events[COMMON_PID]?.[windowId]
      common_pid_cb && common_pid_cb(payload)
    }
  }
}

const EVENT_MANAGER = {
  COMMON_PID,
  COOMON_PID: COMMON_PID, // 兼容旧代码拼写错误
  register (pid, windowId, listenEvents) {
    for (const [event, callback] of Object.entries(listenEvents)) {
      if (!EVENTS[event]) {
        // 此event未注册过，需要先在addon中注册事件
        EVENTS[event] = {}
        const { registerEvent } = require("./addon");
        registerEvent({
          [event]: (payload) => {
            eventDispatcher(event, payload)
          }
        })
      }

      if (!EVENTS[event][pid]) {
        EVENTS[event][pid] = {}
      }
      EVENTS[event][pid][windowId] = callback
    }
  },
  unregister (pid, windowId, event) {
    if (event) {
      if (EVENTS[event][pid]) {
        if (windowId) {
          delete EVENTS[event][pid][windowId]
        } else {
          delete EVENTS[event][pid]
        }
      }
    } else {
      for (let event_name in EVENTS) {
        if (EVENTS[event_name][pid]) {
          if (windowId) {
            delete EVENTS[event_name][pid][windowId]
          } else {
            delete EVENTS[event_name][pid]
          }
        }
      }
    }

  }
}

module.exports = EVENT_MANAGER