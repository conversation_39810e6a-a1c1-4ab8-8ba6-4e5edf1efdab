const { Worker } = require('worker_threads')
const path = require('path')

function runAccelerate(type, params) {
  return new Promise((resolve, reject) => {
    console.log(type, params);
    
    // 创建 Worker 来处理耗时的 StartAcc 调用
    console.log('worker path', path.join(__dirname, 'acc.worker.js'))
    const worker = new Worker(path.join(__dirname, 'acc.worker.js'), {
      workerData: { type, params }
    });
    
    const timeout = setTimeout(() => {
      worker.terminate();
      reject(new Error('StartAcc operation timed out'));
    }, 10000); // 10秒超时
    
    worker.on('message', (message) => {
      if (message.type === 'workerReady') {
        // Worker 已准备就绪，发送开始命令
        worker.postMessage({
          type: type,
          params: params
        });
      } else if (message.type === 'startAccResult') {
        clearTimeout(timeout);
        
        if (message.status === 'ok') {
          console.log('startRes', message.result);
          resolve(message.result);
        } else {
          resolve({
            status: 'failed',
            msg: message.error
          })
        }
        
        // 清理 Worker
        worker.terminate();
      } else if (message.type === 'initAccResult') {
        clearTimeout(timeout);
        if (message.status === 'ok') {
          console.log('initRes', message.result);
          resolve({
            status: 'ok',
            result: message.result
          });
        } else {
          resolve({
            status: 'failed',
            result: message.error,
            msg: message.error
          })
        }
        worker.terminate();
      }
    });
    
    worker.on('error', (error) => {
      clearTimeout(timeout);
      console.error('Worker error:', error);
      resolve({
        status: 'failed',
        msg: error
      })
      worker.terminate();
    });
    
    worker.on('exit', (code) => {
      if (code !== 0) {
        clearTimeout(timeout);
        resolve({
          status: 'failed',
          msg: `Worker stopped with exit code ${code}`
        })
      }
    });
  })
}

module.exports = { runAccelerate }
