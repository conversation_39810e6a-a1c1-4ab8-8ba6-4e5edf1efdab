import { getSMToken } from '../../api/sm.js'
// import { getCookie } from '../../common/utils'
var BASE_DOMAIN = 'xiaoheihe.cn'
// var BASE_DOMAIN = 'debugmode.cn'

function init () {
  window._smConf = {
    organization: '0yD85BjYvGFAvHaSQ1mc', //必填，组织标识，邮件中organization项
    appId: 'heybox_website', //必填，应用标识，默认传值default，其他应用标识提前联系数美协助定义
    publicKey: 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCXj9exmI4nQjmT52iwr+yf7hAQ06bfSZHTAHUfRBYiagCf/whhd8es0R79wBigpiHLd28TKA8b8mGR8OiiI1hV+qfynCWihvp3mdj8MiiH6SU3lhro2hkfYzImZB0RmWr2zE4Xt1+A6Oyp6bf+W7JSxYUXHw3nNv7Td4jw4jEFKQIDAQAB', //必填，私钥标识，邮件中publicKey项
    staticHost: 'static.portal101.cn',
    protocol: 'https',
  };
  let resolve = () => {}
  let promise = new Promise((_resolve) => {
    resolve = _resolve
  })

  window._smReadyFuncs = [() => {
    resolve(window.SMSdk)
  }];
  
  window.SMSdk = {}
  injectSMScript();
  return promise
}

// 获取数美脚本src
function getSMSrc () {
  var originHost = "static.fengkongcloud.com";
  var protocol = 'https://'; // 客户端中数美请求使用https, 防止劫持
  var fpJsPath = '/dist/web/v3.0.0/fp.min.js';
  var jsTimer = '?t=' + (new Date().getTime() / (6 * 3600 * 1000)).toFixed(0)
  var url = protocol + _smConf.staticHost + fpJsPath + jsTimer;
  var ua = navigator.userAgent.toLowerCase();
  var isWinXP = /windows\s(?:nt\s5.1)|(?:xp)/.test(ua);
  var isLowIE = /msie\s[678]\.0/.test(ua);
  if (isWinXP && isLowIE) {
    url = protocol + originHost + fpJsPath + jsTimer;
  }
  return url;
}

// 注入脚本
function injectSMScript () {
  var url = getSMSrc()
  var sm = document.createElement("script");
  var s = document.getElementsByTagName("script")[0];
  sm.src = url;
  s.parentNode.insertBefore(sm, s);
}

async function getXHHToken(data) {
  try {
    const res = await getSMToken({}, {
      box_data: data
    })
    const { status, result, msg } = res.data || res
    if (status != 'ok') return msg
    return result.heybox_token
  } catch (err) {
    throw new Error(err)
  }
}

async function setXHHTokenIdToCookie(xhh_token_id) {
  if (!xhh_token_id) {
    throw new Error("xhh_token_id 不能为空")
  }
  if (typeof xhh_token_id !== 'string') {
    throw new TypeError("xhh_token_id 应该为string")
  }
  var now = new Date()
  now.setTime(now.getTime() + 14 * 24 * 60 * 60 * 1000)
  
  let cookie = `x_xhh_tokenid=${escape(xhh_token_id)};expires=${now.toGMTString()};path=/;domain=${BASE_DOMAIN}`
  
  document.cookie = cookie
  return xhh_token_id
}

async function setXHHTokenID(sdk) {
  let data = sdk.getDeviceId()
  let xhh_token_id = ""
  console.log('data', data)
  if (data.startsWith("B") && data.length === 89) {
    xhh_token_id = data
  }
  if (data.startsWith("D")) {
    xhh_token_id = await getXHHToken(data)
  }
  if (!xhh_token_id) {
    throw new Error("tokenid创建失败")
  }
  
  return setXHHTokenIdToCookie(xhh_token_id)
}

// 暴露获取ID的函数
function injectGlobalFunc(SMSdk) {
  window.dealSmDeviceId = setXHHTokenID.bind(this, SMSdk)
}

async function requestSMId() {
  if (window.electronAPI && window.electronAPI.requestSMId) {
    window.electronAPI.requestSMId(async () => {
      window.electronAPI.sendSMId(await getSmDeviceId())
    })
  }
}

async function getSmDeviceId () {
  return await window.dealSmDeviceId()
}

try {
  const SMSdk = await init()
  await setXHHTokenID(SMSdk)
  injectGlobalFunc(SMSdk)
  requestSMId()
} catch (err) {
  console.log(err);
}
