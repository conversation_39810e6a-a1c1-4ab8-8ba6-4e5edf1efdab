const parser = require("@babel/parser");
const isNode = (t) => t && t.constructor.name === "Node";
const isFunction = (node) => {
  return [
    "ArrowFunctionExpression",
    "FunctionDeclaration",
    "FunctionExpression",
    "ClassDeclaration",
    "ClassMethod",
  ].includes(node.type);
};
function walkNode(roots, codeStr) {
  const ret = [];
  const todo = roots.filter((item) => isNode(item));
  let node;
  while ((node = todo.shift())) {
    if (isFunction(node)) {
      if (
        node.body.type === "BlockStatement" ||
        node.body.type === "ClassBody"
      ) {
        ret.push([node.start, codeStr.slice(node.start, node.body.start + 1)]);
        ret.push([
          node.body.end - 1,
          codeStr.slice(node.body.end - 1, node.body.end),
        ]);
      } else {
        ret.push([node.start, codeStr.slice(node.start, node.body.start)]);
      }
    }
    Object.keys(node).forEach((k) => {
      const item = node[k];
      if (item && item.forEach) {
        item.forEach((t) => isNode(t) && todo.push(t));
      } else if (isNode(item)) {
        todo.push(item);
      }
    });
  }
  return ret;
}
function padString(len) {
  return len <= 0 ? "" : " ".repeat(len);
}
function findLineBreaks(codeStr) {
  const lineBreaks = [];
  let lastBr = -1;
  while (true) {
    lastBr = codeStr.indexOf("\n", lastBr + 1);
    if (lastBr === -1) {
      return lineBreaks;
    }
    lineBreaks.push([lastBr, "\n"]);
  }
}
function generateSource(tokens, targetLength) {
  const ret = [];
  let len = 0;
  tokens.forEach(([start, content]) => {
    if (len < start) {
      ret.push(padString(start - len));
      len = start;
    }
    ret.push(content);
    len += content.length;
  });
  if (len < targetLength) {
    ret.push(padString(targetLength - len));
  }
  return ret.join("");
}
function getByteSource(codeStr) {
  const ast = parser.parse(codeStr);
  const tokens = walkNode(ast.program.body, codeStr)
    .concat(findLineBreaks(codeStr))
    .sort((a, b) => a[0] - b[0]);
  return generateSource(tokens, codeStr.length);
}
module.exports = getByteSource;
