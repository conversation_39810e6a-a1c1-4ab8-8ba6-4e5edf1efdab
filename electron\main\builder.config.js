const env = require('./env.js')
const packageJson = require('./package.json')
const path = require('path')
const fs = require('fs')
const { exec } = require('child_process')
const buildResources = require('@heybox/electron-build-resources')

const outputDir = `out/HeyboxApp_${env.ELECTRON_ENV || 'prod'}_${env.NODE_BIT}_${packageJson.version}`

module.exports = {
  removePackageScripts: true,
  removePackageKeywords: true,
  appId: "com.heybox.app",
  productName: "HeyboxApp",
  asar: false,
  win: {
    target: ["dir"],
    icon: buildResources.icon,
    signAndEditExecutable: true,
  },
  files: [
    "!builder.config.js",
    "!out",
    "!build",
    "!src",
    "!src_"
  ],
  extraResources: [
    {
      from: "src/utils/installProcess",
      to: "utils/installProcess"
    }
  ],
  directories: {
    output: outputDir
  },
  beforeBuild: async () => {
    // 在开始构建前清理输出目录
    const outputPath = path.join(process.cwd(), outputDir)
    if (fs.existsSync(outputPath)) {
      console.log('Cleaning output directory:', outputPath)
      fs.rmSync(outputPath, { recursive: true, force: true })
    }
    return true
  },
  afterAllArtifactBuild: async () => {
    // if (process.env.npm_config_open_folder == 0) return

    // // 构建完成后打开输出文件夹
    // const outputPath = path.join(process.cwd(), outputDir)
    // console.log('Opening output directory:', outputPath)
    
    // // 使用系统命令打开文件夹
    // const command = process.platform === 'win32' ? 'explorer' : 'open'
    // exec(`${command} "${outputPath}"`, (error) => {})
  }
} 