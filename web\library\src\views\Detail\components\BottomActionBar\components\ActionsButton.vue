<template>
  <div
    class="cpt-actions-button"
    :class="[
      theme,
      options ? 'has-options' : '',
      { 'icon-only': !showText },
      { 'two-line': layoutMode === 'medium' }
    ]"
    @click="handleClickButton"
  >
    <div class="actions-button-main">
      <div v-if="theme === 'success'" class="loading-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
          <circle cx="8" cy="8" r="7" stroke="white" stroke-opacity="0.2" stroke-width="2"/>
          <path d="M0.99974 8C0.447596 8 -0.00644427 8.44933 0.0623785 8.99717C0.216761 10.2261 0.655078 11.4072 1.34824 12.4446C2.22729 13.7602 3.47672 14.7855 4.93853 15.391C6.40034 15.9965 8.00887 16.155 9.56072 15.8463C11.1126 15.5376 12.538 14.7757 13.6569 13.6569C14.7757 12.538 15.5376 11.1126 15.8463 9.56072C16.155 8.00887 15.9965 6.40034 15.391 4.93853C14.7855 3.47672 13.7602 2.22729 12.4446 1.34824C11.4072 0.655082 10.2261 0.216766 8.99717 0.062383C8.44933 -0.00643987 8 0.447601 8 0.999744C8 1.55189 8.45064 1.99101 8.99514 2.08258C9.82781 2.22261 10.6254 2.53749 11.3337 3.01076C12.3205 3.6701 13.0896 4.60725 13.5437 5.7037C13.9979 6.80015 14.1167 8.00666 13.8852 9.17064C13.6537 10.3346 13.0822 11.4038 12.243 12.243C11.4038 13.0822 10.3346 13.6537 9.17064 13.8852C8.00665 14.1167 6.80015 13.9979 5.7037 13.5437C4.60725 13.0896 3.6701 12.3205 3.01075 11.3337C2.53749 10.6254 2.22261 9.82781 2.08258 8.99514C1.991 8.45064 1.55188 8 0.99974 8Z" fill="white">
            <animateTransform
              attributeName="transform"
              attributeType="XML"
              type="rotate"
              from="0 8 8"
              to="360 8 8"
              dur="1s"
              repeatCount="indefinite"/>
          </path>
        </svg>
      </div>
      <slot v-else name="icon"></slot>
      <div v-if="layoutMode === 'medium'" class="button-content-vertical">
        <div
          v-if="showText && text"
          class="actions-button-text"
        >
          {{ text }}
          <span v-if="speed" class="speed-text"> ({{ speed }})</span>
        </div>
        <slot name="desc"></slot>
      </div>
      <template v-else>
        <div
          v-if="showText && text"
          class="actions-button-text"
        >
          {{ text }}
          <span v-if="speed" class="speed-text"> ({{ speed }})</span>
        </div>
        <slot name="desc"></slot>
      </template>
    </div>
    <template v-if="options">
      <div class="actions-split-line"></div>
      <div
        class="actions-button-arrow"
        :class="{ 'rotated': isPopoverVisible }"
        ref="arrowTriggerRef"
        @click.stop="handleClickOptions"
      >
        <svg
          width="18"
          height="18"
          viewBox="0 0 18 18"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M14.0303 6.96967C14.3232 7.26256 14.3232 7.73744 14.0303 8.03033L9.53033 12.5303C9.23744 12.8232 8.76256 12.8232 8.46967 12.5303L3.96967 8.03033C3.67678 7.73744 3.67678 7.26256 3.96967 6.96967C4.26256 6.67678 4.73744 6.67678 5.03033 6.96967L9 10.9393L12.9697 6.96967C13.2626 6.67678 13.7374 6.67678 14.0303 6.96967Z"
            fill="#8C9196"
          />
        </svg>
      </div>
    </template>

    <!-- Account Switch Popover -->
    <AccountSwitchPopover
      v-if="options && options.includes('account-switch')"
      :triggerRef="arrowTriggerRef"
      :accounts="accounts"
      @selectAccount="handleSelectAccount"
      @updateShow="handlePopoverShow"
      @addLocalFile="handleAddLocalFile"
    />
  </div>
</template>

<script setup name="ActionsButton">
import { defineProps, defineEmits, ref } from 'vue';
import AccountSwitchPopover from './AccountSwitchPopover.vue';

const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  showText: {
    type: Boolean,
    default: true,
  },
  layoutMode: {
    type: String,
    default: 'wide',
  },
  width: {
    type: Number,
    default: 227,
  },
  theme: {
    type: String,
    default: '',
  },
  options: {
    type: Array,
  },
  speed: {
    type: String,
    default: '',
  },
  accounts: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['clickButton', 'clickOptions', 'selectAccount', 'selectOption', 'addLocalFile']);

const arrowTriggerRef = ref(null);
const isPopoverVisible = ref(false);

const handleClickButton = () => {
  emit('clickButton');
};

const handleClickOptions = () => {
  emit('clickOptions');
};

const handleSelectAccount = (account) => {
  emit('selectAccount', account);
};

const handlePopoverShow = (show) => {
  isPopoverVisible.value = show;
};

const handleAddLocalFile = (file) => {
  emit('addLocalFile', file);
};
</script>

<style lang="scss">
.cpt-actions-button {
  border-radius: 8px;
  background: var(---general-color-bg-1, #f3f4f5);
  cursor: pointer;

  .actions-button-main {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0px 16px;
    height: 46px;

    gap: 6px;

    .loading-icon {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 18px;
      height: 18px;
    }
  }

  &.has-options {
    display: flex;
    align-items: center;
    .actions-button-main {
      flex: 1;
    }
  }

  // 只显示图标的模式
  &.icon-only {
    .actions-button-main {
      padding: 0px 12px;
    }
  }

  // 两行显示模式 (medium 布局)
  &.two-line {
    .actions-button-main {
      padding: 0 16px;

      .button-content-vertical {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;

        .actions-button-text {
          line-height: normal;
          font-size: 14px;
          font-weight: 500;
        }

        .button-desc {
          white-space: normal;
          font-size: 10px;
          line-height: normal;
        }
      }
    }
  }

  &.primary {
    background: var(
      ---,
      linear-gradient(
        46deg,
        var(---greadient-color-primary-left, #464b50) -0.9%,
        var(---greadient-color-primary-right, #14191e) 100.9%
      )
    );

    .actions-button-text {
      color: var(--const-white-0, #fff);
    }

    .actions-split-line {
      background: var(
        --white-gamerecord-color-white-10a,
        rgba(255, 255, 255, 0.1)
      );
    }
  }

  &.success {
    background: var(---general-color-success, #32B846);

    .actions-button-text {
      color: var(--const-white-0, #fff);
    }

    .actions-split-line {
      background: rgba(255, 255, 255, 0.1);
    }
  }

  &.info {
    background: var(---general-color-info, #006EF4);

    .actions-button-text {
      color: var(--const-white-0, #fff);
    }

    .actions-split-line {
      background: rgba(255, 255, 255, 0.1);
    }
  }

  &.steam {
    background: var(--Steam, linear-gradient(90deg, #253F78 0%, #1B73A5 100%));

    .actions-button-text {
      color: var(--const-white-0, #fff);
    }

    .actions-split-line {
      background: rgba(255, 255, 255, 0.1);
    }

    .actions-button-arrow {
      svg {
        path {
          fill: #FFF;
        }
      }
    }
  }
  .actions-button-text {
    color: 1111111;
    font-size: 16px;
    font-weight: 500;
    line-height: 22px;
    white-space: nowrap;

    .speed-text {
      color: rgba(255, 255, 255, 0.50);
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
    }
  }

  .actions-split-line {
    width: 1px;
    align-self: stretch;
    background: var(---general-color-stroke-0, #dadde0);
  }

  .actions-button-arrow {
    width: 34px;
    height: 46px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;

    svg {
      path {
        fill: #FFF;
      }
    }

    &.rotated {
      transform: rotate(180deg);
    }
  }
}
</style>
