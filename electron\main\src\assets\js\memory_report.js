const { app, ipcMain } = require("electron")
const { TAB_DEVTOOL_NAME, MEMORY_REPORT_INTERVAL } = require('../constant/constant')
const { log } = require('@heybox/electron-utils')
const utils = require('@heybox/electron-utils')

const USER_OP_MAP = {
  'load_webview': '0',
  'destroy_webview': '1',
}

let reportTimer = null

// 判断内存上报数据是否是脏数据，目前当存在devTool的内存数据时认为是脏数据
// 存在脏数据时不进行内存上报,防止数据污染
function isDirtyUsage(usage) {
  return !!usage[TAB_DEVTOOL_NAME]
}

const initMemoryReport = () => {
  global.sendMemeryReport = (type) => {
    try {
      const data = utils.getMemoryUsage()
      if (isDirtyUsage(data)) return

      clearTimeout(reportTimer)

      reportTimer = setTimeout(() => {
        sendMemeryReport('reportInterval')
      }, MEMORY_REPORT_INTERVAL)

      log.info('[memory_report]', type, data)
    } catch (e) {
      console.log('e', e)
      throw e
    }
    
  }
  
  try {
    // memory_report_config = await getMemoryReportConfig()
    reportTimer = setTimeout(() => {
      sendMemeryReport()
    }, MEMORY_REPORT_INTERVAL)
  } catch (e) {
    // 无网络连接时会导致报错，无需处理
    console.log('e', e)
  }

}

module.exports = initMemoryReport