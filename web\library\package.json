{"name": "@heybox-app-web/library", "description": "Heybox app library demo", "main": "dist/index.html", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heybox-app-web-shared/components": "workspace:*", "@heybox-app-web-shared/eventbus": "workspace:*", "@heybox-app-web-shared/font": "workspace:*", "@heybox-app-web-shared/ipc": "workspace:*", "@heybox-app-web-shared/utils": "workspace:*", "@heybox-webapp/hb-theme": "^0.0.3", "@heybox/hb-sm": "^1.0.5", "axios": "^1.9.0", "crypto-js": "^4.2.0", "js-md5": "^0.8.3", "mitt": "^3.0.1", "pako": "^2.1.0", "qs": "^6.14.0", "vue": "^3.3.4", "vue-router": "^4.2.2", "vue-toastification": "2.0.0-rc.5", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "sass": "^1.63.6", "vite": "^4.4.0", "vite-plugin-cdn-import": "^1.0.1"}}