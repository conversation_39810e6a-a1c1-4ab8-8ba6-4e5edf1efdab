const { xx } = require('./variable')
const struct = require('python-struct')
const createHmac = require('create-hmac')


function sum (arr) {
  return arr.reduce(function(p, c, arr){
    return p + c
  })
}
function gencode (url, time) {
  let path = `/${url.split('/').filter(_=>_).join('/')}/`
  let code = ''
  let char = 'BCDFGHJKMNPQRTVWXY23456789'
  let byte_time = struct.pack('>Q', time)
  let digest = createHmac('sha1', btoa(path)).update(byte_time).digest()
  let start = digest.slice(19, 20)[0] & 0xF
  let codeint = struct.unpack('>I', digest.slice(start, start+4))[0] & 0x7fffffff
  for (let i=0; i<5; i++) {
    let div = ~~(codeint / char.length)
    let mod = codeint % char.length
    codeint = div
    code += char[mod]
  }
  let sign = `${sum(xx(code.slice(-4).split('').map(_ => _.charCodeAt()))) % 100}`
  if (sign.length < 2) {
    sign = 0 + sign
  }
  sign = code + sign
  return sign
}
const G = {
  a (url, time) {
    return gencode(url, time-1)
  },
  b (url, time) {
    return gencode(url, time-2)
  },
  c (url, time) {
    return gencode(url, time-3)
  },
  d (url, time) {
    return gencode(url, time-4)
  },
  e (url, time) {
    return gencode(url, time-5)
  },
  f (url, time) {
    return gencode(url, time)
  },
  g (url, time) {
    return gencode(url, time+1)
  },
  h (url, time) {
    return gencode(url, time+2)
  },
  i (url, time) {
    return gencode(url, time+3)
  },
  j (url, time) {
    return gencode(url, time+4)
  },
  k (url, time) {
    return gencode(url, time+5)
  },
  l (url, time) {
    return gencode(`/${url}/`, time-1)
  },
  m (url, time) {
    return gencode(`/${url}/`, time-2)
  },
  w () {
    return new Date()
  },
  n (url, time) {
    return gencode(`/${url}/`, time-3)
  },
  o (url, time) {
    return gencode(`/${url}/`, time-4)
  },
  p (url, time) {
    return gencode(`/${url}/`, time-5)
  },
  q (url, time) {
    return gencode(`/${url}/`, time)
  },
  r (url, time) {
    return gencode(`/${url}/`, time+1)
  },
  s (url, time) {
    return gencode(`/${url}/`, time+2)
  },
  t (url, time) {
    return gencode(`/${url}/`, time+3)
  },
  u (url, time) {
    return gencode(`/${url}/`, time+4)
  },
  v (url, time) {
    return gencode(`/${url}/`, time+5)
  }
}
export { G }