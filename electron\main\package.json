{"name": "@heybox-app/electron", "version": "1.0.0", "main": "index.js", "scripts": {"dev": "node build/set_env.js dev && electron .", "build": "node build/set_env.js build && electron-builder --config builder.config.js", "compiler": "node build/changeEntry.js build/compiler.js && electron . && node build/changeEntry.js", "upload:sentry": "node sentry/task.js"}, "keywords": [], "author": "heybox", "license": "MIT", "dependencies": {"@heybox/acc-sdk": "workspace:*", "@heybox/electron-build-resources": "workspace:*", "@heybox/electron-utils": "workspace:*", "@heybox/ingame-notice": "workspace:*", "@heybox/km-event": "workspace:*", "@heybox/mini-program": "workspace:*", "@heybox/node-inject": "workspace:*", "@heybox/process-iterate": "workspace:*", "@sentry/electron": "^4.24.0", "@sentry/integrations": "^7.73.0", "classic-level": "^1.4.1", "electron-log": "^4.4.8", "electron-store": "^8.1.0", "js-base64": "^3.7.7", "js-md5": "^0.8.3", "socks": "^2.8.5", "socks-proxy-agent": "^7.0.0", "ws": "^8.18.3"}, "devDependencies": {"@babel/parser": "^7.27.2", "@heybox-app-web-shared/components": "workspace:*", "@heybox-app-web-shared/eventbus": "workspace:*", "@heybox-app-web-shared/ipc": "workspace:*", "@heybox-app-web-shared/utils": "workspace:*", "@heybox-app-web/library": "workspace:*", "@heybox-app-web/main": "workspace:*", "@heybox-app-web/popup": "workspace:*", "electron": "33.2.0", "electron-builder": "26.0.16", "terser": "^5.39.2"}}