<template>
  <div class="cpt-check-more" @click="handleClick">
    {{ text
    }}<svg
      width="8"
      height="8"
      viewBox="0 0 8 8"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      :class="{ 'rotated': isExpanded }"
    >
      <g clip-path="url(#clip0_352_25722)">
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M0.667317 2.33325L7.33398 2.33325L4.00065 6.33325L0.667317 2.33325Z"
          fill="#8C9196"
        />
      </g>
      <defs>
        <clipPath id="clip0_352_25722">
          <rect
            width="8"
            height="8"
            fill="white"
          />
        </clipPath>
      </defs>
    </svg>
  </div>
</template>

<script setup name="CheckMore">
import { computed } from 'vue';

const props = defineProps({
  text: {
    type: String,
    default: '查看全部',
  },
  collapsedText: {
    type: String,
    default: '收起',
  },
  isExpanded: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(['toggle']);

const text = computed(() => {
  return props.isExpanded ? props.collapsedText : props.text;
});

const handleClick = () => {
  emit('toggle');
};
</script>

<style lang="scss">
.cpt-check-more {
  display: flex;
  height: 40px;
  font-size: 12px;
  line-height: 16px;
  justify-content: center;
  align-items: center;
  gap: 4px;
  align-self: stretch;
  border-radius: 5px;
  border-top: 1px solid var(---general-color-stroke-2, #f3f4f5);
  color: $general-color-text-3;
  cursor: pointer;

  svg {
    transition: transform 0.3s ease;

    &.rotated {
      transform: rotate(180deg);
    }
  }
}
</style>
