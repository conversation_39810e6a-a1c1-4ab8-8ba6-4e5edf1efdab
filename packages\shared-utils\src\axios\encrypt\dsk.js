import AES from 'crypto-js/aes';
import UTF8 from 'crypto-js/enc-utf8';
import CryptoJS from 'crypto-js';
import { mode } from 'crypto-js';
import getRSA from './jsencrypt'
import pako from 'pako';

function getBaseString() {
  const base = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!"#$%&\'()*+,-./:;<=>?@[\\]^_`{|}~';
  let temp = '';
  while (temp.length < 16) {
    temp += base[Math.floor(base.length * Math.random())];
  }
  return temp;
}

function getAESKey (data, key) {
  let iv = UTF8.parse('abcdefghijklmnop')
  let temp = AES.encrypt(UTF8.parse(data), UTF8.parse(key),{iv: iv, mode: mode.CBC,}).toString()
  return temp
}

/**
 * 进行AES对称加密
 */
export function getAESKeyUTF8(data, key) {
  const wordArray = CryptoJS.lib.WordArray.create(data);
  let iv = CryptoJS.enc.Utf8.parse('abcdefghijklmnop');
  let temp = CryptoJS.AES.encrypt(wordArray, CryptoJS.enc.Utf8.parse(key), {
    iv: iv,
    mode: CryptoJS.mode.CBC,
  }).toString();
  return temp;
}

// export default function (data) {
//   let key = getBaseString(), sid, time_ = ~~(+(new Date().getTime()) / 1000)
//   data = getAESKeyUTF8(pako.gzip(new TextEncoder().encode(JSON.stringify(data))), key)
//   key = getRSA(key)
//   sid = md5(key + time_) + md5(data)
//   return {sid, key, data, time: time_}
// }

export default function (data, confused) {
  let key = getBaseString(),
    sid,
    time_ = ~~(+new Date().getTime() / 1000);
  data = getAESKeyUTF8(pako.gzip(new TextEncoder().encode(JSON.stringify(data))), key);
  key = getRSA(key);
  sid = confused
    ? CryptoJS.MD5(data + time_).toString() + CryptoJS.MD5(key).toString()
    : CryptoJS.MD5(key + time_).toString() + CryptoJS.MD5(data).toString();
  return { sid, key, data, time: time_ };
}