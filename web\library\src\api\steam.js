import {extractUaVal<PERSON>, axiosGet<PERSON>and<PERSON>, axiosPostHand<PERSON>} from '@heybox-app-web-shared/utils'
const {BASE_XHH} = extractUaValues()

export function reportMyLibrary (args, formdata) {
	let url = BASE_XHH + '/heyboxpc/library/report_my_library'
	return axiosPostHandler(url, args, formdata)
}

export function reportMyLibraryV2 (args, formdata) {
	let url = BASE_XHH + '/heyboxpc/library/report_my_library_v2'
	return axiosPostHandler(url, args, formdata)
}

export function getMyAppList (args, formdata) {
	let url = BASE_XHH + '/heyboxpc/library/my_app_list'
	return axiosPostHandler(url, args, formdata)
}

export function searchAppList (args, formdata) {
	let url = BASE_XHH + '/heyboxpc/library/search'
	return axiosPostHandler(url, args, formdata)
}

export function getMyAccountList (args, formdata) {
	let url = BASE_XHH + '/heyboxpc/library/my_account_list'
	return axiosPostHandler(url, args, formdata)
}

export function getListFilterOptions (args, formdata) {
	let url = BASE_XHH + '/heyboxpc/library/list_filter_options'
	return axiosPostHandler(url, args, formdata)
}

export function getMyAppDetail (args, formdata) {
	let url = BASE_XHH + '/heyboxpc/app/my_app_detail'
	return axiosPostHandler(url, args, formdata)
}

export function getSteamStatusInfo (url) {
	return axiosGetHandler(url)
}