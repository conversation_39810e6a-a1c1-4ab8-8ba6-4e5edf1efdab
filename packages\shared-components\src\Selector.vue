<template>
  <div class="cpt-selector" :class="{ 'active': isShow }">
    <div class="cpt-selector-trigger" ref="triggerRef">
      <slot></slot>
    </div>
    <div class="cpt-selector-content">
      <Popover
        class="cpt-selector-popover"
        :triggerRef="triggerRef"
        :triggerType="'click'"
        :hideDelay="0"
        :showDelay="0"
        :placement="appendToBody ? placement : ''"
        :disable="disable"
        :clickHidden="true"
        :appendToBody="appendToBody"
        :offset="offset"
        @updateShow="handleUpdateShow"
      >
        <div class="cpt-selector-wrapper" :class="type" :style="{ width: computedWidth }">
          <div 
            class="cpt-selector-option"
            v-for="item in options.filter(item => !item.hidden)" 
            :key="item.value" 
            @click.stop="handleClickItem(item)" 
            :class="[
              { 'active': type !== 'menu' && item.value === model },
              item.type
            ]"
          >
            {{ item.label }}
            <div class="check-icon" v-if="showCheckIcon && item.value === model">
              <i class="iconfont icon-common-check-line"></i>
            </div>
          </div>
        </div>
      </Popover>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import Popover from './Popover.vue'

const model = defineModel({
  type: String,
  default: ''
})
const emit = defineEmits(['select'])

const props = defineProps({
  disable: {
    type: Boolean,
    default: false
  },
  options: {
    type: Array,
    default: () => []
  },
  appendToBody: {
    type: Boolean,
    default: true
  },
  width: {
    type: String,
    default: '160px'
  },
  type: {
    type: String,
    default: 'default'
  },
  showCheckIcon: {
    type: Boolean,
    default: true
  },
  placement: {
    type: String,
    default: 'bottom'
  },
  autoWidth: {
    type: Boolean,
    default: false
  },
  offset: {
    type: Object,
    default: () => ({
      x: 0,
      y: 0,
    })
  }
})

const triggerRef = ref(null)
const isShow = ref(false)

const computedWidth = computed(() => {
  if(props.autoWidth) {
    return triggerRef.value.getBoundingClientRect().width - 16 + 'px' // 去掉padding宽度
  }
  return props.width
})

const handleUpdateShow = (show) => {
  isShow.value = show
}

const handleClickItem = (option) => {
  model.value = option.value
  emit('select', option)
  triggerRef.value.click()
}

</script>
<style lang="scss">
.cpt-selector {
  position: relative;

  &.active {
    .cpt-selector-trigger .icon-common-arrow-down-filled {
      transform: rotate(180deg);
    }
    .cpt-selector-trigger .icon-common-arrow-left-line {
      transform: rotate(90deg);
    }
  }
  .cpt-selector-trigger {
    cursor: pointer;
    height: 100%;
    width: 100%;
    .icon-common-arrow-down-filled {
      transition: transform 0.2s ease-in-out;
    }
    .icon-common-arrow-left-line {
      transform: rotate(270deg);
      transition: transform 0.2s ease-in-out;
    }
  }
  .cpt-selector-content {
    position: relative;
  }
}
.cpt-selector-popover {
  position: absolute;
  padding: 8px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.10);
  background-color: var(---general-color-primary-0, #fff);
  .cpt-selector-wrapper {
    padding: 2px 0;
    &.menu {
      .cpt-selector-option {
        font-weight: 600;
      }
    }
    .cpt-selector-option {
      padding: 8px;
      font-size: 14px;
      font-weight: 400;
      border-radius: 5px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      &.active {
        color: var(---general-color-primary-100, rgba(20, 25, 30, 1));
        font-weight: 600;
        .iconfont {
          font-weight: 400;
        }
      }
      &.danger {
        color: $general-color-danger;
      }
      .check-icon {
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background-color: var(---general-color-primary-100, rgba(20, 25, 30, 1));
        text-align: center;
        line-height: 16px;
        .iconfont {
          width: 16px;
          height: 16px;
          display: block;
          font-size: 12px;
          color: $general-color-text-6;
          &:before {
            line-height: 16px;
          }
        }
      }
    }
  }
}
</style>