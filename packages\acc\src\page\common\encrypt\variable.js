
// abeghimnopqrstuw
const V = ['a', 'b', 'e', 'g', 'h', 'i', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'w']

function x1 (x) {
  if (x & 0x80) {
    return (((x << 1) ^ 0x1B) & 0xFF)
  }
  return x << 1
}

function x2 (x) {
  return (x1(x) ^ x)
}

function x3 (x) {
  return (x2(x1(x)))
}

function x4 (x) {
  return x3(x2(x1(x)))
}

function xe (x) {
  return (x4(x) ^ x3(x) ^ x2(x))
}

function xx (col) {
  let tmp = [0, 0, 0, 0]
  tmp[0] = xe(col[0]) ^ x4(col[1]) ^ x3(col[2]) ^ x2(col[3])
  tmp[1] = x2(col[0]) ^ xe(col[1]) ^ x4(col[2]) ^ x3(col[3])
  tmp[2] = x3(col[0]) ^ x2(col[1]) ^ xe(col[2]) ^ x4(col[3])
  tmp[3] = x4(col[0]) ^ x3(col[1]) ^ x2(col[2]) ^ xe(col[3])
  col[0] = tmp[0]
  col[1] = tmp[1]
  col[2] = tmp[2]
  col[3] = tmp[3]
  return col
}

module.exports = { V, xx }