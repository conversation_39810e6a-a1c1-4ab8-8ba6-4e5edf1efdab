<template>
  <div
    class="cpt-bbs-content"
    ref="outer_ref"
  >
    <NewsItemHeader :user="newsData.user" />
    <div
      class="bbs-content-title"
      v-html="newsData.title"
    ></div>
    <div
      class="bbs-content-content"
      v-if="newsData.description"
      v-html="newsData.description"
    ></div>
    <BbsImgs
      class="bbs-content-imgs"
      v-if="newsData.imgs && newsData.imgs.length > 0"
      :link="newsData"
      :outer_ref="outer_ref"
    />
    <BbsVideo
      class="bbs-content-video"
      v-if="newsData.video"
      :video="newsData.video"
    ></BbsVideo>
    <NewsItemBottom :newsData="newsData" />
  </div>
</template>

<script setup name="BbsContent">
import { ref, defineProps, defineAsyncComponent } from 'vue';
import NewsItemHeader from '../NewsItemHeader/index.vue';
import NewsItemBottom from '../NewsItemBottom/index.vue';
const BbsImgs = defineAsyncComponent(() =>
  import('../../../../../../components/bbs/BbsImgs.vue')
);
const BbsVideo = defineAsyncComponent(() =>
  import('../../../../../../components/bbs/BbsVideo.vue')
);

const props = defineProps({
  newsData: {
    type: Object,
    default: () => {},
  },
});

const outer_ref = ref(null);
</script>

<style lang="scss">
.cpt-bbs-content {
  width: 100%;
  display: flex;
  padding: 14px 12px 0px 12px;
  flex-direction: column;
  align-items: flex-start;
  gap: 14px;
  align-self: stretch;
  background: var(---general-color-primary-0, #fff);

  cursor: pointer;

  .bbs-content-title {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    align-self: stretch;
    overflow: hidden;
    color: 1111111;
    text-overflow: ellipsis;
    font-family: 'PingFang SC';
    font-size: 17px;
    font-style: normal;
    font-weight: 500;
    line-height: 25px;
  }

  .bbs-content-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    align-self: stretch;
    overflow: hidden;
    color: 1111111;
    font-feature-settings: 'liga' off, 'clig' off;
    text-overflow: ellipsis;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 23px;
  }
}
</style>
