module.exports = {
  BILIBILI_LIVE_SOFTWARE_TITLE: 'livehime.exe', // 哔哩哔哩直播姬的进程名，用于识别是否是b站主播
  STEAM_INJECT_STATE: {
    PENDING: 0,
    SUCCESS: 1,
    FAILURE: -1,
  },
  TASK_BLACKLIST: ['svchost', 'ApplicationFrameHost', 'explorer.exe', 'WindowsInternal.ComposableShell.Experiences.TextInput.InputApp', 'Calculator', 'ShellExperienceHost', 'cmd.exe', 'SystemSettings'],
  // 瓦国际服和国服的区分，国际服路径一定有Riot Games，国服路径wegame的和启动器的不一样
  // 只判断国际服的path，其余情况都认为是国服
  VALORANT_GAME_INFO: [
    {
      type: 'international',
      path: 'Riot Games\\VALORANT\\live',
      name: '瓦罗兰特'
    },
    {
      type: 'national',
      path: '',
      name: '无畏契约'
    },
  ],

  SYSTEM_WINDOW_TYPE: {
    TOP: 'top_window',
    COMMON: 'window',
    FULLSCREEN: 'fullscreen_window'
  },

  MUSIC_APP_DEFAULT_NAME: [
    '酷狗音乐',
    '酷我音乐',
    '桌面歌词 - 酷狗音乐',
    '桌面歌词',
    'QQ音乐',
    '网易云音乐',
    'QQ音乐 听我想听',
    'Spotify Free',
  ],

  MUSIC_REARRANGE_STRINGS: [
    '-酷我音乐 ',
    ' - 酷狗音乐 '
  ],
}
