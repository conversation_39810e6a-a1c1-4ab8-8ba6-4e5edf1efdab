const { app } = require('electron');
const fs = require('original-fs');
const path = require('path');
const { getTargetVersion } = require('@heybox/electron-utils/version')
const log = require('@heybox/electron-utils/log')
const regedit = require('@heybox/electron-utils/regedit');
const ENV = require('../env').ENV;
const { homedir } = require('os');

Promise.all([
  regedit.getClientVersion(),
  regedit.getAsarVersion(),
  regedit.getNextLaunchVersion(),
  regedit.getMachineId(),
  regedit.getExeInstallPath(),
]).then(
  ([
    clientVersion,
    regeditAsarVersion,
    nextLaunchVersion,
    machineId,
    installPath,
  ]) => {
    log.info(`[clientVersion]: ${clientVersion}`);
    log.info(`[regeditAsarVersion]: ${regeditAsarVersion}`);
    log.info(`[launchVersion]: ${nextLaunchVersion}`);

    global.EXE_VERSION = clientVersion;
    global.machineId = machineId.slice(1, machineId.length - 1);
    global.installPath = installPath;

    try {
      let versionsPath = path.join(__dirname, '../../versions');
      // 开发环境用这个测试
      // let versionsPath = path.join(__dirname, '../out/versions')

      let versions = fs
        .readdirSync(versionsPath, { withFileTypes: true })
        .filter((dir) => {
          return dir.isDirectory();
        })
        .map((dir) => dir.name);

      const execVersion =
        ENV === 'test'
          ? getTargetVersion(versions, require('../package').version)
          : nextLaunchVersion;
      global.HeyboxAppExeVersion = execVersion;

      if (ENV === 'test' && execVersion) {
        let versionEntry = path.join(
          versionsPath,
          execVersion,
          'app',
          'index.js'
        );
        require(versionEntry);
      } else {
        let versionEntry = path.join(
          versionsPath,
          nextLaunchVersion,
          'app',
          'index.js'
        );
        require(versionEntry);

        if (regeditAsarVersion != nextLaunchVersion) {
          regedit.setAsarVersion(nextLaunchVersion);
        }
        if (versions.length > 0) {
          setTimeout(() => {
            try {
              versions.forEach((v, index) => {
                if (
                  v != clientVersion &&
                  v != regeditAsarVersion &&
                  v != nextLaunchVersion
                ) {
                  fs.rmSync(path.join(versionsPath, v), {
                    recursive: true,
                    force: true,
                  });
                }
              });
            } catch (error) {
              log.error(`[rm asar version]: ${error.message}`);
            }
          }, 2000);
        }

        let exeVersionsPath = path.join(__dirname, '../../../../');
        let exeVersions = fs
          .readdirSync(exeVersionsPath, { withFileTypes: true })
          .filter((dir) => {
            return dir.isDirectory();
          })
          .map((dir) => dir.name);

        if (exeVersions.length > 1) {
          setTimeout(() => {
            exeVersions.forEach((v) => {
              if (v != clientVersion) {
                fs.rm(
                  path.join(exeVersionsPath, v),
                  { recursive: true, force: true },
                  (error) => {
                    if (error) {
                      log.error(`[rm exe version]: ${error.message}`);
                    }
                  }
                );
              }
            });
          }, 2000);
        }
      }
    } catch (error) {
      log.error(`[launcher]: ${error.message} \n ${error.stack}`);
    }
  }
);
