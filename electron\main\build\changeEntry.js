const fs = require('fs')
const path = require('path')

const mainPath = process.argv[2]

const packageJsonPath = path.join(__dirname, '../package.json')
const packageJson = fs.readFileSync(packageJsonPath, 'utf8')
const packageObject = JSON.parse(packageJson)

packageObject.main = mainPath ? mainPath.trim() : 'index.js'
const newPackageJson = JSON.stringify(packageObject, null, 2)
fs.writeFileSync(packageJsonPath, newPackageJson, 'utf8')