const path = require('path')
const client = require('./heybox_js/heyboxacc_x64.node');
const { BrowserWindow, ipcMain, shell } = require('electron');
const { getUrlParam, focusWindow } = require('./assets/js/utils')
const { EventEmitter } = require('events')
const window_config = require('./page/common/window_config')
const { runAccelerate } = require('./assets/js/accWorker')

class AccSdk extends EventEmitter {
  accWindow = {}
  smDeviceId = ''
  source = ''
  constructor() {
    super()
  }
  
  getVersionInfo() {
    const result = client.heyboxAcc.GetVersionInfo();
    return result
  }

  getSmDeviceId () {
    return new Promise((resolve) => {
      if(this.smDeviceId) {
        resolve(this.smDeviceId)
        return
      }
      ipcMain.on('send-sm-id', (e, v) => {
        // console.log('send-sm-id', v)
        this.smDeviceId = v
        
        resolve(v)
        ipcMain.removeAllListeners('send-sm-id')
      })
      if(this.accWindow.webContents) {
        this.accWindow.webContents.send('request-sm-id')
      }
    })
  }

  _handleLoginSuccess() {
    this.emit('onLogin')
    this.closeWebview('login')
  }

  initEvents() {
    ipcMain.handle('heybox:user-certify', (event, params) => {
      return this.userCertify(params)
    });
    ipcMain.handle('heybox:get-user-info', (event) => {
      return this.getUserInfo()
    });
    ipcMain.on('heybox:open-page', (event, page, params) => {
      this.openWebView(page, params)
    });
    ipcMain.handle('heybox:get-proxy-node-list', (event, params) => {
      return this.getProxyList(params)
    });
    
    ipcMain.handle('heybox:sdk-start-accelerate', (event, params) => {
      return this.startAcc(params)
    });
    
    ipcMain.handle('heybox:sdk-stop-accelerate', (event, params) => {
      return this.stopAcc(params)
    });
    
    ipcMain.handle('heybox:login-by-password', (event, params) => {
      return this.loginByPhonePassword(params)
    });
    
    ipcMain.handle('heybox:login-by-code', (event, params) => {
      return this.loginByPhoneCode(params)
    });
    
    ipcMain.handle('heybox:get-qr-code', (event) => {
      return this.loginGetQrCode()
    });
    
    ipcMain.handle('heybox:get-qr-state', (event, params) => {
      return this.loginGetQrState(params)
    });
    
    ipcMain.handle('heybox:get-code', (event, params) => {
      return this.loginGetPhoneCode(params)
    });
    
    ipcMain.handle('heybox:login-by-qr', (event, params) => {
      return this.loginByQr(params)
    });
    
    ipcMain.handle('heybox:get-wechat-qr-code', (event) => {
      return this.loginGetWeChatCode()
    });   
    
    ipcMain.handle('heybox:login-by-wechat', (event, params) => {
      return this.loginByWeChat(params)
    });

    ipcMain.handle('heybox:recharge', (event, params) => {
      return this.recharge(params)
    });

    ipcMain.handle('heybox:get-game-info', (event, params) => {
      return this.getGameInfo(params)
    });

    ipcMain.handle('heybox:send-request', (event, url, method, args, form) => {
      return new Promise((resolve, reject) => {
        const requestArg = {
          url,
          query: args,
          form,
          is_get: method === 'get',
        }
        console.log('requestArg', requestArg)
        const result = client.heyboxAcc.CommonApi(JSON.stringify(requestArg));
        console.log('requestArg', result)
        resolve(JSON.parse(result))
      })
    });

    ipcMain.handle('heybox:get-version', () => {
      return this.getVersionInfo()
    });
    ipcMain.on('heybox:close-window', (event, windowName) => {
      console.log('heybox:close-window', windowName)
      this.closeWebview(windowName)
    });
    ipcMain.on('heybox:open-in-browser', (event, url) => {
      try {
        console.log('open in browser', url)
        shell.openExternal(url).then(() => {
          console.log('open in browser success')
        })
      } catch (e) {
        console.log('open in browser error', e)
      }
    });
  }

  destroyEvents() {
    ipcMain.removeAllListeners('send-sm-id')
    ipcMain.removeAllListeners('heybox:close-window')
    ipcMain.removeHandler('heybox:user-certify')
    ipcMain.removeHandler('heybox:get-game-list')
    ipcMain.removeHandler('heybox:get-used-game-list')
    ipcMain.removeHandler('heybox:get-proxy-node-list')
    ipcMain.removeHandler('heybox:sdk-start-accelerate')
    ipcMain.removeHandler('heybox:sdk-stop-accelerate')
    ipcMain.removeHandler('heybox:logout')
    ipcMain.removeHandler('heybox:login-by-password')
    ipcMain.removeHandler('heybox:login-by-code')
    ipcMain.removeHandler('heybox:get-qr-code')
    ipcMain.removeHandler('heybox:get-qr-state')
    ipcMain.removeHandler('heybox:get-code')
    ipcMain.removeHandler('heybox:login-by-qr')
    ipcMain.removeHandler('heybox:get-wechat-qr-code')
    ipcMain.removeHandler('heybox:login-by-wechat')
    ipcMain.removeHandler('heybox:recharge')
    ipcMain.removeHandler('heybox:send-request')
    ipcMain.removeHandler('heybox:get-version')
    ipcMain.removeHandler('heybox:get-game-info')
    ipcMain.removeAllListeners('heybox:open-in-browser')
  }

  openWebView(type, params) {
    console.log('openWebView', type, params)
    if(this.accWindow[type]) {
      this.accWindow[type].webContents.send('heybox:set-options', params)
      focusWindow(this.accWindow[type])
      
      return this.accWindow[type]
    }
    return new Promise((resolve, reject) => {
      try {
        const accWindow = new BrowserWindow({
          ...window_config[type],
          title: type,
          titleBarStyle: 'hidden',
          transparent: true,
          // frame: false,
          webPreferences: {
            preload: path.join(__dirname, './preload.js'),
            webSecurity: false,
            contextIsolation: false,
            nodeIntegration: true,
          },
        });
        let fileUrl = new URL(path.join(__dirname, `./page/${type}.html`));
        if(params) {
          Object.entries(params).forEach(([key, value]) => {
            fileUrl.searchParams.set(key, value);
          });
        }
        accWindow.loadURL(fileUrl.toString());
        accWindow.webContents.openDevTools();
        this.accWindow[type] = accWindow
        if(type === 'login') {
          this.initLoginWindow(accWindow)
        }
        accWindow.on('closed', () => {
          console.log('accWindow closed')
          delete this.accWindow[type]
        })
        resolve(accWindow)
      } catch (error) {
        reject(error)
      }
    })
  }

  closeWebview(type) {
    if(this.accWindow[type]) {
      this.accWindow[type].close()
    }
  }

  getWebViews() {
    return Object.values(this.accWindow)
  }

  initLoginWindow(accWindow) {
    console.log('initLoginWindow', accWindow)
    accWindow.webContents.on('will-navigate', (e, url) => {
      console.log('will-navigate', url)
      if (url.includes('/account/wechat/login_redirect/v2/proxy')) {
        e.preventDefault()
        let code = getUrlParam('code', url)
        let res = this.loginByWechat({ code })
        accWindow.webContents.send('login-by-wechat', res)
      }
    })
  }
  
  initSdk(source, x_xhh_tokenid) {
    this.smDeviceId = x_xhh_tokenid
    this.source = source
    const heyboxAccConfig = {
      download_source: source,
      x_app: source === 'heybox_pc' ? 'heybox_pc' : 'heybox_acc_pc'
    }
    runAccelerate('initAcc', heyboxAccConfig).then(res => {
      console.log('initAccRes', res)
      this.emit('onSdkInit', res.result)
      if(res.status === 'ok' && !res.result) {
        this.initEvents()
      }
    })
  }

  unitSdk() {
    this.destroyEvents()
    let res = client.heyboxAcc.UnInit();
    console.log('UnInit', res)
  }
  
  getUserInfo() {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const getUserInfoArg = {
          url: "http://accoriapi.xiaoheihe.cn/proxy/get_user_vip_info/?",
          form: {
            x_xhh_tokenid
          }
        }
        const result = client.heyboxAcc.CommonApi(JSON.stringify(getUserInfoArg));
        console.log('getUserInfoArg', result)
        resolve(JSON.parse(result))
      })
    })
  }

  getGameInfo(params) {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const getGameInfoArg = {
          "url": "https://accoriapi.xiaoheihe.cn/proxy/get_game_info_for_pc/",  // 固定
          "query": {
            "acc_id": params.acc_id  // 必选，acc_id
          },
          "form": {
            "x_xhh_tokenid": x_xhh_tokenid
          }
        }
        console.log('getGameInfoArg', getGameInfoArg)
        const result = client.heyboxAcc.CommonApi(JSON.stringify(getGameInfoArg));
        console.log('getGameInfoRes', result)
        resolve(JSON.parse(result))
      })
    })
  }
  
  userCertify(params) {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const certifyArg = {
          url: "https://accoriapi.xiaoheihe.cn/account/acc/certificate/?",
          form: {
            x_xhh_tokenid,
            ...params
          }
        }
        console.log('certifyArg', certifyArg)
        const result = client.heyboxAcc.CommonApi(JSON.stringify(certifyArg));
        console.log('certifyArg', result)
        let res = JSON.parse(result)
        if(res.status === 'ok') {
          this.emit('onAuth')
          this.closeWebview('realname_auth')
        }
        resolve(res)
      })
    })
  }
  
  getGameList(params) {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const getGameListArg = {
          x_xhh_tokenid,
          ...params
        }
        console.log('getGameListArg', getGameListArg)
        const result = client.heyboxAcc.GetGameList(JSON.stringify(getGameListArg));
        console.log('getGameListArg', result)
        resolve(JSON.parse(result))
      })
    })
  }
  
  getUsedGameList(params) {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const getUsedGameListArg = {
          x_xhh_tokenid: x_xhh_tokenid,
          ...params
        }
        console.log('getUsedGameListArg', getUsedGameListArg)
        const result = client.heyboxAcc.GetGameList(JSON.stringify(getUsedGameListArg));
        console.log('getUsedGameListRes', result)
        resolve(JSON.parse(result))
      })
    })
  }
  
  getProxyList(params) {
    return new Promise((resolve, reject) => {
      const getProxyNodeListArg = params
      console.log('getProxyNodeListArg', getProxyNodeListArg)
      const result = client.heyboxAcc.GetProxyNodeList(JSON.stringify(getProxyNodeListArg));
      console.log('getProxyNodeListRes', result)
      resolve(JSON.parse(result))
    })
  }
  
  startAcc(params) {
    return new Promise((resolve, reject) => {
      const startInfo = params
      console.log('startInfo', startInfo)
      runAccelerate('startAcc', startInfo).then(res => {
        console.log('startAccRes', res)
        if(res.status === 'ok') {
          this.emit('onAccStateChange', true, params);
          this.closeWebview('proxy_select');
        } else {
          this.emit('onErrorMessage', 'startAcc', res.msg)
        }
        resolve(res)
      }).catch(err => {
        this.emit('onErrorMessage', 'startAcc', err.msg)
        resolve(err)
      })
    })
  }
  
  stopAcc(params) {
    return new Promise((resolve, reject) => {
      const stopInfo = params
      console.log('stopInfo', stopInfo)
      setTimeout(() => {
        const result = client.heyboxAcc.StopAcc(JSON.stringify(stopInfo));
        console.log('stopRes', result)
        let res = JSON.parse(result)
        if(res.status === 'ok') {
          this.emit('onAccStateChange', false)
        }
        resolve(res)
      }, 0)
    })
  }

  loginGetPhoneCode(params) {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const loginGetPhoneCodeArg = {
          url: "https://accoriapi.xiaoheihe.cn/account/get_login_code/",
          form: {
            x_xhh_tokenid,
            phone_num: params.phone,
          }
        }
        const result = client.heyboxAcc.CommonApi(JSON.stringify(loginGetPhoneCodeArg));
        console.log("login_get_phone_code result: ", result);
        resolve(JSON.parse(result))
      })
    })
  }

  loginByPhonePassword(params) {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        let loginByPhonePasswordArg = {
          url: "https://accoriapi.xiaoheihe.cn/account/heybox_proxy_login/",
          form: {
            x_xhh_tokenid
          },
          request_type:"login_by_phone_password",
          params
        }
        const result = client.heyboxAcc.AcountApi(JSON.stringify(loginByPhonePasswordArg));
        console.log("login_by_phone_password result: ", result);
        let res = JSON.parse(result)
        if(res.status === 'ok') {
          this._handleLoginSuccess()
        }
        resolve(res)
      })
    })
  }

  loginByPhoneCode(params) {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        let loginByPhondeCodeArg = {
        url: "https://accoriapi.xiaoheihe.cn/account/login_code/",
        query: {
          code: params.code,
        },
        form: {
          x_xhh_tokenid,
          phone_num: params.phone,
        },
        "request_type":"login_by_phone_code",
        params
      };
      const result = client.heyboxAcc.AcountApi(JSON.stringify(loginByPhondeCodeArg));
      console.log("login_by_phone_code result: ", result);
      let res = JSON.parse(result)
      if(res.status === 'ok') {
        this._handleLoginSuccess()
        }
        resolve(res)
      })
    })
  }

  loginGetQrCode() {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const loginGetQrCodeArg = {
          url: "https://accoriapi.xiaoheihe.cn/account/get_qrcode_url/",
          form: {
            x_xhh_tokenid,
          }
        }
        const result = client.heyboxAcc.CommonApi(JSON.stringify(loginGetQrCodeArg));
        console.log("login_get_qr_code_url result: ", result);
        resolve(JSON.parse(result))
      })
    })
  }

  loginGetQrState(params) {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const loginGetQrStateArg = {
          url: "https://accoriapi.xiaoheihe.cn/account/qr_state/",
          query: {
            qr: params.qr,
          },
          form: {
            x_xhh_tokenid,
          }
        }
        const result = client.heyboxAcc.CommonApi(JSON.stringify(loginGetQrStateArg));
        // console.log("login_get_qr_state result: ", result);
        resolve(JSON.parse(result))
      })
    })
  }

  loginByQr(params) {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const loginByQrArg = {
          url: "https://accoriapi.xiaoheihe.cn/account/heybox_proxy_login/",
          form: {
            x_xhh_tokenid,
          },
          request_type:"login_by_qr",
          params
        }
        console.log('loginByQrArg', loginByQrArg)
        const result = client.heyboxAcc.AcountApi(JSON.stringify(loginByQrArg));
        console.log("login_by_qr result: ", result);
        let res = JSON.parse(result)
        if(res.status === 'ok') {
          this._handleLoginSuccess()
        }
        resolve(res)
      })
    })
  }

  loginGetWeChatCode() {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const loginGetWeChatCodeArg = {
          url: "https://accoriapi.xiaoheihe.cn/account/wechat/login/v2/proxy/",
          form: {
            x_xhh_tokenid,
          }
        }
        const result = client.heyboxAcc.CommonApi(JSON.stringify(loginGetWeChatCodeArg));
        console.log("login_get_wechat_code result: ", result);
        resolve(JSON.parse(result))
      })
    })
  }

  loginByWechat(params) {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        const loginByWechatArg = {
          // url: "https://accoriapi.xiaoheihe.cn/account/wechat/login_redirect/v2/proxy/",
          url: "https://api.xiaoheihe.cn/account/wechat/login_redirect/v2/proxy/",
          query: {
            code: params.code,
            state: "xiaoheihe",
          },  
          form: {
            x_xhh_tokenid,
          },
          request_type:"login_by_wechat",
          params: {}
        } 
        console.log('loginByWechatArg', loginByWechatArg)
        const result = client.heyboxAcc.AcountApi(JSON.stringify(loginByWechatArg));
        console.log("login_by_wechat result: ", result);
        let res = JSON.parse(result)
        if(res.status === 'ok') {
          this._handleLoginSuccess()
        }
        resolve(res)
      })
    })
  }

  logout() {
    return new Promise((resolve, reject) => {
      this.getSmDeviceId().then(x_xhh_tokenid => {
        let logoutArg = {
          url: "https://accoriapi.xiaoheihe.cn/account/heybox_proxy_login/",
          form: {
            x_xhh_tokenid
          },
          request_type:"logout",
        }
        const result = client.heyboxAcc.AcountApi(JSON.stringify(logoutArg));
        console.log("logout result: ", result);
        resolve(JSON.parse(result))
      })
    })
  }

  recharge(params) {
    return new Promise((resolve, reject) => {
      // const rechargeArg = {
      //   url: "https://accoriapi.xiaoheihe.cn/account/recharge/",
      // }
      if(params.type === 'success') {
        this.emit('onRecharge')
        this.closeWebview('recharge')
        resolve({
          status: 'ok',
          result: {},
          msg: '充值成功'
        })
      } else {
        this.emit('onRecharge', '充值失败')
        this.closeWebview('recharge')
        resolve({
          status: 'failed',
          result: {},
          msg: '充值失败'
        })
      }
    })
  }
}

module.exports = new AccSdk()
