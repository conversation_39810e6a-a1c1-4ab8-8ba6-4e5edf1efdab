<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>小黑盒加速器插件</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/theme.css">
    <link rel="stylesheet" href="../assets/font/iconfont.css">
  </head>
  <body>
    <div class="shading"></div>
    <div class="realname-auth-cpt">
      <div class="title-wrapper">
        <div class="title">
          当前账户需要实名认证
        </div>
        <svg id="close-button" class="close pointer" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="Dismiss">
            <path id="Shape" d="M4.08859 4.21569L4.14645 4.14645C4.32001 3.97288 4.58944 3.9536 4.78431 4.08859L4.85355 4.14645L10 9.293L15.1464 4.14645C15.32 3.97288 15.5894 3.9536 15.7843 4.08859L15.8536 4.14645C16.0271 4.32001 16.0464 4.58944 15.9114 4.78431L15.8536 4.85355L10.707 10L15.8536 15.1464C16.0271 15.32 16.0464 15.5894 15.9114 15.7843L15.8536 15.8536C15.68 16.0271 15.4106 16.0464 15.2157 15.9114L15.1464 15.8536L10 10.707L4.85355 15.8536C4.67999 16.0271 4.41056 16.0464 4.21569 15.9114L4.14645 15.8536C3.97288 15.68 3.9536 15.4106 4.08859 15.2157L4.14645 15.1464L9.293 10L4.14645 4.85355C3.97288 4.67999 3.9536 4.41056 4.08859 4.21569L4.14645 4.14645L4.08859 4.21569Z" fill="#424242"/>
          </g>
        </svg>
      </div>
      <div class="content-wrapper">
        <div class="content">
          <div class="input-wrapper">
            <input type="text" id="name" placeholder="请输入姓名">
          </div>
          <div class="input-wrapper">
            <input type="text" id="id_card" placeholder="请输入身份证">
          </div>
          <div class="tips-wrapper">
            <p class="tip">
              <span class="required required-icon">*</span>
              <span class="tips-text">实名信息认证后&ensp;</span>
              <span class="required">不能修改，</span>
              <span class="tips-text">请认真填写</span>
            </p>
            <p class="tip">
              <span class="required required-icon">*</span>
              <span class="tips-text">未实名账号和未成年用户无法充值，详情查看</span>
              <a id="realname-auth-notice" class="link">《<span class="underline">实名认证系统升级公告</span>》</a>
            </p>
            <p class="tip">
              <span class="required required-icon">*</span>
              <span class="tips-text">以上信息仅用于实名认证，不会泄露予任何第三方</span>
            </p>
          </div>
          <div class="button-wrapper">
            <!-- <button id="certify-success" class="primary-button">认证成功</button>
            <button id="certify-failed" class="primary-button">认证失败</button> -->
            <button id="certify-button" class="primary-button">确定</button>
            <button id="cancel-button" class="default-button">取消</button>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script src="./common/initTheme.js"></script>
  <script type="module">
    import './assets/js/sm.js'
    import { toastError } from './common/toast.js'
    function init() {
      document.getElementById('certify-button').addEventListener('click', userCertify)
      document.getElementById('close-button').addEventListener('click', close)
      document.getElementById('cancel-button').addEventListener('click', close)
      document.getElementById('realname-auth-notice').addEventListener('click', () => openURL('https://acc.xiaoheihe.cn/help/detail/1/272'))
      // document.getElementById('certify-failed').addEventListener('click', () => openPage('dialog', {
      //   config: JSON.stringify({
      //     content: '未成年无法充值',
      //     desc: '详情查看<a class="link">《<span class="underline">实名认证系统升级公告</span>》</a>',
      //     type: 'warn',
      //     link: 'https://acc.xiaoheihe.cn/help/detail/1/272',
      //   })
      // }))
    }
    async function userCertify() {
      console.log('certify')
      let name = document.getElementById('name').value
      let id_card = document.getElementById('id_card').value
      console.log('name', name)
      console.log('id_card', id_card)
      let res = await window.electronAPI.userCertify({
        name: name,
        id_card: id_card,
      })
      console.log('certify', res)
      if(res.status === 'ok') {
        // changePage('main')
      } else {
        toastError(res.msg)
      }
    }
    function close() {
      window.electronAPI.close('realname_auth')
    }
    function openURL(url) {
      window.electronAPI.openInBrowser(url)
    }
    function openPage(page, params) {
      window.electronAPI.openPage(page, params)
    }
    init()
  </script>
  <style>
    .realname-auth-cpt {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 24px 24px;
      background-color: var(--nb1r);
      border-radius: 16px;
      overflow: hidden;
      .input-wrapper {
        margin: 10px 0 6px;
      }
      .content-wrapper {
        position: relative;
        z-index: 1;
      }
      .required-icon {
        position: relative;
        top: 5px;
        margin-right: 1px;
        font-size: 16px;
        font-weight: 700;
      }
    }
    .tips-wrapper {
      margin-bottom: 22px;
    }
  </style>
</html>
