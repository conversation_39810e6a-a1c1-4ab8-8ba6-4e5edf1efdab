const { ClassicLevel } = require('classic-level')
const path = require('path')
const { app } = require('electron')
const dbPath = path.join(app.getPath('userData'), 'heybox');
const Sentry = require('@sentry/electron');
const { log } = require('@heybox/electron-utils')

class LevelStore {
  constructor(dbPath) {
    this.db = new ClassicLevel(dbPath, { valueEncoding: 'json' })
  }

  async get(key) {
    try {
      const value = await this.db.get(key);
      return value ? JSON.parse(value) : null
    } catch (err) {
      Sentry.captureException(err)
      if (err.notFound) {
        return null;
      }
      log.info(err)
    }
  }

  async set(key, value) {
    await this.db.put(key, JSON.stringify(value));
  }

  async delete(key) {
    await this.db.del(key);
  }

  async close() {
    await this.db.close();
  }
}

module.exports = new LevelStore(dbPath);