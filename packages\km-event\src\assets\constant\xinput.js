const xinput_map = {
  1: "GAMEPAD_DPAD_UP", // 方向键上
  2: "GAMEPAD_DPAD_DOWN", // 方向键下
  4: "GAMEPAD_DPAD_LEFT", // 方向键左
  8: "GAMEPAD_DPAD_RIGHT", // 方向键右
  16: "GAMEPAD_START", // 菜单键（三条横杠，位于右侧）
  32: "GAMEPAD_BACK", // 视图键（两个方框，位于左侧）
  64: "GAMEPAD_LEFT_THUMB", // 左摇杆按键
  128: "GAMEPAD_RIGHT_THUMB", // 右摇杆按键
  256: "GAMEPAD_LEFT_SHOULDER", // LB键 (左肩键)
  512: "GAMEPAD_RIGHT_SHOULDER", // RB键 (右肩键)
  4096: "GAMEPAD_A", // A键
  8192: "GAMEPAD_B", // B键
  16384: "GAMEPAD_X", // X键
  32768: "GAMEPAD_Y" // Y键
};

/**
 * 获取被按下的按钮名称
 * @param {number} value - 按钮的位掩码值
 * @returns {string[]} - 被按下的按钮名称数组
 */
function getPressedXinputButtons(value, triggers) {
  const pressedButtons = [];
  for (const [bitmask, buttonName] of Object.entries(xinput_map)) {
      if (value & bitmask) {
          pressedButtons.push(buttonName);
      }
  }
  if (triggers.leftTrigger) {
      pressedButtons.push('LEFT_TRIGGER'); // LT键(左扳机键)
  }
  if (triggers.rightTrigger) {
      pressedButtons.push('RIGHT_TRIGGER'); // RT键(右扳机键)
  }
  return pressedButtons;
}

module.exports = { xinput_map, getPressedXinputButtons };