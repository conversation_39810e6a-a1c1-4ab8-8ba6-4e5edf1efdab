const { app, BrowserWindow, ipc<PERSON>ain, shell, ipc<PERSON>enderer, dialog, clipboard, session, Menu, net, protocol } = require('electron')
const path = require('path');
const fs = require('fs')
const { homedir } = require('os')
const { exec } = require('child_process');
const AccSdk = require('@heybox/acc-sdk')

// 环境相关配置
ELECTRON_ENV = require('../env').ELECTRON_ENV || 'prod';
LOCAL_WEB_PATHS = require('../env').LOCAL_WEB_PATHS || [];

IS_PROD_URL = ELECTRON_ENV === 'prod' || ELECTRON_ENV === 'local-prod'
IS_LOCAL_DEV = ELECTRON_ENV === 'local' || ELECTRON_ENV === 'local-prod'

global.EXE_BIT = require('../env').NODE_BIT
global.ELECTRON_VERSION = process.versions.electron
global.CLIENT_VERSION = require('../package.json').version
global.ELECTRON_APP = 'heybox'
global.appPath = app.isPackaged ? path.dirname(app.getPath('exe')) : app.getAppPath()

global.appDir = __dirname
global.packageDir = IS_LOCAL_DEV 
  ? path.join(__dirname, '../../../packages')
  : path.join(__dirname, '../node_modules/@heybox')
console.log('global.packageDir', global.packageDir)
global.USER_HOME = homedir()
const { log, store } = require('@heybox/electron-utils')
const cdn = require('@heybox/electron-utils/cdn')
log.initVersion(CLIENT_VERSION)
const utils = require('@heybox/electron-utils')
const { DEFAULT_WEBVIEW } = require('./assets/constant/constant')
store.init()
const requireAddon = utils.requireAddon
global.kmEvent = null
const tastIterate = require('@heybox/process-iterate')
global.miniprogram_base_dir = app.getPath('userData')
global.MiniProgram = null

const trayMenu = require('./tray-menu/index')

const gotTheLock = app.isPackaged ? app.requestSingleInstanceLock() : true
if (!gotTheLock) {
  app.quit();
}

// 避免使用远程内容的安全警告
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

// 主窗口引用
global.mainWindow = null;

FORCE_ENV = main_config.FORCE_ENV;

IS_HARDWARE_ACCELERATION = main_config.setting_data.hardware_acceleration;
process.env.http_proxy = '';

let isSilent = false // 是否静默启动

// API配置
BASE_API = IS_PROD_URL ? 'https://chat.xiaoheihe.cn' : 'https://chat.debugmode.cn'
BASE_HEYBOX_API = IS_PROD_URL ? 'https://api.xiaoheihe.cn' : 'https://heybox.debugmode.cn'
BASE_HEYACC_API = IS_PROD_URL ? 'https://accoriapi.xiaoheihe.cn' : 'https://heybox.debugmode.cn'
LOCAL_URL = IS_PROD_URL ? 'https://webtest.xiaoheihe.cn:8080/' : 'https://webtest.debugmode.cn:8080/'

ENV_TAG = FORCE_ENV === 'primary' 
  ? 'RylaiServiceTag/__primary__'
  : FORCE_ENV === 'intranet' 
    ? 'RylaiServiceTag/intranet'
    : ''
    // : 'RylaiServiceTag/master'
const argv = require('./assets/js/minimist')(process.argv.slice(1))

main()

function main () {
  beforeStartUp()

  app.whenReady().then(async () => {
    // 处理协议
    // handleSchemeRegister()
    log.info('[hook] app-ready')
    if (!gotTheLock) {
      app.quit()
    } else {
      // 正常启动
      launch()
    }
  })
}

async function launch () {
  const cookies = require('./cookie/account_listener')
  cookies.init()
  referRewrite()
  createWindow()
  initIpc()
  initMainWindowEvent()
  initWebContentsEvent()
  global.clientVersionManager = require('./manager/clientVersionManager');

  !global.kmEvent && (global.kmEvent = require('@heybox/km-event'))
  await loadIndexPage()
  afterStartUp()
}

function beforeStartUp () {
  require("./process/sentry_init").sentryInit(ELECTRON_ENV == 'prod' && app.isPackaged && !main_config.PREVENT_SENTRY)
  handleArgs()
  setAppParams()
  setAppEvent()
  Menu.setApplicationMenu(null)

  global.globalStateManager = require('./manager/globalStateManager');

  const initMemoryReport = require("./assets/js/memory_report")
  initMemoryReport()
  global.webContentsManager = require('./manager/webContentsManager');
}

function afterStartUp () {
  global.kmEvent.startWatch()
  !global.MiniProgram && (global.MiniProgram = require('@heybox/mini-program'))

  trayMenu.init()

  global.webContentsManager.initResizeWatch()

  ensurePopupManager()

  require('@heybox/km-event/src/continue_key_press')

  // 登陆后查询
  global.kmEvent?.recover()
  tastIterate.initProcessInterval()
  // global.blackMyth = require('./process/black_myth_wukong')
}

/**
 * 创建主窗口
 */
async function createWindow() {
  mainWindow = new BrowserWindow({
    width: 1346,
    height: 798,
    resizable: true,
    maximizable: true,
    titleBarStyle: 'hidden',
    frame: false,
    icon: path.join(__dirname, './assets/images/favicon.ico'),
    backgroundColor: 'rgb(247, 248, 249)',
    show: !isSilent,
    webPreferences: {
      preload: path.join(__dirname, './preload/main.js'),
      // sandbox: false,
      nodeIntegration: true,
      // contextIsolation: false,
      additionalArguments: ['--window-id=main']
    }
  });
}

async function loadIndexPage() {
  log.info('[hook] loadIndexPage start')
  
  // 检测网络状况，如果没网延时200ms检查网络，最多尝试25次
  for (let i = 0; i < 25; i++) {
    let online = net.isOnline()
    log.info('[net check]', online)
    if (!online) {
      await utils.sleep(200)
    } else {
      break
    }
  }

  try {
    const { getWebUrl, updateWebContentsUa } = require('./assets/js/utils')
    updateWebContentsUa(mainWindow.webContents)

    // 只加载 Main WebView，不预加载默认的 Library WebView
    // 默认 WebView 将在用户登录成功后通过 loadDefaultWebView() 加载
    if (IS_LOCAL_DEV) {
      await mainWindow.loadURL(getWebUrl('main'))
    } else {
      await mainWindow.loadFile(getWebUrl('main'))
    }

    mainWindow.webContents.openDevTools({ mode: 'detach', activate: true });
  } catch (e) {
    // 如果加载过程中，程序被退出，会导致报错 ERR_FAILED (-2) loading，无需处理
    console.error(e)
  }
  log.info('[hook] loadIndexPage end')
}

async function reloadWebContent() {
  log.info('[hook] reloadWebContent')
  // mainwindow 只有 sidebar 不需要刷新
  // mainWindow?.reload()
  global.kmEvent?.resetPrevent()
  global.webContentsManager.destroyAllWebView()
  // 重新加载当前活跃的 WebView
  const currentView = global.webContentsManager.currentActiveView
  log.info(`[hook] reloadWebContent - reloading current active view: ${currentView}`)
  global.webContentsManager.preloadWebView(currentView)
}

function referRewrite() {
  const allowedRedirectDomains = [
    /^(.+\.)?xiaoheihe\.cn$/,
    /^(.+\.)?debugmode\.cn$/
  ];
  
  session.defaultSession.webRequest.onBeforeSendHeaders({ urls: ['*://*/*'] }, (details, callback) => {
    let refererURL = { pathname: '' }
    try {
      refererURL = new URL(details.requestHeaders.Referer)
    } catch (error) { }

    if (refererURL.hostname === '127.0.0.1' || !details.requestHeaders.Referer || (IS_LOCAL_DEV && IS_PROD_URL)) {
      details.requestHeaders.Referer = BASE_API
    }
    callback({ requestHeaders: details.requestHeaders })
  })
  session.defaultSession.webRequest.onHeadersReceived({ urls: ['*://*/*'] }, (details, callback) => {
    let set_cookie_key = Object.keys(details.responseHeaders).find(key => key.toLowerCase() === 'set-cookie') // electron 22版本是Set-Cookie, 28版本是set-cookie
    if (details.responseHeaders && details.responseHeaders[set_cookie_key]) {
      details.responseHeaders[set_cookie_key] = details.responseHeaders[set_cookie_key].map((cookie) => {
        return cookie + ';SameSite=None;Secure'
      })
    }
    //检查是否为受保护的端点
    if (isEndpointProtected(details.url)) {
      const redirectStatusCodes = [301, 302, 303, 307, 308];
      if (redirectStatusCodes.includes(details.statusCode)) {
        const location = details.responseHeaders.location && details.responseHeaders.location[0];
        if (location) {
          try {
            const redirectURL = new URL(location, details.url);
            const redirectDomain = redirectURL.hostname;
            const isAllowed = allowedRedirectDomains.some(pattern => pattern.test(redirectDomain));
            if (!isAllowed) {
              // 非法重定向处理
              log.info(`[DNS ERROR] 非法重定向到: ${redirectURL.toString()}`, details);
              mainWindow.webContents.send('dns-change-warn');
              // 阻止重定向
              delete details.responseHeaders.location;
            }
          } catch (error) {
            log.error('[DNS ERROR] 解析重定向URL失败:', error);
          }
        }
      }
    }
    callback({ responseHeaders: details.responseHeaders })
  })
}

// 初始化ipc通讯
function initIpc () {
  //窗口最小化
  ipcMain.on('mainWindow:min', function () {
    mainWindow.minimize();
  })
  //窗口最大化 
  ipcMain.on('mainWindow:max', function () {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  })
  // 获取窗口是否是最大化状态
  ipcMain.handle('mainWindow:get-is-maximized', function (e, k) {
    return mainWindow.isMaximized()
  })
  //窗口关闭
  ipcMain.on('mainWindow:close', function () {
    app.quit()
  })
  //窗口隐藏
  ipcMain.on('mainWindow:hide', function () {
    mainWindow.hide();
  })
  //窗口聚焦
  ipcMain.on('mainWindow:focus', function (e) {
    focusWindow(BrowserWindow.fromWebContents(e.sender))
  })
  // 刷新网页
  ipcMain.on('mainWindow:refresh', function () {
    reloadWebContent()
  })
  //本地储存
  ipcMain.handle('localstorage:set', function (e, k, v) {
    return store.set(k, v)
  })

  ipcMain.handle('localstorage:get', function (e, k) {
    return store.get(k)
  })
  ipcMain.handle('localstorage:del', function (e, k) {
    return store.del(k)
  })
  //缓存储存到leveldb
  ipcMain.on('levelstore:set', function (e, k, v) {
    if(!global.level_store) global.level_store = require('./levelstore/index')
    level_store.set(k, v)
  })
  ipcMain.handle('levelstore:get', async function (e, k) {
    if(!global.level_store) global.level_store = require('./levelstore/index')
    return await level_store.get(k)
  })
  ipcMain.handle('levelstore:del', async function (e, k) {
    if(!global.level_store) global.level_store = require('./levelstore/index')
    return await level_store.del(k)
  })
  ipcMain.handle('openMiniProgram', (e, moduleName, options) => {
    MiniProgram && MiniProgram.beforeMount(moduleName, options)
  })
  ipcMain.handle('file:exists', async (event, path) => {
    // 检查文件是否存在
    return fs.promises.access(path, fs.constants.F_OK)
      .then(() => true)
      .catch(() => false);
  });
  ipcMain.handle('file:open', async (event, path) => {
    console.log('[file:open]')
    // 打开文件或文件夹
    return shell.showItemInFolder(path)
  });

  ipcMain.handle('file:openByDefaultApp', async (event, path) => {
    console.log('[file:openByDefaultApp]')
    // 以桌面的默认方式打开给定的文件
    return shell.openPath(path)
  });

  ipcMain.handle('file:remove', (event, files) => {
    console.log('[file:remove]')
    let res = {}
    console.log('remove files: ', files)
    try {
      files.forEach((filePath) => {
        fs.rmSync(filePath, {
          recursive: true,
          force: true
        })
      })
      res.status = 'ok'
    } catch (error) {
      res.status = 'failed'
      res.msg = '文件删除失败'
    }
    return res
  })

  // 获取本地路径
  ipcMain.handle('file:path', async function (e, config) {
    console.log('[file:path]')
    try {
      let res = await dialog.showOpenDialog({
        properties: ['openFile'],
        ...config
      })
      console.log(res)
      return {
        status: 'ok',
        ...res
      }
    } catch(e) {
      return {
        status: 'error',
        e
      }
    }
  })

  ipcMain.handle('getRegeditKey', async (e, path, key) => {
    return utils.getRegeditKey(path, key)
  })

  ipcMain.handle('getFileData', async (e, path) => {
    return utils.getFileData(path)
  })

  ipcMain.handle('writeFileData', async (e, path, data) => {
    return utils.writeFileData(path, data)
  })

  ipcMain.handle('getDeviceId', async (e) => {
    return utils.getMachineId()
  })

  ipcMain.handle('switchSteamAccount', (e, accountName) => {
    let addon = requireAddon('tool_helper')
    addon.switchSteamAccount((res) => {
      log.info('[switchSteamAccount] res', res)
      return res
    }, { 
      accountName: accountName,
      arguments: "-silent"
    })
  })
  ipcMain.handle('setOfflineMode', (e, accountName) => {
    let addon = requireAddon('tool_helper')
    addon.setOfflineMode((res) => {
      log.info('[setOfflineMode] res', res)
      return res
    }, { accountName: accountName })
  })
  // 监听主窗口页面刷新
  ipcMain.on('mainWindow:pageReloaded', () => {
    global.webContentsManager.destroyAllWebView()
  })
  ipcMain.handle('getStoreCookies', () => {
    return utils.getStoreCookies()
  })
  ipcMain.handle('clearStoreCookies', () => {
    return utils.clearStoreCookies()
  })
  ipcMain.handle('get-log-file-zip', () => {
    return new Promise(async (resolve, reject) => {
      try {
        const { createZip, removeFile } = utils
        const log_path = log.getFile().path
        let files_paths = [log_path]
        const temp_zip_path = path.join(app.getPath('temp'), `heyboxapp_temp_logs-${new Date().getTime()}.zip`)
        await createZip(temp_zip_path, files_paths)
        const { uploadFile } = cdn
        uploadFile(temp_zip_path, 'file', 'archives', 'log-file-zip', (uuid, status, res) => {
          if (status === 'success') {
            removeFile(temp_zip_path)
            resolve(res)
          } else if (status === 'error') {
            removeFile(temp_zip_path)
            reject(res)
          }
        })
      } catch (err) {
        reject(err)
      }
    })
  })
  initAcceleratorIpc()
  initSteamIpc()
}

// 初始化steam登陆 ipc
function initSteamIpc () {
  // Steam登录相关IPC处理
  ipcMain.handle('steam:login', async (event, options) => {
    try {
      const { loginSteamAccount } = utils
      
      return new Promise((resolve, reject) => {
        loginSteamAccount(options, {
          onGuardCode: async (count) => {
            // 发送事件到渲染进程，请求输入邮箱验证码
            event.sender.send('steam:guard-code-required', { count })
            
            // 等待渲染进程的响应
            return new Promise((resolveCode) => {
              const handler = (e, code) => {
                ipcMain.removeListener('steam:guard-code-input', handler)
                resolveCode(code)
              }
              ipcMain.once('steam:guard-code-input', handler)
            })
          },
          onAppConfirm: async () => {
            // 发送事件到渲染进程，请求用户确认或输入TOTP码
            event.sender.send('steam:app-confirm-required')
            
            // 等待渲染进程的响应
            return new Promise((resolveConfirm) => {
              const handler = (e, result) => {
                ipcMain.removeListener('steam:app-confirm-input', handler)
                resolveConfirm(result)
              }
              ipcMain.once('steam:app-confirm-input', handler)
            })
          },
          onSuccess: (result) => {
            resolve({ status: 'ok', data: result })
          },
          onProgress: (progress) => {
            log.info('[Steam Login] 下载进度更新:', {
              accountName: options.accountName,
              progress: progress,
              timestamp: new Date().toISOString()
            })
            event.sender.send('steam:login-progress', progress)
          },
          onError: (error) => {
            resolve({ status: 'failed', msg: error.message })
          }
        })
      })
    } catch (error) {
      return { status: 'failed', error: error.message }
    }
  })
  ipcMain.handle('steam:get-steam-apps', async (event, account_name) => {
    const { getSteamUserData } = utils
    const steamUserData = await getSteamUserData(account_name)
    return steamUserData
  })

  ipcMain.handle('popup:ensure-initialized', () => {
    ensurePopupManager();
    return true;
  })

  ipcMain.handle('shell:openExternal', async (event, url) => {
    try {
      await shell.openExternal(url);
      return { success: true };
    } catch (error) {
      console.error('Failed to open external URL:', error);
      return { success: false, error: error.message };
    }
  })

  ipcMain.handle('steam:checkInstallation', async () => {
    try {
      const steamPath = await utils.getRegeditKey(['HKCU', 'SOFTWARE\\Valve\\Steam'], 'SteamPath')
      return {
        installed: !!steamPath,
        path: steamPath || null
      }
    } catch (error) {
      log.error('[Steam Check] Error checking Steam installation:', error)
      return {
        installed: false,
        path: null,
        error: error.message
      }
    }
  })
}


// 初始化加速器ipc
function initAcceleratorIpc () {
  // 初始化加速器状态
  store.set('acc_config.is_init_acc', false)
  store.set('acc_config.is_acc_login', false)
  store.set('acc_config.is_acc_start', false)
  store.set('acc_config.current_acc_id', null)
  store.set('acc_config.acc_loading', false)
  store.set('acc_config.is_acc_initing', false)
  this.is_acc_ipc_init = false
  ipcMain.on('acc-sdk:init-acc', async (event, smDeviceId) => {
    AccSdk.initSdk('heybox_pc', smDeviceId)
    if(this.is_acc_ipc_init) return
    this.is_acc_ipc_init = true
    AccSdk.on('onSdkInit', (...args) => {
      mainWindow.webContents.send('onSdkInit', ...args)
    })
    AccSdk.on('onLogin', (...args) => {
      mainWindow.webContents.send('onLogin', ...args)
    })
    AccSdk.on('onAuth', (...args) => {
      global.webContentsManager.getWindow('popup')?.webContents.send('onAuth', ...args)
    })
    AccSdk.on('onAccStateChange', (...args) => {
      mainWindow.webContents.send('onAccStateChange', ...args)
    })
    AccSdk.on('onRecharge', (...args) => {
      global.webContentsManager.getWindow('popup')?.webContents.send('onRecharge', ...args)
    })
    AccSdk.on('onErrorMessage', (...args) => {
      mainWindow.webContents.send('onErrorMessage', ...args)
    })
  });
  
  ipcMain.handle('acc-sdk:get-user-info', (event) => {
    return AccSdk.getUserInfo()
  });
  
  ipcMain.handle('acc-sdk:start-game', (event, launch_schema) => {
    console.log('start-game', launch_schema)

    // 在Electron环境中使用shell.openExternal
    shell.openExternal(launch_schema)
      .then(() => {
        console.log('游戏启动成功')
      })
      .catch((error) => {
        console.error('启动游戏失败:', error)
      })
    // return AccSdk.startGame(params)
  });

  ipcMain.handle('acc-sdk:logout', async (event) => {
    console.log('acc-sdk:logout ipc')
    return await AccSdk.logout()
  })
}

// 初始化mainwindow事件
function initMainWindowEvent () {
  mainWindow.on('closed', (event) => {
    app.quit()
  }),
  mainWindow.on('close', (event) => {
    if (main_config.setting_data.close_event === 'hide') {
      mainWindow.hide();
      event.preventDefault()
    }
  }),
  mainWindow.on('maximize', () => {
    mainWindow.webContents.send('mainWindow:update-window-state', 'maximize')
  })
  mainWindow.on('unmaximize', () => {
    mainWindow.webContents.send('mainWindow:update-window-state', 'unmaximize')
  })
  mainWindow.on('minimize', () => {
    mainWindow.webContents.send('mainWindow:update-window-state', 'minimize')
  })
  mainWindow.on('hide', () => {
    mainWindow.webContents.send('mainWindow:update-window-state', 'hide')
  })
  mainWindow.on('show', () => {
    mainWindow.webContents.send('mainWindow:update-window-state', 'show')
  })
  mainWindow.on('restore', () => {
    mainWindow.webContents.send('mainWindow:update-window-state', 'restore')
  }) 
  mainWindow.on('blur', () => {
    mainWindow.webContents.send('mainWindow:update-window-state', 'blur')
  })
  mainWindow.on('focus', () => {
    mainWindow?.webContents?.send('mainWindow:update-window-state', 'focus')
  })
  // mainWindow.on('resized', () => {
  // })
  // mainWindow.hookWindowMessage(0x8002, (wParam, lParam) => {
  //   log.info(wParam.readUInt32LE(0), lParam.readUInt32LE(0))
  //   if (wParam.readUInt32LE(0) == 0x0342 && lParam.readUInt32LE(0) == 0x0212) {
  //     global.kmEvent?.stopWatch()
  //     app.exit()
  //   }
  // })
  // const addon = requireAddon('get_endpoint_info')
  // sentryTest()
}

function initWebContentsEvent () {
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.code == 'F12' && (main_config.devTool || !app.isPackaged)) {
      mainWindow.webContents.openDevTools()
    }
    if (!app.isPackaged && input.code == 'F5') {
      reloadWebContent()
    }
  })
  mainWindow.webContents.on('will-navigate', (e, url) => {
    // global.kmEvent?.resetPrevent()
  })
  mainWindow?.webContents?.on('did-finish-load', () => {
  })
  mainWindow.webContents.setWindowOpenHandler(({url}) => {
    if(!mainWindow || mainWindow.isDestroyed()) return
    console.log('[hook] setWindowOpenHandler')
    shell.openExternal(url)
    return { action: 'deny' }; // 阻止 Electron 打开新窗口
  })
  mainWindow.webContents.on('render-process-gone', (event, details) => {
    if (details.reason !== 'killed') {
      log.info('[mainWindow:render-process-gone]', details);
      // const CrashCommon = require('./crash-common/index')
      // CrashCommon.createWindow()
      if (details.reason !== 'clean-exit') {
        process.nextTick(() => {
          mainWindow.destroy()
        })
      }
    }
  });
}

/**
 * 设置应用参数
 */
function setAppParams () {
  app.commandLine.appendSwitch('autoplay-policy', 'no-user-gesture-required');
  app.commandLine.appendSwitch('ignore-certificate-errors')    //忽略证书的检测
  app.commandLine.appendSwitch('js-flags', '--expose-gc'); // 允许手动gc
  app.commandLine.appendSwitch('allow-file-access-from-files', '1'); // 允许访问本地文件资源
  app.commandLine.appendSwitch('disable-web-security', '1'); // 禁用同源策略（忽略跨域问题）
  app.commandLine.appendSwitch('ignore-certificate-errors', '1'); // 忽略与证书相关的错误
  app.commandLine.appendSwitch('disable-renderer-backgrounding')
  app.commandLine.appendSwitch('dns-result-order', 'ipv4first')
  // app.commandLine.appendSwitch('disable-features', 'SameSiteByDefaultCookies,CookiesWithoutSameSiteMustBeSecure,IntensiveWakeUpThrottling,AllowAggressiveThrottlingWithWebSocket'),
  app.commandLine.appendSwitch('disable-features', 'IntensiveWakeUpThrottling,AllowAggressiveThrottlingWithWebSocket,disable_idle_sockets_close_on_memory_pressure'),
  // sandbox会导致win11崩溃问题
  app.commandLine.appendSwitch('no-sandbox');
  app.commandLine.appendSwitch('--no-sandbox');
  log.info('[HardwareAcceleration]', main_config.setting_data.hardware_acceleration)
  if (!main_config.setting_data.hardware_acceleration) {
    app.commandLine.appendSwitch('disable-gpu');
    app.disableHardwareAcceleration();
    // 其余gpu相关参数
    app.commandLine.appendSwitch('disable-software-rasterizer');
    app.commandLine.appendSwitch('disable-gpu-compositing');
    app.commandLine.appendSwitch('disable-gpu-rasterization');
    app.commandLine.appendSwitch('disable-gpu-sandbox');
  } else {
    app.commandLine.appendSwitch('enable-gpu-rasterization');
    app.commandLine.appendSwitch('enable-webgl');
    app.commandLine.appendSwitch('enable-gpu');
    app.commandLine.appendSwitch('enable-software-rasterizer');
    app.commandLine.appendSwitch('enable-gpu-compositing');
    app.commandLine.appendSwitch('enable-gpu-sandbox');
  }
  if (main_config.ignore_proxy) {
    app.commandLine.appendSwitch('no-proxy-server') // 忽略代理
  }
}

function setAppEvent () {
  app.on('render-process-gone', (event, webContents, details) => {
    log.info('[app:render-process-gone]', details);
  });
  app.on('child-process-gone', (event, details) => {
    log.info('[app:child-process-gone]', details);
  });
  app.on('window-all-closed', function () {
    if (process.platform !== 'darwin') app.quit()
  })
  app.on('before-quit', function () {
    if (ipcRenderer) {
      ipcRenderer.removeAllListeners()
    }
    global.kmEvent?.stopWatch()
  })
  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    // 当运行第二个实例时,将会聚焦到mainWindow这个窗口
    if (mainWindow) {
      // 处理启动项中邀请参数
      log.info('[second-instance argv]', commandLine.slice(1))
    }
  })
}

// 处理启动参数
function handleArgs() {
  log.info('[process.argv]', argv)
  if (argv.tag) {
    userAgent = 'RylaiServiceTag/' + argv.tag
  }
  if (argv.auto) {
    isAutoStart = true
    if (main_config.setting_data.auto_launch_hide) {
      isSilent = true
    }
  }
}

function isEndpointProtected(requestURL) {
  try {
    const protectedEndpoints = [
      '/account/login/',
      '/account/login_code/',
      '/account/get_login_code/',
      '/account/heybox_pc/get_qr/',
      '/account/get_auth_info/'
    ];
    const parsedURL = new URL(requestURL);
    const pathname = parsedURL.pathname;
    return protectedEndpoints.some(endpoint => pathname.startsWith(endpoint));
  } catch (error) {
    log.error('解析请求URL失败:', error);
    return false;
  }
}

function ensurePopupManager() {
  if (!global.popupManager) {
    global.popupManager = require('./manager/popupManager');
    global.popupManager.initResizeWatch()
  }
}