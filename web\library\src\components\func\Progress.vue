<template>
  <div class="cpt-progress">
    <div class="progress-rail">
      <div
        class="progress-inner"
        :style="{
          width: `${progress}%`,
          background: `linear-gradient(to right, ${color_start} 0%, ${color_end} 100%)`,
        }"
      ></div>
    </div>
  </div>
</template>

<script setup name="Progress">
import { defineProps } from 'vue';

const props = defineProps({
  progress: {
    type: Number,
    default: 0,
    validator: (value) => {
      return value >= 0 && value <= 100;
    },
  },
  color_start: {
    type: String,
    default: 'green',
  },
  color_end: {
    type: String,
    default: 'green',
  },
});
</script>

<style lang="scss">
.cpt-progress {
  width: 100%;
  height: 4px;
  .progress-rail {
    width: 100%;
    height: 100%;
    background-color: var(
      --white-gamerecord-color-white-10a,
      rgba(255, 255, 255, 0.1)
    );
    border-radius: 2px;
    .progress-inner {
      height: 100%;
      border-radius: 9999px;
    }
  }
}
</style>
