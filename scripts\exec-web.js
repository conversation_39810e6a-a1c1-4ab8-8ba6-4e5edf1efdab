const { spawn } = require('child_process');

// 解析命令行参数
const args = process.argv.slice(2);
const mode = args.find(arg => arg === 'dev' || arg === 'build');
const pathArgs = args
  .filter(arg => arg.startsWith('--path='))
  .map(arg => arg.split('=')[1]);

if (!mode || !['dev', 'build'].includes(mode)) {
  console.error('请指定模式: dev 或 build');
  process.exit(1);
}

console.log('exec web', mode, pathArgs);

// 执行单个命令的函数
function executeCommand(pathArg) {
  return new Promise((resolve, reject) => {
    let command, args;
    
    if (pathArg === '*') {
      // 处理所有包的情况
      command = 'yarn';
      if (mode === 'dev') {
        args = ['workspaces', 'foreach', '--all', '-p', '-i', '--include', '@heybox-app-web/*', 'run', mode];
      } else {
        args = ['workspaces', 'foreach', '--all', '-p', '--include', '@heybox-app-web/*', 'run', mode];
      }
      console.log(`${mode === 'dev' ? 'Starting' : 'Building'} all @heybox-app-web/* packages...`);
    } else {
      // 处理单个包的情况
      command = 'yarn';
      args = ['workspace', `@heybox-app-web/${pathArg}`, 'run', mode];
      console.log(`${mode === 'dev' ? 'Starting' : 'Building'} @heybox-app-web/${pathArg}...`);
    }
    
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`命令执行失败，退出码: ${code}`));
      }
    });

    child.on('error', (err) => {
      reject(err);
    });
  });
}

// 执行所有命令
async function runCommands() {
  try {
    if (pathArgs.length > 0) {
      // 并行执行所有指定的包
      await Promise.all(pathArgs.map(pathArg => executeCommand(pathArg)));
    } else {
      // 如果没有指定包，处理所有包
      console.log(`${mode === 'dev' ? 'Starting' : 'Building'} all web paths...`);
      await executeCommand('*');
    }
  } catch (error) {
    console.error(`执行命令失败: ${error.message}`);
    process.exit(1);
  }
}

runCommands(); 