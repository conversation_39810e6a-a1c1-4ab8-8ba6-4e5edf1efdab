/**
 * Steam Games 接口封装
 * 用于与主进程的 Steam 服务通信
 */

class SteamGames {
  constructor() {
    this.isServiceStarted = false;
    this.downloadStatusCallbacks = new Set();
    this.currentDownloadStatus = null;
    this.startServicePromise = null;

    // 监听主进程的下载状态更新
    this.setupDownloadStatusListener();
  }

  /**
   * 设置下载状态监听器
   */
  setupDownloadStatusListener() {
    if (window.electronAPI && window.electronAPI.onSteamDownloadStatus) {
      window.electronAPI.onSteamDownloadStatus((downloadStatus) => {
        this.currentDownloadStatus = downloadStatus;

        // 通知所有注册的回调函数
        this.downloadStatusCallbacks.forEach(cb => {
          try {
            cb(downloadStatus);
          } catch (error) {
            console.error('[SteamGames] Download status callback error:', error);
          }
        });
      });
    }
  }

  /**
   * 注册下载状态变化回调
   */
  onDownloadStatusChange(callback) {
    if (typeof callback === 'function') {
      this.downloadStatusCallbacks.add(callback);

      // 如果已有当前状态，立即调用回调
      if (this.currentDownloadStatus) {
        callback(this.currentDownloadStatus);
      }
    }
  }

  /**
   * 取消下载状态变化回调
   */
  offDownloadStatusChange(callback) {
    this.downloadStatusCallbacks.delete(callback);
  }

  /**
   * 获取当前下载状态
   */
  getCurrentDownloadStatus() {
    return this.currentDownloadStatus;
  }

  /**
   * 启动 Steam 服务
   */
  async startService() {
    try {
      // 检查 steamGames 是否可用
      if (!window.steamGames) {
        throw new Error('Steam Games not available - window.steamGames is undefined');
      }

      const result = await window.steamGames.startService();
      if (result.success) {
        this.isServiceStarted = true;
      }
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to start service:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 停止 Steam 服务
   */
  async stopService() {
    try {
      const result = await window.steamGames.stopService();
      if (result.success) {
        this.isServiceStarted = false;
      }
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to stop service:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 确保服务已启动
   */
  async ensureServiceStarted() {
    if (!this.isServiceStarted) {
      // 如果已经有启动过程在进行中，等待它完成
      if (this.startServicePromise) {
        console.log('[SteamGames] Service startup already in progress, waiting...');
        return await this.startServicePromise;
      }

      // 启动 Promise 缓存
      this.startServicePromise = this._doStartService();

      try {
        await this.startServicePromise;
      } finally {
        this.startServicePromise = null;
      }
    }
  }

  /**
   * 实际执行服务启动的内部方法
   */
  async _doStartService() {
    const result = await this.startService();
    if (!result.success) {
      if (result.requiresSteamRestart) {
        // 若需要重启 Steam，给用户提示
        console.warn('[SteamGames] Steam restart required:', result.error);
        throw new Error('Steam restart required. Please restart Steam to enable game status detection.');
      }
      throw new Error(`Failed to start Steam service: ${result.error}`);
    }

    // 即使 Steam 连接失败，服务也可能已经启动
    if (result.steamConnectionFailed) {
      console.warn('[SteamGames] Service started but Steam connection failed:', result.steamError);
      this.isServiceStarted = true;
      throw new Error(`Failed to establish connection with Steam: ${result.steamError}`);
    }
  }

  /**
   * 检查游戏状态
   * @param {number} appId - Steam 应用 ID
   * @returns {Promise<Object>} 游戏状态信息
   */
  async checkGameStatus(appId) {
    try {
      await this.ensureServiceStarted();
      const result = await window.steamGames.checkGameStatus(appId);
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to check game status:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 安装游戏
   * @param {number} appId - Steam 应用 ID
   * @param {number} folderIdx - 安装文件夹索引（可选）
   * @returns {Promise<Object>} 安装结果
   */
  async installGame(appId, folderIdx) {
    try {
      await this.ensureServiceStarted();
      const result = await window.steamGames.installGame(appId, folderIdx);
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to install game:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 启动游戏
   * @param {number} appId - Steam 应用 ID
   * @returns {Promise<Object>} 启动结果
   */
  async runGame(appId) {
    try {
      await this.ensureServiceStarted();
      const result = await window.steamGames.runGame(appId);
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to run game:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 停止游戏
   * @param {number} appId - Steam 应用 ID
   * @returns {Promise<Object>} 停止结果
   */
  async terminateGame(appId) {
    try {
      await this.ensureServiceStarted();
      const result = await window.steamGames.terminateGame(appId);
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to terminate game:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取游戏信息
   * @param {number} appId - Steam 应用 ID
   * @returns {Promise<Object>} 游戏信息
   */
  async getAppInfo(appId) {
    try {
      await this.ensureServiceStarted();
      const result = await window.steamGames.getAppInfo(appId);
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to get app info:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取所有应用信息
   * @returns {Promise<Object>} 所有应用信息，包含游戏和快捷方式
   */
  async getAppsInfo() {
    try {
      await this.ensureServiceStarted();
      const result = await window.steamGames.getAppsInfo();
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to get apps info:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取下载概览
   * @returns {Promise<Object>} 下载概览信息
   */
  async getDownloadOverview() {
    try {
      await this.ensureServiceStarted();
      const result = await window.steamGames.getDownloadOverview();
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to get download overview:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 根据游戏状态返回按钮配置
   * @param {number} appId - Steam 应用 ID
   * @returns {Promise<Object>} 按钮状态配置
   */
  async getGameButtonStatus(appId) {
    try {
      try {
        await this.ensureServiceStarted();
        const gameInfo = await this.getAppInfo(appId);
        console.log('getAppInfo', gameInfo);
        

        if (gameInfo.success) {
          const { installed, status } = gameInfo;

          if (!installed) {
            return {
              status: 'toSteam',
              progress: 0,
              speed: ''
            };
          }

          // 检查下载状态
          const downloadOverview = await this.getDownloadOverview();
          if (downloadOverview.success && downloadOverview.downloading) {
            // 查找当前游戏的下载信息
            const currentDownload = downloadOverview.downloads?.find(d => d.appid === appId);
            if (currentDownload) {
              const isUpdate = currentDownload.update;
              const progress = Math.round((currentDownload.bytes_downloaded / currentDownload.bytes_total) * 100);
              const speed = this.formatSpeed(currentDownload.bytes_per_second);

              return {
                status: isUpdate ? 'updating' : 'installing',
                progress: progress,
                speed: speed
              };
            }
          }

          // 检查游戏是否正在运行
          if (status === 4) { // 4 表示正在运行
            return {
              status: 'running',
              progress: 0,
              speed: ''
            };
          }

          // 默认为准备启动状态
          return {
            status: 'ready',
            progress: 0,
            speed: ''
          };
        }
      } catch (steamError) {
        console.warn('[SteamGames] Steam service not available, falling back to local detection:', steamError.message);

        if (steamError.message.includes('Failed to establish connection with Steam')) {
          console.info('[SteamGames] Tip: Make sure Steam is running and restart Steam after enabling remote debugging');
        }
      }

      // 本地游戏检测
      const isLocallyInstalled = await this.checkLocalGameInstallation(appId);
      if (isLocallyInstalled) {
        return {
          status: 'ready',
          progress: 0,
          speed: ''
        };
      }

      // 如果本地也没有安装，显示前往 Steam
      return {
        status: 'toSteam',
        progress: 0,
        speed: ''
      };
    } catch (error) {
      console.error('[SteamGames] Failed to get game button status:', error);
      return {
        status: 'toSteam',
        progress: 0,
        speed: ''
      };
    }
  }

  /**
   * 检查游戏是否在本地安装
   * @param {number} appId - Steam 应用 ID
   * @returns {Promise<boolean>} 是否已安装
   */
  async checkLocalGameInstallation(appId) {
    try {
      //  Steam Manager 检查本地安装的游戏
      if (window.steamManager && window.steamManager.installedGames) {
        const installedGame = window.steamManager.installedGames.find(game => game.appid === appId);
        if (installedGame) {
          console.log('[SteamGames] Found locally installed game:', installedGame);
          return true;
        }
      }

      if (window.store && window.store.state.steam_games) {
        const gameInStore = window.store.state.steam_games.find(game => game.appid === appId);
        if (gameInStore && gameInStore.installed) {
          console.log('[SteamGames] Found game in store as installed:', gameInStore);
          return true;
        }
      }

      return false;
    } catch (error) {
      console.error('[SteamGames] Failed to check local game installation:', error);
      return false;
    }
  }

  /**
   * 格式化下载速度
   * @param {number} bytesPerSecond - 每秒字节数
   * @returns {string} 格式化的速度字符串
   */
  formatSpeed(bytesPerSecond) {
    if (!bytesPerSecond) return '';
    
    const mbps = bytesPerSecond / (1024 * 1024);
    if (mbps >= 1) {
      return `${mbps.toFixed(1)}MB/s`;
    }
    
    const kbps = bytesPerSecond / 1024;
    return `${kbps.toFixed(1)}KB/s`;
  }

  /**
   * 添加快捷方式
   * @param {Object} options - 快捷方式选项
   * @param {string} options.name - 快捷方式名称
   * @param {string} options.exe - 可执行文件路径
   * @param {Array<string>} [options.launchOptions] - 启动选项
   * @param {string} [options.icon] - 图标路径
   * @param {string} [options.startDir] - 启动目录
   * @returns {Promise<Object>} 操作结果
   */
  async addShortcut(options) {
    try {
      console.log('[SteamGames] Adding shortcut:', options);
      const result = await window.steamGames.addShortcut(options);
      console.log('[SteamGames] Add shortcut result:', result);
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to add shortcut:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 移除快捷方式
   * @param {number} appId - Steam 应用 ID
   * @returns {Promise<Object>} 操作结果
   */
  async removeShortcut(appId) {
    try {
      console.log('[SteamGames] Removing shortcut:', appId);
      const result = await window.steamGames.removeShortcut(appId);
      console.log('[SteamGames] Remove shortcut result:', result);
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to remove shortcut:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 创建桌面快捷方式
   * @param {number} appId - Steam 应用 ID
   * @returns {Promise<Object>} 操作结果
   */
  async createDesktopShortcut(appId) {
    try {
      await this.ensureServiceStarted();
      console.log('[SteamGames] Creating desktop shortcut for app:', appId);
      const result = await window.steamGames.createDesktopShortcut(appId);
      console.log('[SteamGames] Create desktop shortcut result:', result);
      return result;
    } catch (error) {
      console.error('[SteamGames] Failed to create desktop shortcut:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 处理按钮点击事件
   * @param {string} status - 当前按钮状态
   * @param {number} appId - Steam 应用 ID
   * @returns {Promise<Object>} 操作结果
   */
  async handleButtonClick(status, appId) {
    try {
      switch (status) {
        case 'ready':
          return await this.runGame(appId);

        case 'running':
          return await this.terminateGame(appId);

        case 'toSteam':
          return await this.installGame(appId);

        case 'installing':
        case 'updating':
          // 下载和更新不执行操作
          return { success: false, error: 'Operation not allowed in current state' };

        default:
          return { success: false, error: 'Unknown status' };
      }
    } catch (error) {
      console.error('[SteamGames] Failed to handle button click:', error);
      return { success: false, error: error.message };
    }
  }
}

const steamGames = new SteamGames();

export default steamGames;
