<template>
  <div class="cpt-news-item-header">
    <Avatar
      :avatar="user.avatar"
      :decoration="user.decoration"
      :showDecoration="true"
      :width="68"
    />
    <div class="user-info">
      <div class="user-name">{{ user.username }}</div>
      <NewsHeyboxLevel :levelInfo="user.level_info" />
    </div>
    <slot />
  </div>
</template>

<script setup name="NewsItemHeader">
import Avatar from '@/components/func/Avatar.vue';
import NewsHeyboxLevel from './components/NewsHeyboxLevel.vue';
import { defineProps } from 'vue';

const props = defineProps({
  user: {
    type: Object,
    default: () => {},
  },
});
</script>
<style lang="scss">
// 这里所有的距离相关的都放大了两倍 因为这里有一个7px的level标签 需要做整体缩放
.cpt-news-item-header {
  display: flex;
  align-items: center;
  gap: 20px;
  width: 200%;
  height: 34px;

  transform: scale(0.5);
  transform-origin: left center;

  .level-tag-wrapper {
    margin-right: -8px;
  }

  .cpt-account-avatar {
    .user-avatar {
      border-radius: 50%;
      img {
        border-radius: 50%;
      }
    }
  }
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    width: fit-content;
    max-width: 50%;
    .user-name {
      color: 1111111;
      font-family: 'PingFang SC';
      font-size: 28px;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .extension-content {
    display: flex;
    align-items: center;
    gap: 12px;

    .text,
    .icon {
      white-space: nowrap;
    }

    .text {
      color: var(---general-color-text-2, #64696e);
      font-size: 28px;
    }

    .icon {
      width: 32px;
      height: 32px;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .time {
      color: var(---general-color-text-3, #8c9196);
      font-size: 22px;
    }
  }
}
</style>
