const { ipcMain } = require('electron')
const { requireAddon, log, eventBus: EventBus, store } = require('@heybox/electron-utils')
const request = require('@heybox/electron-utils/request')

const { 
  STEAM_INJECT_STATE, 
  TASK_BLACKLIST, 
  VALORANT_GAME_INFO,
  MUSIC_APP_DEFAULT_NAME,
  MUSIC_REARRANGE_STRINGS,
} = require('./assets/constant')
const steam_inject = require('@heybox/node-inject').steam
const {
  checkIsBiliBiliLive,
  getMiniMapKeysByProcessName,
  isNeedListenMiniProgramProcessState,
  // reportGamesCount,
  getLowerName,
} = require('./utils')
const {
  generateRegexList,
  checkIsNeedToRearrange,
} = require('./music')
const {
  matchOfficialGame,
  matchUserGame,
  getHigherPriorityProcessMatch,
  filterProcessBlackList,
} = require('./match')

const {
  registerInterval,
  getProcessList,
  getFilteredAppWindowInfo,
  getAppWindowInfo,
  SYSTEM_WINDOW_TYPE,
} = require('@heybox/electron-utils')

global.mini_map = {}
global.mini_program_bind_process_state = {}
global.steam_inject_state = STEAM_INJECT_STATE.PENDING
global.bilibili_living = false
global.process_blacklist = {}
global.game_process_map = {}

let user_game_process_map = {}
let inject_games_white_list = []
let oneMinInterval = null
let getRemoteProcessListPromise = null
let MUSIC_REARRANGE_REGEX_LIST = null

// TODO
// if (global.ELECTRON_ENV !== 'prod') {
//   process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
// }

class Task_Iterate {
  constructor() {
    this._intervalInited = false
    this.isInjectSteamRegister = false
    this.steamInjectCrash = false
    this.application_state = {}
    this.steamGameStateFilters = []
    this.initGameCounts = false
    ipcMain.handle('addon:getAppWindowInfo', getAppWindowInfo)
    ipcMain.handle('addon:getProcessPathByWindowID', getProcessPathByWindowID)
    ipcMain.handle('addon:getProcessList', getProcessList)
    ipcMain.handle('addon:getFilteredAppWindowInfo', getFilteredAppWindowInfo)
    ipcMain.handle('get-application-state', this.getUserApplicationState.bind(this))
    ipcMain.handle('bilibili:get-state', () => global.bilibili_living)
    EventBus.on('steam_hook:steam_game_state', this.handleApplicationStateChange.bind(this))
  }

  // 初始化进程循环监听
  initProcessInterval() {
    // 初始化addon
    // 如果已经初始化过，立刻触发一次检测轮训
    if (this._intervalInited) {
      this.reportMusicState()
      return
    }

    this._intervalInited = true
    // 先获取远端的游戏map
    let promise = this.getRemoteProcessList()
    if (!getRemoteProcessListPromise) {
      getRemoteProcessListPromise = promise
    }
    this.reportMusicState()
  }

  async getRemoteProcessList() {
    try {
      let res = await Promise.all([
        request.$get('/chatroom/v2/account/user_games', {}, 'heychat'),
        request.$get('/chatroom/v2/games', {}, 'heychat'),
        request.$get('/chatroom/v2/minipro', {}, 'heychat'),
      ])
      let [res1, res2, res3] = res
      if (res1.status === 'ok') {
        user_game_process_map = res1.result.user_games
      }
      if (res2.status === 'ok') {
        let { game_list = {}, inject_games = [], process_blacklist = {} } = res2.result
        game_process_map = game_list
        inject_games_white_list = inject_games
        global.process_blacklist = process_blacklist
      }
      if (res3.status === 'ok') {
        let { mini_pro_enable = [], minipro_infos = {} } = res3.result
        global.mini_map = minipro_infos
        store.set('miniprogram_setting', mini_pro_enable)
        !global.MiniProgram && (global.MiniProgram = require('@heybox/mini-program'))
        const { onMiniProgramEnableChange } = require('@heybox/mini-program/bind_eventbus')
        onMiniProgramEnableChange(mini_pro_enable)
        const { preRequireMinipro } = require('@heybox/mini-program/pre_require')
        preRequireMinipro(global.mini_map)
      }

      this.startIntervalTask()
    } catch(e){
      console.error(e);
      setTimeout(() => {
        this.getRemoteProcessList()
      }, 60 * 1000)
    }
  }

  startIntervalTask() {
    registerInterval(SYSTEM_WINDOW_TYPE.COMMON, this.iterateWindowProcess.bind(this), true)

    if(oneMinInterval){
      clearInterval(oneMinInterval)
      oneMinInterval = null
    }
    const task = () => {
      steam_inject.injectSteam()
      this.bilibiliStateCheck()
    }
    task()
    oneMinInterval = setInterval(() => {
      task()
    }, 1000 * 60)
  }

  reportProcess(game) {
    const { process_path, game_name } = game

    // 瓦罗兰特 & 无畏契约特殊区分逻辑
    if (VALORANT_GAME_INFO.find(info => info.name === game_name)) {
      let val_info = VALORANT_GAME_INFO.find(info => process_path.includes(info.path))
      if (val_info) {
        game.game_name = val_info.name
      }
    }
    
    this.handleApplicationStateChange('game', {...game, process_path: process_path?.toLowerCase()})
  }

  async iterateWindowProcess(window_list) {
    // v1.37.1 防止 steam hook 导致 怪物猎人荒野启动时崩溃 
    // 由于傻逼卡普空的bug，使用steam注入会导致 街霸6和怪物猎人荒野启动时直接崩溃
    if (window_list.some(v => v.process_name.toLowerCase() === 'monsterhunterwilds.exe')) {
      if (store.get('user_account_settings.allow_steam_nethook') == 1) {
        log.info('[closeSteamNethook wilds process]', main_config.wilds_close_steam_nethook)
        store.set('user_account_settings.allow_steam_nethook', 0)
        store.setLocalConfig('wilds_close_steam_nethook', 1)
        mainWindow?.webContents?.send('localstorage:refresh', 'main_config', global.main_config)
        request.$post('/chatroom/v2/settings/account/update', {}, {
          setting: 'allow_steam_nethook',
          value: 0
        })
      }
    }

    // 检测游戏状态
    let result = this.getFirstGameProcess(window_list)
    console.log('[iterateWindowProcess getFirstGameProcess]', result)
    if (result) {
      let {processItem, game} = result
      this.reportProcess({...processItem, ...game})
    } else {
      this.reportProcess({ game_name: ''})
    }

    miniProProcessCheck(window_list)
    this.getTotalGameCounts(window_list)
  }

  // 获取第一个音乐软件进程信息
  reportMusicState () {
    clearTimeout(this._reportMusicStateLoop)
    requireAddon("get_endpoint_info").enumMusicAppInfo((res) => {
      res = res.filter(v => {
          return !MUSIC_APP_DEFAULT_NAME.includes(v.window_title)
      })
      main_config.musicLog && log.info('[enumMusicAppInfo]:', res)
      const firstItem = res[0] || null
      // 如果是同一个软件，则不重置定时器
      if(firstItem){
        if(firstItem.window_handle !== this.application_state?.music?.window_handle){
          this.getCurMusicAppState(firstItem)
        }
      } else {
        this.handleApplicationStateChange('music', { music_name: '' })
        clearTimeout(this._checkMusicAppWindowInfoLoop)
      }
    })
    this._reportMusicStateLoop = setTimeout(() => {
      this.reportMusicState()
    }, 60 * 1000)
  }

  // 重新设置一个定时器，定时获取当前音乐软件的播放信息
  getCurMusicAppState(musicInfo){
    clearTimeout(this._checkMusicAppWindowInfoLoop)
    musicInfo = musicInfo || this.application_state?.music
    if(!musicInfo) return
    let { title: music_name = '' } = requireAddon("get_endpoint_info").getWindowInfo(musicInfo.window_handle)
    if (music_name) {
      let afterGenerate
      if(MUSIC_REARRANGE_REGEX_LIST) {
        afterGenerate = MUSIC_REARRANGE_REGEX_LIST
      } else {
        afterGenerate = MUSIC_REARRANGE_STRINGS.map(str => generateRegexList(str))
        MUSIC_REARRANGE_REGEX_LIST = afterGenerate
      }
      music_name = checkIsNeedToRearrange(music_name, afterGenerate)
      main_config.musicLog && log.info('[music_name]:', music_name)
      this.handleApplicationStateChange('music', {...musicInfo, music_name})
      this._checkMusicAppWindowInfoLoop = setTimeout(()=>{
        this.getCurMusicAppState()
      }, 20 * 1000)
    }else{
      this.handleApplicationStateChange('music', { music_name: '' })
    }
  }

  checkIsCanShowOverlay(filePath){
    if(!user_game_process_map) return true
    const lowerPath = filePath.toLowerCase()
    const item = user_game_process_map[lowerPath]
    if(item && !item.overlay){
      return false
    } else {
      return true
    }
  }

  updateUserGames(v){
    user_game_process_map = v || {}
    this.startIntervalTask()
  }

  handleApplicationStateChange(key, data) {
    this.application_state[key] = data
    EventBus.emit('change-application-state', {key, data, application_state: this.application_state})
    global.mainWindow && global.mainWindow.webContents.send('change-application-state', this.application_state)
  }

  // 获取processList中排序第一的游戏进程
  getFirstGameProcess = (processList, useFilterShow = true) => {
    processList = filterProcessBlackList(processList)
    
    let final_match_result = null

    for (let p of processList) {
      const user_data = matchUserGame(p, user_game_process_map)
      const official_data = matchOfficialGame(p, game_process_map)
      if (!user_data && !official_data) continue
      if (useFilterShow && user_data && !user_data.game.show) continue
      // console.log(official_data)
      // console.log(user_data)
      const title = official_data?.game?.title || user_data?.processItem?.title
      const match_result = {
        ...official_data || {},
        ...user_data || {},
      }
      match_result.game.title = title
      final_match_result = getHigherPriorityProcessMatch(final_match_result, match_result)
    }

    return final_match_result
  }

  getTotalGameCounts(processList) {
    if (this.initGameCounts) return
    this.initGameCounts = true
    if(!processList.length) return
    const list = []

    for(let i = 0; i < processList.length; i++){
      const p = processList[i]
      if (matchOfficialGame(p, game_process_map)){
        list.push(p.pid) 
      }
    }
    // reportGamesCount([...new Set(list)].length)
  }

  async bilibiliStateCheck() {
    let process_list = await getProcessList()
    let bilibili_state = false
    for (let process of process_list) {
      if (checkIsBiliBiliLive(process)) {
        bilibili_state = true
        break
      }
    }
  
    // 更新b站主播状态
    if (global.bilibili_living !== bilibili_state) {
      global.bilibili_living = bilibili_state
      if (!mainWindow.isDestroyed() && mainWindow.webContents) {
        mainWindow.webContents.send('bilibili:live-state-change', global.bilibili_living)
      }
      log.info('[bilibili state change]', global.bilibili_living)
    }
  }

  // 检索当前窗口进程 获取所有可以用的浮窗小程序
  async getAvaliableOverlayMiniPro (window) {
    // getAvaliableOverlayMiniPro需要等getRemoteProcessList接口返回后再执行
    if (getRemoteProcessListPromise) {
      await getRemoteProcessListPromise
    } 

    let checkFunc = (p) => {
      let process_name = (p.process_name || p.processName)?.toLowerCase()
      return getMiniMapKeysByProcessName(process_name)
    }

    if (!window) {
      let processList = filterProcessBlackList(await getAppWindowInfo()), result = []
      for (let p of processList) {
        result = [...result, ...checkFunc(p)]
      }
      return result
    } else {
      return checkFunc(window)
    }
  }

  // 检测窗口数据是否允许注入
  checkIsInjectWhiteListProcess(data) {
    // 兼容不同数据来源数据，后续统一addon返回数据格式
    let game_process_name = data.processName || data.process_name
    let game_window_name = data.title || data.windowName || data.window_name
    return inject_games_white_list.some((obj => {
      // 如果process_name和window_name都没有认为是错误数据
      if (!(obj.process_name || obj.window_name)) return false;
      let process_name_match = !obj.process_name || obj.process_name === game_process_name
      let window_name_match = !obj.window_name || obj.window_name === game_window_name
      return process_name_match && window_name_match
    }))
  }

  checkIsGame(process) {
    if(!process) return
  
    // 数据字段格式兼容
    const real_process_name = process.process_name || process.processName || ''
    const real_window_name = process.title || process.windowName || process.window_name || ''
    if (TASK_BLACKLIST.find(black => getLowerName(real_process_name).includes(black))) {
      return false
    }
  
    return !!matchOfficialGame({
      process_name: real_process_name,
      window_name: real_window_name,
    }, game_process_map)
  }

  // 获取注入游戏白名单
  getInjectGamesWhiteList() {
    return inject_games_white_list
  }
  getUserApplicationState() {
    return this.application_state
  }
}

function miniProProcessCheck(window_list) {
  let started_process_mini_pro_ids = []
  window_list.forEach(process => {
    started_process_mini_pro_ids.push(...getMiniMapKeysByProcessName(process.process_name))
  });

  // 更新小程序绑定的游戏启动状态
  for (let mini_pro_id in global.mini_map) {
    let mini_data = global.mini_map[mini_pro_id]
    if (isNeedListenMiniProgramProcessState(mini_data)) {
      let newState = started_process_mini_pro_ids.includes(mini_pro_id)
      let oldState = !!mini_program_bind_process_state[mini_pro_id]
      if (newState != oldState) {
        // 检测小程序绑定游戏的状态是否变化
        mini_program_bind_process_state[mini_pro_id] = newState
        !global.MiniProgram && (global.MiniProgram = require('../mini-program/main'))
        EventBus.emit('mini-program:bind-process-exist-change', { mini_pro_id, process_exit: newState })
      }
    }
  }
}

function getProcessPathByWindowID(_, id) {
  let res = requireAddon("get_endpoint_info").getProcessPathByWindowID(parseInt(id))
  log.info('[getProcessPathByWindowID]', id, '-', res)
  return res
}

module.exports = new Task_Iterate()
