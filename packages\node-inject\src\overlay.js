
const { BrowserWindow, screen } = require('electron')
const { log, store } = require('@heybox/electron-utils')
const request = require('@heybox/electron-utils/request')
const utils = require('@heybox/electron-utils')
const tastIterate = require('@heybox/process-iterate')

const DEFAULT_OFFSCREEN_WINDOW_CONFIG = {
  x: 0, 
  y: 0, 
  minimizable: false, 
  maximizable: false, 
  show: false, 
  frame: false, 
  skipTaskbar: true, 
  transparent: true,
  followInjectWindowSize: true, // 窗口宽高自动跟随窗口宽高
  ignoreMouseEvents: false, // 是否忽略鼠标事件
  frameRate: 30, // 默认30帧，高帧数会有内存压力
}

const EVENT_MANAGER = require('./event')

class Node_Inject {
  constructor() {
    this.injectedWindows = {}
  }
   /**
   * 将某窗口注入到游戏窗口中
   * 
   * @param {Object} config - 配置对象
   * @param {BrowserWindow} config.window - 要注入的Electron BrowserWindow实例
   * @param {Object} config.gameWin - 游戏窗口对象，包含processId和windowId
   * @param {number} config.gameWin.processId - 游戏窗口的进程ID
   * @param {number} config.gameWin.windowId - 游戏窗口的窗口ID
   * @param {Object} [config.listenEvents={}] - 一个对象，键是事件名称，值是处理这些事件的回调函数
   * @param {Object} [config.captionRect={}] - 可拖动窗口的矩形区域对象， left\right\top\height
   * @param {Object} [config.dragBorderWidth=0] - 添加窗口边框用了拖拽窗口大小
   * @param {Object} [config.rect={}] - 实际注入的x,y,width,height x和y 以被注入窗口左上角为0点
   * @param {boolean} [config.mouse='none'] - none 不捕获鼠标，游戏鼠标正常 | capture 捕获鼠标，游戏鼠标不可用  | deliver 捕获鼠标，游戏鼠标可用
   * @param {boolean} [config.keyboard='none'] - 键盘采集 同mouse属性
   * @param {boolean} [config.transparent=true] - 是否晋档鼠标位于非完全透明元素时才捕获游戏键鼠输入
   * @param {boolean} [config.visible=true] - 是否显示窗口
   * @param {boolean} [config.name=''] - 名称无特殊含义 仅有可读性
   * 
   * @param {boolean} [config.minWidth=100] - 最小宽高，限制图片拽窗口大小时的最小宽度
   * @param {boolean} [config.minHeight=100]
   * 
   * @param {boolean} [config.maxWidth] - 最大宽高，限制overlay显示的最大宽度，超过宽度的像素不渲染，默认为屏幕宽高
   * @param {boolean} [config.maxWidth]
   * 
   * @param {boolean} [config.autoMoveInGamwWindow=true] - 注入时是否保证注入窗口在游戏窗口内部
   * @param {boolean} [config.interceptMouse=false] - 注入时是否开启游戏内鼠标，用于在fps游戏中锁定准星的情况下获取鼠标
   * 
   * 
   * @returns {Promise} - 当窗口成功注入时resolve，注入失败时reject
   */
  injectWindow(config) {
    return new Promise(async (resolve, reject) => {
      let { 
        window, 
        gameWin = {}, 
        listenEvents = {}, 
      } = config

      if (!window || window.isDestroyed()) return

      let pid = gameWin.processId
      if (!pid) {
        throw new Error("Invalid gameWin, processId must be provided")
      }
      let inject_result_check_interval = null, inject_retry_times = 0 // 注入重试循环
      const {
        addOverlayWindow,
        injectByWindow,
        enableOverlay,
        interceptInput,
        gameProcessPids,
        clearScaleFactorCache,
      } = require("./addon");
      let windowId = window.id
      utils.fixWindowId(window)
      if (!this.injectedWindows[pid]) {
        this.injectedWindows[pid] = [window]
      } else {
        this.injectedWindows[pid].push(window)
      }

      let resolveFunc = (res) => {
        clearInterval(inject_result_check_interval)
        inject_result_check_interval = null
        enableOverlay(pid, true)
        if (config.interceptMouse) {
          interceptInput(true)
        }
        resolve(res)
      }
      let rejectFunc = (res) => {
        clearInterval(inject_result_check_interval)
        inject_result_check_interval = null
        clearScaleFactorCache(gameWin)
        reject(res)
      }
      let events = this.createInjectEvents(window, listenEvents, resolveFunc, gameWin)
      EVENT_MANAGER.register(pid, windowId, events)
      window.on("closed", () => {
        this.clearInjectEvent(pid, window.id)
        clearScaleFactorCache(gameWin)
        if (config.interceptMouse) {
          interceptInput(false)
        }
      });

      // 跟随注入窗口宽高
      if (config.followInjectWindowSize) {
        if (!config.rect) {
          config.rect = {}
        }
        let { width, height } = this.getGameWindowSize(gameWin)
        config.rect.width = width
        config.rect.height = height
      }

      // 设置Title为StatusBar，窗口失焦时不会自动取消注入，其余Title命名都是普通窗口
      addOverlayWindow(config);
      let res = await injectByWindow(gameWin);
      log.info("[overlay injectByWindow]", res);
      if (res.result) {
        // 如果注入cs2需要特殊提示
        checkShowCs2Notice(gameWin)
        // injectByWindow返回success不代表注入成功了，"game.process"事件才代表真的注入成功
        // "game.process"事件同一个游戏进程只会触发一次，如果已经触发过就直接resolve
        if (gameProcessPids.includes(pid)) {
          resolveFunc()
        } else {
          // 如果"game.process"未触发，则每20s尝试一次注入，最多5次
          inject_result_check_interval = setInterval(async () => {
            if (window.isDestroyed()) {
              rejectFunc('window_destroyed')
            } else if (inject_retry_times < 5) {
              log.info("[overlay inject retry]", inject_retry_times, window.id);
              await injectByWindow(gameWin);
              inject_retry_times ++
            } else {
              rejectFunc('timeout')
            }
          }, 1000 * 20)
        }
      } else {
        rejectFunc(res.reason)
      }

      if (main_config.openDevTools) {
        setTimeout(() => {
          window?.webContents?.openDevTools()
        }, 300)
      }
    })
  }

  /**
   * 注入指定游戏，并监听回调
   * 
   * @param {Object} config - 配置对象
   * @param {Object} config.gameWin - 游戏窗口对象，包含processId和windowId
   * @param {number} config.gameWin.processId - 游戏窗口的进程ID
   * @param {number} config.gameWin.windowId - 游戏窗口的窗口ID
   * @param {Object} [config.listenEvents={}] - 一个对象，键是事件名称，值是处理这些事件的回调函数
   * @param {String} config.id - 此次注入监听唯一id标识
   * 
   * @returns {Promise} - 当窗口成功注入时resolve pid，注入失败时reject
   */
  injectGame(config) {
    return new Promise(async (resolve, reject) => {
      log.info('[injectGame start]')
      let { gameWin = {}, listenEvents = {}, id, offset = {} } = config

      let pid = gameWin.processId
      if (!pid) {
        throw new Error("Invalid gameWin, processId must be provided")
      }
      if (!id) {
        throw new Error("Invalid config, id must be provided")
      }
      let inject_result_check_interval = null, inject_retry_times = 0 // 注入重试循环
      const {
        startNode,
        injectByWindow,
        gameProcessPids,
      } = require("./addon");
      startNode()

      let resolveFunc = () => {
        log.info('[injectGame resolveFunc]')
        clearInterval(inject_result_check_interval)
        inject_result_check_interval = null
        resolve(pid)
      }
      let rejectFunc = (res) => {
        clearInterval(inject_result_check_interval)
        inject_result_check_interval = null
        reject(res)
      }

      if (listenEvents['game.process']) {
        listenEvents['game.process'] = (payload) => {
          listenEvents['game.process'](payload)
          resolveFunc()
        }
      } else {
        listenEvents['game.process'] = resolveFunc
      }

      EVENT_MANAGER.register(pid, id, listenEvents)

      let res = await injectByWindow(gameWin);
      log.info("[overlay injectGame]", res);
      if (res.result) {
        // injectByWindow返回success不代表注入成功了，"game.process"事件才代表真的注入成功

        // "game.process"事件同一个游戏进程只会触发一次，如果已经触发过就直接resolve
        if (gameProcessPids.includes(pid)) {
          resolveFunc()
        } else {
          // 如果"game.process"未触发，则每20s尝试一次注入，最多5次
          inject_result_check_interval = setInterval(async () => {
            if (inject_retry_times < 5) {
              log.info("[overlay inject retry]", inject_retry_times);
              await injectByWindow(gameWin);
              inject_retry_times ++
            } else {
              rejectFunc('timeout')
            }
          }, 1000 * 20)
        }
      } else {
        rejectFunc(res.reason)
      }
    })
  }
  // 创建离屏窗口
  createOffscreenWindow(config, gameWin) {
    if (config.x !== undefined || config.y !== undefined) {
      console.warn('[createOffscreenWindow warn] The coordinates of the off screen window will not affect the coordinates of the injection window. The injection window coordinates need to be passed in injectWindow function as config.rect')
    }
    const rect = this.getGameWindowSize(gameWin)
    const match_screen = screen.getDisplayMatching(rect)
    config = {
      ...DEFAULT_OFFSCREEN_WINDOW_CONFIG,
      ...config,
      x: match_screen.bounds.x,
      y: match_screen.bounds.y,
    }
    
    config.webPreferences = {
      ...config.webPreferences || {},
      offscreen: utils.getUseSharedTexture() 
        ? { useSharedTexture: true } 
        : true
    }
    if(!config.processName && gameWin.processName) {
      config.processName = gameWin.processName
    }
    // 跟随注入窗口宽高
    if (config.followInjectWindowSize) {
      let { width, height } = this.getGameWindowSize(gameWin)
      config.width = width
      config.height = height
    }
    let window = new BrowserWindow(config)
    // 额外调用一次setBounds设置宽高，防止electron默认创建窗口不能超过主屏的宽高bug
    if (config.width) {
      window.setBounds({
        width: config.width,
      })
    }
    if (config.height) {
      window.setBounds({
        height: config.height,
      })
    }
    // const { getWindowPhysicalBounds } = require('./addon')
    // console.log('createOffscreenWindow', config, window.getBounds(), getWindowPhysicalBounds(window))
    window.followInjectWindowSize = config.followInjectWindowSize
    window.ignoreMouseEvents = config.ignoreMouseEvents
    window.webContents.setFrameRate(config.frameRate)
    window._offScreen = true
    return window
  }
  // 创建注入事件回调
  createInjectEvents(window, listenEvents, resolve, gameWin) {
    let events = {}
    for (const [key, func] of Object.entries(listenEvents)) {
      switch (key) {
        case "graphics.window.event.resize":
          // 如果配置了跟随注入窗口宽高，在resize事件回调中处理离屏窗口宽高
          if (window.followInjectWindowSize) {
            events["graphics.window.event.resize"] = (payload) => {
              const { screenToDip } = require("./addon");
              this.setInjectSize(window, {
                width: screenToDip(payload.width, gameWin),
                height: screenToDip(payload.height, gameWin)
              })
              func && func(payload)
            }
          } else {
            events["graphics.window.event.resize"] = func
          }
          break
        case "game.process":
          events["game.process"] = (payload) => {
            resolve()
            func && func(payload)
          }
          break
        default:
          if (func) {
            events[key] = func
          }
          break
      }
    }

    // game.process需要触发injectWindow的resolve，如果未赋值，收到赋值resolve
    if (!events["game.process"]) {
      events["game.process"] = resolve
    }

    if (!events["graphics.window.event.resize"] && window.followInjectWindowSize) {
      events["graphics.window.event.resize"] = (payload) => {
        const { screenToDip } = require("./addon");
        this.setInjectSize(window,  {
          width: screenToDip(payload.width, gameWin),
          height: screenToDip(payload.height, gameWin)
        })
      }
    }

    let dmpReportFunc = (payload) => {
      if (payload.reason === 'client_lost' && store.get('global_config.switches.upload_inject_dump')) {
        try {
          // TODO: 需要封装sentry
          // require('../process/sentry_init').uploadRecentAddonDmpFile()
        } catch (e) {
          console.error(e)
        }
      }
    }
    if (!events["game.connection.close"]) {
      events["game.connection.close"] = dmpReportFunc
    } else {
      let func = events["game.connection.close"]
      events["game.connection.close"] = (payload) => {
        func(payload)
        dmpReportFunc(payload)
      }
    }
    return events
  }
  // 清除窗口注入事件
  clearInjectEvent(pid, windowId) {
    EVENT_MANAGER.unregister(pid, windowId)
    if (this.injectedWindows[pid]) {
      this.injectedWindows[pid] = this.injectedWindows[pid].filter(w => (w._id || w.id) !== windowId)
    }
    if (!this.injectedWindows[pid] || this.injectedWindows[pid].length === 0) {
      const {enableOverlay} = require("./addon");
      enableOverlay(pid, false)
    }
  }
  // 设置窗口宽高
  setInjectSize(window, {width, height, pid}) {
    console.log("[overlay evnet]:graphics.window.event.resize", width, height, window.getBounds());
    if (window && !window.isDestroyed()) {
      window.setBounds({width, height});
    }
  }
  // 获取首个游戏窗口
  getFirstGameWindow(top_window_list) {
    const { getFirstGameProcess, checkIsInjectWhiteListProcess } = tastIterate
    let window_list = []
    if (!top_window_list) {
      top_window_list = this.getTopWindows()
    }
    if (main_config.injectWindowLog) {
      log.info('top_window_list', top_window_list)
    }
    // 遍历所有窗口，找出进程和窗口名符合白名单的窗口，push到window_list中
    top_window_list.forEach(data => {
      const isInWhite = checkIsInjectWhiteListProcess(data)
      // 是注入白名单 然后也是自定义里面使用注入模式的才可以将其加入到window_list中
      if (isInWhite) {
        window_list.push({
          ...data,
          process_name: data.processName,
          process_path: data.processPath,
        })
      }
      return data
    })
    if (window_list.length === 0) {
      return false
    }
    const firstGameProcess = getFirstGameProcess(window_list, false)

    return firstGameProcess?.processItem
  }
  // 获取所有游戏窗口
  getAllGameWindow() {
    return this.getTopWindows().filter((topWindow) => {
      const { title, processPath } = topWindow
      return checkIsGameWindow({ process_name: title, process_path: processPath })
    })
  }
  // 获取顶层窗口
  getTopWindows() {
    const { getTopWindows } = require("./addon");
    return getTopWindows()
  }
  getGameWindowSize(gameWin) {
    const { getWindowBounds, screenToDip } = require("./addon");
    let rect = getWindowBounds(gameWin.windowId)
    return {
      width: screenToDip(rect.width, gameWin),
      height: screenToDip(rect.height, gameWin),
      x: screenToDip(rect.x, gameWin),
      y: screenToDip(rect.y, gameWin),
      screenRect: rect,
    }
  }
  registerEvent(pid, windowId, listenEvents) {
    EVENT_MANAGER.register(pid, windowId, listenEvents)
  }
  unregisterEvent(pid, windowId, event) {
    EVENT_MANAGER.unregister(pid, windowId, event)
  }
  async getOffsets(key, version) {
    const res = await request.$get('/chatroom/v2/minipro/offset', { key, game_version: version })
    if (res.result?.offset) {
      return utils.base64ToBuffer(res.result?.offset)
    }
  }
}

function checkIsGameWindow(item) {
  const { checkIsGame } = tastIterate
  return checkIsGame(item)
}

function checkShowCs2Notice({ processName }) {
  if (processName === 'cs2.exe' && mainWindow && !store.get('is_cs2_inject_notice_showed')) {
    store.set('is_cs2_inject_notice_showed', true)
    utils.focusWindow(mainWindow)
    mainWindow.webContents.send('show_cs2_inject_notice')
  }
}


module.exports = new Node_Inject()
