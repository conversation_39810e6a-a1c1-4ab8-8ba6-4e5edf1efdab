const path = require('path')
const fs = require('fs')
const GUILD_NOTICE_FREQUENCE = 1000 * 60 * 60 * 24 * 7 // guild弹窗的弹出频率是7天提示一次
const { log, store } = require('@heybox/electron-utils')
const request = require('@heybox/electron-utils/request')
const tastIterate = require('@heybox/process-iterate')

// 判断当前小程序id对应小程序instance是否存在
function checkIsMiniProInstanceExists(moduleName) {
  return global.MiniProgram && !!global.MiniProgram.getInstance(moduleName)
}

// 检测窗口进程是否在注入白名单
function checkIsInjectWhiteList(win) {
  const { checkIsInjectWhiteListProcess } = tastIterate
  return checkIsInjectWhiteListProcess(win)
}

// 接口获取notice数据
async function getNoticeData(process_name) {
  let params = {
    process_name,
    mini_pro_filter: getNotShowGuildMiniproIds(),
    // mini_pro_filter: [],
  }
  log.info('[getNoticeData]', params)
  return request.$post('/chatroom/v2/minipro/guides', {}, params)
}

// 检查小程序是否可以展示快捷键引导弹窗
function checkCanShowKmNotice(item, installed_mini_programs) {
  // 1. 如果小程序已经开启了，不需要展示快捷键弹窗
  if (checkIsMiniProInstanceExists(item.mini_pro_id)) return false
  // 2. 如果小程序还没安装，说明还未使用过，不需要展示快捷键弹窗
  if (!installed_mini_programs.includes(item.mini_pro_id)) return false

  let miniprogram_setting = store.get('miniprogram_setting')
  let local_miniprogram_enable = store.get('local_miniprogram_enable') || {}

  // 3. 从浮窗管理的字段中获取浮窗是否enable
  let overlay_mp = miniprogram_setting.find((v) => v.mini_pro_id === item.mini_pro_id && v.enable)
  if (overlay_mp) {
    return true
  } else {
    // 4. 如果不是浮窗，比如准星工具和硬件检测，需要从config.json中的local_miniprogram_enable里检查是否enable
    let local_mp_enable = local_miniprogram_enable[item.mini_pro_id]
    return local_mp_enable
  }
}

// 获取所有已安装的小程序列表
function getInstalledMiniProgram() {
  try {
    const items = fs.readdirSync(path.join(global.miniprogram_base_dir, 'miniprogram'), { withFileTypes: true })
    const folders = items
      .filter((item) => item.isDirectory()) // 仅保留文件夹
      .map((item) => item.name) // 提取文件夹名称
    return folders
  } catch (err) {
    console.error('Error reading directory:', err)
    return []
  }
}

// 获取快捷键的值
function getShortcutCode(str) {
  try {
    let data = JSON.parse(str)
    data = data.map(item => {
      item.value = store.getValueByKeys({
        key: item.key ? 'km_config.' + item.key : null,
        default_value_from_key: item.default_value_from_key ? 'km_config.' + item.default_value_from_key : null,
        default_value: item.default_value ? JSON.parse(item.default_value) : null
      })
      return item
    }).filter(item => item.value && item.value.length > 0)
    if (data.length > 0) {
      return data
    }
  } catch (err) {
    log.info('[getShortcutCode error]', err)
  }
}

// 获取不再展示的小程序id
function getNeverShowGuildMiniproId() {
  return store.get('never_show_guild_minprogram_ids') || []
}

// 设置不再展示的小程序id
function setNeverShowGuildMiniproId(id) {
  if (!id) return
  let list = store.get('never_show_guild_minprogram_ids') || []
  if (list.includes(id)) return
  list.push(id)
  store.set('never_show_guild_minprogram_ids', list)
}

// 获取7天内已经展示过的小程序id
function getCdGuildMiniproId() {
  let now = new Date().getTime()
  let data = store.get('last_show_guild_minprogram_ts') || {}
  return Object.keys(data).filter(key => now - data[key] < GUILD_NOTICE_FREQUENCE)
}

// 设置7天内已经展示过的小程序id
function setCdGuildMiniproId(id) {
  if (!id) return
  let data = store.get('last_show_guild_minprogram_ts') || {}
  let now = new Date().getTime()
  data[id] = now
  store.set('last_show_guild_minprogram_ts', data)
}

// 整合所有 不需要展示guild提示的小程序id
function getNotShowGuildMiniproIds() {
  let list = [...getInstalledMiniProgram(), ...getNeverShowGuildMiniproId(), ...getCdGuildMiniproId()]
  return Array.from(new Set(list))
}

module.exports = {
  checkIsMiniProInstanceExists,
  checkIsInjectWhiteList,
  getNoticeData,
  checkCanShowKmNotice,
  getInstalledMiniProgram,
  getShortcutCode,
  getNeverShowGuildMiniproId,
  setNeverShowGuildMiniproId,
  getCdGuildMiniproId,
  setCdGuildMiniproId,
  getNotShowGuildMiniproIds,
}