<template>
  <div class="skeleton-game-detail">
    <!-- 顶部banner骨架屏 -->
    <div class="skeleton-top-banner">
      <div
        class="back-btn"
        @click="handleBackButton"
      >
        <svg
          width="16"
          height="16"
          viewBox="0 0 14 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M10.1257 1.21956C10.4184 1.49607 10.4316 1.95757 10.1551 2.25034L5.66963 6.99968L10.1551 11.749C10.4316 12.0418 10.4184 12.5033 10.1257 12.7798C9.83289 13.0563 9.37139 13.0431 9.09489 12.7503L4.13655 7.50034C3.8711 7.21932 3.8711 6.78003 4.13655 6.49901L9.09489 1.24901C9.37139 0.95624 9.83289 0.943054 10.1257 1.21956Z"
            fill="white"
            fill-opacity="1"
          />
        </svg>
      </div>
    </div>

    <!-- 主要内容区域骨架屏 -->
    <div class="skeleton-content-container">
      <div class="skeleton-main-content">

        <!-- 新闻区域骨架屏 -->
        <div class="skeleton-news-section">
          <div class="skeleton-news-list">
            <SkeletonNewsItem v-for="i in 3" :key="i" />
          </div>
        </div>
      </div>

      <!-- 右侧边栏骨架屏 -->
      <div class="skeleton-sidebar">
        <!-- 朋友区域骨架屏 -->
        <div class="skeleton-friends-section">
          <div class="skeleton-friends-list">
            <SkeletonFriendItem v-for="i in 4" :key="i" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SkeletonGameDetail">
import { useRouter } from 'vue-router';
import SkeletonFriendItem from './components/SkeletonFriendItem.vue';
import SkeletonNewsItem from './components/SkeletonNewsItem.vue';

const router = useRouter();

const handleBackButton = () => {
  router.push({
    path: '/app/home',
  });
};
</script>

<style lang="scss" scoped>
.skeleton-game-detail {
  width: calc(100% + 8px);
  margin-right: -8px;
  height: 100vh;
  background: var(---general-color-bg-3, #fafbfc);
  overflow: scroll;

  .skeleton-top-banner {
    position: relative;
    width: 100%;
    height: 350px;
    background: var(---general-color-bg-0, #F1F2F3);

    .back-btn {
      position: absolute;
      top: 12px;
      left: 24px;
      cursor: pointer;
      display: flex;
      height: 28px;
      width: 28px;
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      background: var(--balck-gamerecord-color-black-50a, rgba(0, 0, 0, 0.5));
    }
  }

  // 主要内容区域
  .skeleton-content-container {
    display: flex;
    padding: 16px 24px 0 24px;
    gap: 16px;
    min-height: calc(100vh - 350px);
    background: var(---general-color-bg-3, #FAFBFC);

    .skeleton-main-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 32px;

      .skeleton-news-list {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
    }

    .skeleton-sidebar {
      width: 300px;
      display: flex;
      flex-direction: column;
      gap: 32px;


      .skeleton-friends-list {
        display: flex;
        padding: 14px 16px;
        flex-direction: column;
        gap: 12px;
        border-radius: 8px;
        background: var(---general-color-primary-0, #FFF);
      }
    }
  }
}
</style>
