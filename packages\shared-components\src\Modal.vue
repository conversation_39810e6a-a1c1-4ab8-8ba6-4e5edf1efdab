<template>
  <transition name="modal-fade">
    <div class="heybox-modal-overlay" v-if="show" @click.self="handleOverlayClick">
      <div class="heybox-modal" :style="{ width }">
        <div class="heybox-modal-header">
          <span class="heybox-modal-title">{{ title }}</span>
          <button class="heybox-modal-close" @click="close" v-if="showClose">×</button>
        </div>
        <div class="heybox-modal-body">
          <slot></slot>
        </div>
        <div class="heybox-modal-footer" v-if="$slots.footer">
          <slot name="footer"></slot>
        </div>
        <div class="heybox-modal-footer" v-else-if="showFooter">
          <button class="heybox-button" @click="close">{{ cancelText }}</button>
          <button class="heybox-button heybox-button--primary" @click="confirm">{{ confirmText }}</button>
        </div>
      </div>
    </div>
  </transition>
</template>

<script>
export default {
  name: 'HeyboxModal',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: 'Title'
    },
    width: {
      type: String,
      default: '500px'
    },
    showClose: {
      type: Boolean,
      default: true
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    closeOnClickOverlay: {
      type: Boolean,
      default: true
    },
    confirmText: {
      type: String,
      default: '确认'
    },
    cancelText: {
      type: String,
      default: '取消'
    }
  },
  methods: {
    close() {
      this.$emit('update:show', false);
      this.$emit('close');
    },
    confirm() {
      this.$emit('confirm');
      this.close();
    },
    handleOverlayClick() {
      if (this.closeOnClickOverlay) {
        this.close();
      }
    }
  }
};
</script>

<style>
.heybox-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.heybox-modal {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.heybox-modal-header {
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.heybox-modal-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.heybox-modal-close {
  font-size: 20px;
  color: #909399;
  background: transparent;
  border: none;
  cursor: pointer;
  outline: none;
}

.heybox-modal-close:hover {
  color: #409eff;
}

.heybox-modal-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

.heybox-modal-footer {
  padding: 10px 20px;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.heybox-modal-footer .heybox-button + .heybox-button {
  margin-left: 10px;
}

.modal-fade-enter-active,
.modal-fade-leave-active {
  transition: opacity 0.3s;
}

.modal-fade-enter-from,
.modal-fade-leave-to {
  opacity: 0;
}
</style> 