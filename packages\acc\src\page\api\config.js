const path = require('path')
const axios = require(path.join(__dirname, 'common/axios.js'))
import { getBrowserType, getOSType, getCookie } from '../common/utils.js'
const BASE_XHH = 'https://api.xiaoheihe.cn'

// export const __DEFAULT_PARAMS__ = {
//   client_type: 'heybox_chat',
//   x_client_type: 'web',
//   os_type: 'web',
//   x_os_type: getOSType(),
//   device_info: getBrowserType(),
//   x_app: 'heybox_chat',
//   version: '999.0.4',
//   web_version: '1.0.0',
//   chat_os_type: 'web',
//   chat_version: '999.0.0',
//   client_bit: window.exe_bit,
//   win_version: window.windows_version,
//   heybox_id: getCookie('heybox_id'),
// }

const __DEFAULT_PARAMS__ = {
  os_type: 'pc_proxy',
  client_type: 'acc_pc',
  x_app: 'heybox_acc_pc',
  x_os_type: 'Windows',
  x_client_type: 'web',
  version: '999.0.4',
}

window.SUPPORT_WEBP = undefined
export function axiosGetHandler (url, args = {}, apps = {}) {
  let params = Object.assign({
    heybox_id: getCookie('heybox_id')
  }, __DEFAULT_PARAMS__,  apps, args)
  return httpRequest('GET', url, params)
}

export function axiosPostHandler (url, args = {}, formdata, apps = {}) {
  let params = Object.assign({
    heybox_id: getCookie('heybox_id')
  }, __DEFAULT_PARAMS__, apps, args)
  return httpRequest('POST', url, params, formdata)
}

function httpRequest (method, url, args, formdata) {
	let ContentType = "application/x-www-form-urlencoded;charset=utf-8"
	let path = '', upload
	let promise;
	if (url.indexOf(BASE_XHH) === 0) {
		try {
			let u = new URL(url)
			path = u.pathname
		} catch (e) {
			path = url.replace(BASE_XHH, '')
		}
	} else {
		path = url
	}
	if (includeV2(path)) { // v2及以上的POST接口传JSON
		ContentType = "application/json;charset=utf-8"
	}
	if (method === 'POST') {
		if (args._is_upload) {
			ContentType = 'multipart/form-data;'
			upload = args._is_upload
		}
		promise = axios.post(url, formdata, {
			headers: {
				"Content-Type": ContentType
			},
			timeout: 30000,
			responseType: "json",
			params: args,
			data: formdata,
			upload,
		})
	} else {
		promise = axios.get(url, {
			headers: {
				"Content-Type": ContentType
			},
			timeout: 30000,
			responseType: "json",
			params: args
		})
	}
	promise.then(res => {
		if([400, 401, 402, 403, 404].includes(res.status)) {
			console.error("发生了一些错误，请检查网络或稍后重试")
		} else if([500, 501, 502, 503, 504].includes(res.status)) {
			console.error("发生了一些错误，请稍后重试")
		}
	})
	return promise
}

function includeV2(str) {
  const regex = /\/v([2-9]|[1-9]\d+)\//;
  return regex.test(str);
}