// 获取最高版本号
function getTargetVersion(versions, expectVersion) {
  // 过滤出符合版本号格式的数据
  const versionRegex = /^\d+\.\d+\.\d+$/;
  const validVersions = versions.filter((version) =>
    versionRegex.test(version)
  );
  if (validVersions.length === 0) {
    return null;
  }

  // 优先返回期望版本
  let expect = validVersions.find((v) => v === expectVersion);
  if (expect) return expect;

  // 找不到期望版本情况下, 获取最大的版本号
  validVersions.sort((a, b) => {
    const aParts = a.split('.').map(Number);
    const bParts = b.split('.').map(Number);

    for (let i = 0; i < 3; i++) {
      if (aParts[i] > bParts[i]) return -1;
      if (aParts[i] < bParts[i]) return 1;
    }

    return 0;
  });
  return validVersions[0];
}

module.exports = {
  getTargetVersion,
};
