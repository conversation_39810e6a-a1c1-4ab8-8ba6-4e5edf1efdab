const fs = require('original-fs')
const path = require('path')
const { dialog } = require('electron')
const { generateErrorText, getRegeditKey } = require('@heybox/electron-utils')
const log = require('@heybox/electron-utils/log')
const store = require('@heybox/electron-utils/store')
const EventBus = require('@heybox/electron-utils/eventBus')
const ensureDirExists = (dir) => {
  return new Promise((resolve, reject) => {
    try {
      fs.mkdirSync(dir, { recursive: true })
      resolve()
    } catch (error) {
      log.info('[ensureDirExists error]', error)
      reject({error, errorText: generateErrorText(error, '创建文件夹')})
    }
  })
}
module.exports.ensureDirExists = ensureDirExists

const walkDirHandler = (dirname, depth = 1) => {
  return {
    root: dirname,
    children: fs.readdirSync(dirname).map((name) => {
      let filePath = path.join(dirname, name)
      let stat = fs.statSync(filePath)
      let file = {
        isDir: stat.isDirectory(),
        name,
        modify_time: stat.mtime.getTime(),
        size: stat.size
      }
      if (file.isDir && depth > 1) {
        file.children = walkDirHandler(filePath, depth - 1).children
      }
      return file
    })
  }
}
const walkDir = (dirname, depth) => {
  return new Promise((resolve, reject) => {
    try {
      resolve( walkDirHandler(dirname, depth) )
    } catch (error) {
      reject(error.message)
    }
  })
}
module.exports.walkDir = walkDir

module.exports.pathExists = (filePath) => {
  return new Promise((resolve, reject) => {
    try {
      resolve(fs.existsSync(filePath))
    } catch (error) {
      log.info('[pathExists error]', error)
      reject(error.message)
    }
  })
}

const copyFileHandler = (filePath, targetPath) => {
  fs.copyFileSync(filePath, targetPath)
}
module.exports.copyFile = (filePath, targetPath) => {
  return new Promise((resolve, reject) => {
    try {
      if (fs.existsSync(filePath)) {
        copyFileHandler(filePath, targetPath)
        resolve()
      } else {
        reject(`${filePath} was NotExist`)
      }
    } catch (error) {
      reject(error.message)
    }
  })
}

const copyFolderHandler = async (floderPath, targetPath) => {
  let { children } = walkDirHandler(floderPath)
  await ensureDirExists(targetPath)
  for (let i=0; i<children.length; i++) {
    if (children[i].isDir) {
      await copyFolderHandler(path.join(floderPath, children[i].name), path.join(targetPath, children[i].name))
    } else {
      copyFileHandler(path.join(floderPath, children[i].name), path.join(targetPath, children[i].name))
    }
  }
}
module.exports.copyFolder = (floderPath, targetPath) => {
  return new Promise(async (resolve, reject) => {
    try {
      if (fs.existsSync(floderPath)) {
        await copyFolderHandler(floderPath, targetPath)
        resolve()
      } else {
        reject(`${floderPath} was NotExist`)
      }
    } catch (error) {
      reject(error.message)
    }
  })
}


module.exports.openFileSelector = (options) => {
  return new Promise((resolve, reject) => {
    try {
      resolve(dialog.showOpenDialogSync(options))
    } catch (error) {
      reject(error.message)
    }
  })
}

module.exports.getMiniProgramSetting = (moduleName) => {
  if (!moduleName) return
  let miniprogram_setting = store.get('miniprogram_setting') || []
  let index = miniprogram_setting.findIndex(({mini_pro_id}) => mini_pro_id === moduleName)
  if (index !== -1) {
    return miniprogram_setting[index]
  } else {
    return
  }
}
module.exports.clearInstanceCache = (instance) => {
  const path = require('path');
  delete require.cache[require.resolve(path.join(instance.moduleDir, `./${instance.latestVersion}/app.asar/main.js`))]
  delete require.cache[require.resolve(path.join(instance.moduleDir, `./${instance.latestVersion}/app.asar/config.js`))]
}
module.exports.getConfigJson = ({moduleDir, latestVersion}) => {
  const path = require('path');
  return require(path.join(moduleDir, `./${latestVersion}/app.asar/config.js`))
}

module.exports.getInjectGameWindow = (processName) => {
  let node_inject = require('@heybox/node-inject').overlay
  let topWindows = node_inject.getTopWindows()
  return topWindows.find((win) => { 
    if(Array.isArray(processName)) {
      return processName.map(name => name.toLowerCase()).includes(win.processName.toLowerCase())
    } else {
      return win.processName.toLowerCase() === processName.toLowerCase()
    }
  })
}

// 获取小程序窗口的 size
module.exports.getMiniProWindowSize = ({moduleDir, latestVersion, __miniprogram_module_name__}) => {
  let config = this.getConfigJson({moduleDir, latestVersion})
  if (!this.isMiniProWindowSizeNeedStore(config)) return
  let mini_pro_size = store.get('mini_pro_size') || {}
  return mini_pro_size[__miniprogram_module_name__] || {
    width: config.width,
    height: config.height,
    max: false,
  }
}

// 设置小程序窗口的 size
module.exports.setMiniProWindowSize = (__miniprogram_module_name__, data) => {
  let mini_pro_size = store.get('mini_pro_size') || {}
  mini_pro_size[__miniprogram_module_name__] = {
    ...mini_pro_size[__miniprogram_module_name__] || {},
    ...data
  }
  store.set('mini_pro_size', mini_pro_size)
}

// 判断小程序窗口是否需要记录size
module.exports.isMiniProWindowSizeNeedStore = (config) => {
  if (!config) return false
  return !(config.rejectStoreWindowSize || config.isOverlay || config.fullscreen)
}

module.exports.setLocalMiniProEnable = (moduleName, enable) => {
  let local_miniprogram_enable = store.get('local_miniprogram_enable') || {}
  local_miniprogram_enable[moduleName] = enable
  store.set('local_miniprogram_enable', local_miniprogram_enable)
  EventBus.emit('minipro-enable-change', local_miniprogram_enable)
}

module.exports.getLocalMiniProEnable = (moduleName) => {
  let local_miniprogram_enable = store.get('local_miniprogram_enable') || {}
  return local_miniprogram_enable[moduleName]
}

module.exports.getConfigInDisplayConfig = (mini_pro_id, filter_func) => {
  try {
    const mini_data = mini_map[mini_pro_id]
    if (!mini_data)  return null
    const display_config = mini_data.display_config
    if (!display_config) return null

    let display_config_json = JSON.parse(display_config.replace(/\${mini_pro_id}/g, mini_pro_id))
    if (filter_func) {
      return display_config_json.filter(filter_func)
    } else {
      return display_config_json
    }
  } catch (e) {
    log.info('[getConfigInDisplayConfig error]', e)
    return null
  }
}

module.exports.checkBoundInDisplay = (bounds, percent = 1) => {
  const { x, y, width, height } = bounds;
  const { screen } = require('electron');

  // 获取所有显示器的信息
  const displays = screen.getAllDisplays();

  // 计算窗口的总面积
  const windowArea = width * height;

  // 遍历所有显示器，计算窗口在每个显示器内的面积
  let visibleArea = 0;
  for (const display of displays) {
      const { x: screenX, y: screenY, width: screenWidth, height: screenHeight } = display.workArea;

      // 计算窗口和当前显示器的重叠区域
      const overlapX = Math.max(0, Math.min(x + width, screenX + screenWidth) - Math.max(x, screenX));
      const overlapY = Math.max(0, Math.min(y + height, screenY + screenHeight) - Math.max(y, screenY));
      visibleArea += overlapX * overlapY;
  }

  // 如果窗口在屏幕内的面积大于等于 50%，返回 true
  return visibleArea >= windowArea * percent;
}

module.exports.checkBoundInRect = (bounds, rect) => {
  const { x, y, width, height } = bounds;
  return rect.x >= x && rect.y >= y && (width + x) <= (rect.width + rect.x) && (height + y) <= (rect.height + rect.y)
}

module.exports.getRegeditKey = getRegeditKey