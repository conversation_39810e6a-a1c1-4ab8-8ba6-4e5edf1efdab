<template>
  <div class="cpt-accelerator-dialog">
    <div
      class="dialog-container"
      @click.stop
    >
      <div class="view-accelerator">
        <div class="cpt-header">
          <div class="back">
            <i 
              v-if="currentPageInfo.back" 
              class="iconfont icon-common-arrow-left-line"
              @click="toPage(currentPageInfo.back)"
            ></i>
          </div>
          <div class="title">
            {{ currentPageInfo.title }}
          </div>
          <div class="close" @click="close">
            <i class="iconfont icon-common-close-line"></i>
          </div>
        </div>
        <div class="vip-coupon-receive-wrapper" v-if="currentPage === 'recharge' && waitReceiveCoupons.length > 0">
          <div class="left">
            <img src="https://imgheybox.max-c.com/oa/2025/07/11/********************************.png">
          </div>
          <div class="coupon-info">
            <div class="title">{{ waitReceiveCouponTitle }}</div>
            <div class="desc">{{ waitReceiveCouponDesc }}</div>
          </div>
          <div class="right">
            <button @click="receiveCoupon">立即领取</button>
          </div>
        </div>
        <div v-if="showUserInfo" class="cpt-user-info-row">
          <UserInfo :userInfo="userInfo" />
          <div class="button-list">
            <!-- <button v-if="currentPage === 'proxy_select'" class="default-button">
              设置
            </button> -->
            <button v-if="currentPage === 'proxy_select'" class="primary-button" @click="toPage('recharge')">
              充值
            </button>
            <button v-if="currentPage === 'recharge'" class="default-button" @click="toPage('recharge_history')">
              充值记录
            </button>
            <button v-if="currentPage === 'recharge'" class="default-button" @click="toPage('coupon')">
              会员卡券
            </button>
          </div>
        </div>

        <component 
          :is="currentPageInfo.component" 
          ref="currentPageRef" 
          :params="currentPageParams"
          :acc_game_infos="acc_game_infos"
          :appid="appid"
          :userInfo="userInfo"
          :has_coupon="waitReceiveCoupons.length > 0"
          @toPage="toPage"
          @close="close"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="AccelerateDialog">
import { ref, onMounted, defineExpose, computed, nextTick, defineProps, defineEmits } from 'vue';
import { ipcService } from '@heybox-app-web-shared/ipc';
import {
  useSubComponent,
  getDefaultSubProps,
} from '../../composables/useSubComponent';
import ProxySelect from './view/ProxySelect.vue';
import UserInfo from './components/UserInfo.vue';
import Recharge from './view/Recharge.vue';
import RechargeSuccess from './view/RechargeSuccess.vue';
import RechargeHistory from './view/RechargeHistory.vue';
import Coupon from './view/Coupon.vue';
import { useEventBus } from '@heybox-app-web-shared/eventbus'
import { getVipCouponList, receiveVipCoupon } from '_api/acc'
import { useToast } from 'vue-toastification'

const EventBus = useEventBus()
const toast = useToast()

const accConfig = ref({})
const props = defineProps(getDefaultSubProps());
const messageTarget = ref('')
const acc_game_infos = ref([])
const appid = ref('')

const waitReceiveCoupons = ref([])
const waitReceiveCouponTitle = ref('')
const waitReceiveCouponDesc = ref('')

const { show, handleClose, Dialog } = useSubComponent({
  initDialog: (options) => {
    console.log('initDialog', options)
    acc_game_infos.value = options.acc_game_infos
    appid.value = options.appid
    messageTarget.value = 'all'
    show.value = true;
  },
});

const currentPageRef = ref(null)
const userInfo = ref({})
const currentPage = ref('proxy_select')
const currentPageParams = ref({})

const pageList = [
  {
    name: 'proxy_select',
    title: '加速设置',
    component: ProxySelect,
  },
  {
    name: 'recharge',
    title: '会员购买',
    component: Recharge,
    back: 'proxy_select',
  },
  {
    name: 'recharge_history',
    title: '充值记录',
    component: RechargeHistory,
    back: 'recharge',
  },
  {
    name: 'recharge_success',
    title: 'VIP充值',
    component: RechargeSuccess,
    back: 'proxy_select',
  },
  {
    name: 'coupon',
    title: '输入兑换卡密',
    component: Coupon,
    back: 'recharge',
  },
]

const currentPageInfo = computed(() => {
  return pageList.find(item => item.name === currentPage.value)
})

const showUserInfo = computed(() => {
  return currentPage.value === 'proxy_select' || currentPage.value === 'recharge'
})

const init = () => {
  getUserInfo()
  initVipCouponList()
  window.accAPI.onEventListener({
    'onRecharge': (err) => {
      if(err) {
        console.error('onRecharge', err)
      } else {
        console.log('onRecharge', '充值成功')
        getUserInfo()
      }
    }
  })
}

async function getUserInfo() {
  const userInfoResult = await window.accAPI.getUserInfo()
  console.log('userInfoResult', userInfoResult)
  if(userInfoResult.status === 'ok') {
    userInfo.value = userInfoResult.result
  }
}

const sendIpcMessage = (type, data) => {
  const sendTarget = messageTarget.value
  const eventChannel = `acc-dialog-response`
  const messageData = {
    type,
    data,
  }
  if(sendTarget === 'main') {
    ipcService.sendToMain(eventChannel, messageData)
  } else if (sendTarget === 'all') {
    ipcService.sendToAll(eventChannel, messageData)
  } else {
    ipcService.sendToTarget(messageTarget.value, eventChannel, messageData)
  }
}

const close = (data) => {
  sendIpcMessage('close', data)
  handleClose();
};

const openAuthDialog = () => {
  EventBus.emit('show-dynamic-dialog', {
    type: 'Dialog',
    cptName: 'RealnameAuth',
    props: {
      is_from_recharge: true,
    },
  })
}

const openUnderageDialog = () => {
  EventBus.emit('show-dynamic-dialog', {
    type: 'Dialog',
    cptName: 'Underage',
    props: {},
  })
}

const toPage = (page, params) => {
  if(page === 'recharge') {
    if(userInfo.value.is_old_18 === -1) {
      openUnderageDialog()
      return
    } else if(userInfo.value.is_old_18 === 0) {
      openAuthDialog()
      return
    }
  }
  currentPage.value = page
  if(params) {
    currentPageParams.value = params
  }
}

const initVipCouponList = async () => {
  const res = await getVipCouponList()
  console.log('getVipCouponList', res)
  if (res.status == 'ok') {
    waitReceiveCoupons.value = res.result.coupons
    waitReceiveCouponTitle.value = res.result.title
    waitReceiveCouponDesc.value = res.result.sub_title
  }
}

const receiveCoupon = async () => {
  if (waitReceiveCoupons.value && waitReceiveCoupons.value[0]) {
    const res = await receiveVipCoupon({
      id: String(waitReceiveCoupons.value[0].id)
    })
    console.log('receiveVipCoupon', waitReceiveCoupons.value[0].id)
    console.log('receiveVipCoupon', res)
    if (res.status == 'ok') {
      toast.success('领取成功')
      initVipCouponList()
      currentPageRef.value.initCouponSelector()
    }
  }
  console.log('receiveVipCoupon', res)
}

onMounted(() => {
  init()
  EventBus.on('toPage', async (page, params) => {
    await getUserInfo()
    toPage(page, params)
  })
})

defineExpose({
  init,
  getUserInfo,
})

</script>

<style lang="scss">
.cpt-accelerator-dialog {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 1;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  .dialog-container {
    width: 640px;
    height: 559px;
    border-radius: 12px;
    background-color: $general-color-bg-3;
    border: 1px solid $general-color-stroke-0;
    overflow: hidden;
  }
}
.view-accelerator {
  width: 100%;
  height: 100%;
  background-color: $general-color-bg-3;
  .vip-coupon-receive-wrapper {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 10px;
    height: 60px;
    padding: 0 24px;
    background: linear-gradient(45deg, #736E7D 0%, #1B2025 100%);
    .left {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
    .coupon-info {
      flex: 1;
      .title {
        font-size: 14px;
        color: #FFF;
        font-weight: 600;
        margin-bottom: 2px;
      }
      .desc {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.4);
        line-height: 16px;
      }
    }
    .right {
      button {
        width: 72px;
        height: 24px;
        border-radius: 3px;
        background-color: #FFF;
        color: #000;
        font-size: 12px;
        font-weight: 600;
        line-height: 24px;
        text-align: center;
        cursor: pointer;
      }
    }
  }
  .cpt-header {
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 24px;
    background-color: $general-color-bg-4;
    .back { 
      width: 18px;
      height: 18px;
      line-height: 18px;
      .iconfont {
        font-size: 18px;
        color: $general-color-text-1;
        cursor: pointer;
      }
    }
    .title {
      color: $general-color-text-1;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 18px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
    }
    .close {
      .iconfont {
        font-size: 18px;
        color: $general-color-text-4;
      }
    }
  }
  .cpt-user-info-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 12px;
    background-color: $general-color-bg-4;
    .button-list {
      display: flex;
      gap: 8px;
      
      .primary-button, .default-button {
        padding: 7px 12px;
        border-radius: 3px;
        border: none;
        cursor: pointer;
        font-size: 12px;
      }
      
      .primary-button {
        color: $general-color-text-6;
        background: var(---, linear-gradient(46deg, var(---greadient-color-primary-left, #464B50) -0.9%, var(---greadient-color-primary-right, #14191E) 100.9%));
      }
      
      .default-button {
        background-color: $general-color-bg-2;
        color: $general-color-text-1;
      }
    }
  }
  input[type="radio" i] {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    border: 1px solid $general-color-stroke-0;
    border-radius: 50%;
    vertical-align: middle;
    position: relative;
    cursor: pointer;
    margin: 0;
  }

  input[type="radio" i]::before {
    content: "";
    display: block;
    width: 6px;
    height: 6px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
  }

  input[type="radio" i]:checked {
    border: 5px solid var(--general-color-primary-100, rgba(20, 25, 30, 1));
  }

  input[type="radio" i]:checked::before {
    opacity: 1;
  }

  input[type="radio" i]:focus-visible {
    outline: 2px solid var(--dark-op-3);
    outline-offset: 2px;
  }
}
</style>