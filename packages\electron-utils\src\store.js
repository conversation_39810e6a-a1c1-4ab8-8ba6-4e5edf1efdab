const Store = require('electron-store');
const store = getStoreInstance()
const dotProp = require("dot-prop");

// todo: global.log 改成 utils.log 后，由于 log 包含 app ，preload 会用 electron-store , electron-store 又用到了 log，导致 preload 会报错 app 为 undefined
// 后续会更改 store 的逻辑
const { 
  MAIN_DEFAULT_CONFIG, 
  CHAT_DEFAULT_CONFIG, 
  ACC_DEFAULT_CONFIG,
  KM_CONFIG,
  LIBRARY_DEFAULT_CONFIG,
  GLOBAL_CONFIG
} = require('./assets/constant/config')

class LocalStore {
  constructor () {
    this.original_store = store
    let main_config = {}
    let chat_config = {}
    let library_config = {}
    let acc_config = {}
    let km_config = {}
    let hcsd = {} // 兼容老版本快捷键配置
    try {
      main_config = store.get('main_config') || {}
      chat_config = store.get('chat_config') || {}
      acc_config = store.get('acc_config') || {}
      km_config = store.get('km_config') || {}
      library_config = store.get('library_config') || {}
      hcsd = store.get('hcsd') || {}
    } catch (e) {
      global.mainWindow?.webContents.send('localstorage:error', e)
      console.info('[store] get error', e)
    }
    global.km_config = {
      ...KM_CONFIG,
      ...km_config || {},
      ...hcsd || {}
    }
    global.main_config = { 
      ...MAIN_DEFAULT_CONFIG,
      ...main_config || {}
    }
    global.chat_config = {
      ...CHAT_DEFAULT_CONFIG,
      ...chat_config || {}
    }
    global.acc_config = {
      ...ACC_DEFAULT_CONFIG,
      ...acc_config || {}
    }
    global.library_config = {
      ...LIBRARY_DEFAULT_CONFIG,
      ...library_config || {}
    }
    global.global_config = GLOBAL_CONFIG
  }
  async init() {
    try {
      store.set('main_config', {
        ...global.main_config,
      })
      store.set('chat_config', {
        ...global.chat_config,
      })
      store.set('acc_config', {
        ...global.acc_config,
      })
      store.set('km_config', {
        ...global.km_config,
      })
      store.set('hcsd', {
        ...global.km_config,
      })
      store.set('library_config', {
        ...global.library_config,
      })
      store.set('global_config', {
        ...global.global_config,
      })
    } catch (e) {
      console.info('[store] init error', e)
      mainWindow?.webContents.send('localstorage:error', e)
    }
    store.onDidChange('config_data', this.onConfigDataChange.bind(this))
    store.onDidChange('main_config.setting_data', this.onSettingDataChange.bind(this))
  }

  /**
   * 获取带默认值的值，按照key => default_value_from_key => default_value的顺序获取
   * 
   * @param {string} key - 快捷键储存的字段key值，如 "km_.miniprogram_overlay_key"
   * @param {string} default_value_from_key - 如果key对应的值为undefined, 将default_value_from_key的值作为默认值
   * @param {string|Array<number>} default_value - 如果default_value_from_key的值也是undefined，取default_value的值作为默认值
   */
  getValueByKeys({key, default_value_from_key, default_value} = {}) {
    return this.get(key) ?? this.get(default_value_from_key) ?? default_value
  }

  // 获取config.json中的值，
  // 支持获取 某个子key的值，如 user_account_settings.allow_friend_promise_msg
  get(k) {
    if (!k) return undefined

    try {
      let res = dotProp.get(global, k)
      if(!res) res = store.get(k) // 兼容小程序旧数据存储
      return res
    } catch (e) {
      console.info('[store] get error', e)
      mainWindow?.webContents.send('localstorage:error', e)
      return undefined
    }
  }

  // 设置config.json中的值，
  // 支持设置 某个子key的值，如 user_account_settings.allow_friend_promise_msg
  set(k, v) {
    try {
      dotProp.set(global, k, v)
      store.set(k, v)
    } catch (e) {
      console.info('[store] set error', e)
      mainWindow?.webContents.send('localstorage:error', e)
    }
  }
  del(k) {
    try {
      dotProp.delete(global, k)
      return store.delete(k)
    } catch (error) {
      console.log('store del error: ', error)
    }
  }
  getLocalConfig(k) {
    return global.main_config[k]
  }
  setLocalConfig(k, v) {
    const main_config = global.main_config
    main_config[k] = v
    // this.set('main_config', main_config)
  }
  onConfigDataChange(v) {
    global.main_config = v
  }
  onSettingDataChange(v) {
    const regedit = require('./regedit')
    if (v && global.main_config.setting_data && v.auto_launch !== global.main_config.setting_data.auto_launch) {
      regedit.editAutoStart(v.auto_launch)
    }
    if (v && global.main_config.setting_data && v.admin_mode_launch !== global.main_config.setting_data.admin_mode_launch) {
      const isWriteSuccess = regedit.setAdminMode(v.admin_mode_launch ? '1' : '0')
      // 写入失败时，恢复原来的值
      if(!isWriteSuccess) {
        v.admin_mode_launch = global.local_config.admin_mode_launch
      }
    }
    global.main_config.setting_data = v
  }
}

function getStoreInstance() {
  try {
    return new Store({
      watch: true,
    });
  } catch (e) {
    console.info('[store] constructor error', e)
    const path = require('path')
    const fs = require('fs')
    const { app } = require("electron")
    let configPath = path.join(app.getPath('userData'), 'config.json')
    fs.readFile(configPath, 'utf-8', (err, data) => {
      const Sentry = require('@sentry/electron');
      data = data.replace(/\n/g, '').replace(/\t/g, '')
      if (err) {
        Sentry.captureException(err)
        return
      }
      Sentry.captureException(e, {
        level: 'fatal',
        extra: { details: data },
      })
      console.info('[store] error old config', data)
    });
    return new Store({
      clearInvalidConfig: true,
      watch: true,
    });
  }
}
 
module.exports = new LocalStore()
