const { execSync } = require('child_process');

// 获取所有命令行参数
const args = process.argv.slice(2);
const argString = args.join(' ');
const envArg = process.argv.find(arg => arg.startsWith('--env='));
const env = envArg ? envArg.split('=')[1] : 'prod';

try {
    console.log('开始构建 electron main...');
    execSync(`cd electron/main && npm run compiler`);
    execSync(`cd electron/main && npm run build ${argString}`, { stdio: 'inherit' });
    
    console.log('开始构建 electron launcher...');
    execSync(`cd electron/launcher && npm run build:combine ${argString}`, { stdio: 'inherit' });
    
    console.log('electron 构建完成！');
} catch (error) {
    console.error('构建过程中出现错误:', error);
    process.exit(1);
} 