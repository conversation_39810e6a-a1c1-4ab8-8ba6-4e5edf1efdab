{"name": "@heybox-app-web/popup", "private": true, "version": "1.0.0", "main": "dist/index.html", "description": "全局弹窗UI", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heybox-app-web-shared/components": "workspace:*", "@heybox-app-web-shared/eventbus": "workspace:*", "@heybox-app-web-shared/font": "workspace:*", "@heybox-app-web-shared/ipc": "workspace:*", "@heybox-app-web-shared/utils": "workspace:*", "@heybox-webapp/hb-theme": "^0.0.3", "@heybox/hb-sm": "^1.0.5", "axios": "^1.9.0", "cos-js-sdk-v5": "^1.10.1", "element-plus": "^2.9.11", "js-md5": "^0.8.3", "qrcode": "^1.5.3", "qs": "^6.14.0", "vue": "^3.3.4", "vue-toastification": "2.0.0-rc.5"}, "devDependencies": {"@vitejs/plugin-vue": "^4.2.3", "sass": "^1.63.6", "unplugin-element-plus": "^0.10.0", "vite": "^4.4.0", "vite-plugin-cdn-import": "^1.0.1"}}