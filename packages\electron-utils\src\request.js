const { net } = require('electron')
const os = require('os')
const { G } = require('./assets/js/encrtpy/index')
// const { getSmDeviceId } = require('../../../electron/main/src/assets/js/utils')
const m = require('js-md5')
const { getMachineId } = require('./regedit')

let default_params = null

const chat_default_params = {
  client_type: 'heybox_pc',
  x_client_type: 'pc',
  os_type: 'web',
  x_os_type: 'Windows',
  x_app: 'heybox_pc',
  version: '999.0.4',
  web_version: '1.0.0',
  exe_version: global.CLIENT_VERSION, 
  electron_version: global.ELECTRON_VERSION,
  client_bit: global.EXE_BIT,
}

// 初始化default_params
async function initDefaultParams() {
  if (!default_params) {
    default_params = {
      client_type: 'heybox_pc',
      x_client_type: 'pc',
      x_os_type: 'Windows',
      x_app: 'heybox_pc',
      version: global.CLIENT_VERSION,
      exe_version: global.CLIENT_VERSION, 
      electron_version: global.ELECTRON_VERSION,
      client_bit: global.EXE_BIT,
      os_version: os.version(),
      device_id: await getMachineId(),
    }
  }
  return default_params
}

class Request {
  async $http (url, method, params, body = {}, base = 'heybox', headers = {}) {
    // 确保default_params已初始化
    await initDefaultParams()
    
    return new Promise(async (resolve, reject) => {
      let options = {}
      let ContentType = "application/x-www-form-urlencoded;charset=utf-8"
      let sm_cookie = {}
      if (includeV2(url)) { // v2及以上的POST接口传JSON
        ContentType = "application/json;charset=utf-8"
      }
      
      // 数美cookie注入
      // if (REPORT_SM_DEVICE_ID_APIS.indexOf(url) >= 0) {
      //   let device_id = await getSmDeviceId()
      //   if (!device_id) {
      //     return Promise.reject('getSmDeviceId 获取数美device_id失败');
      //   }
      //   sm_cookie = {
      //     name: 'x_xhh_tokenid',
      //     value: device_id
      //   }
      // }
      if (['heybox', 'heychat', 'heyacc'].includes(base)) {
        const utils = require('@heybox/electron-utils')
        let user_login_cookies = utils.getStoreCookies()
        let cookies = [...user_login_cookies, sm_cookie]
        let cookieString = cookies.map((ck) => `${ck.name}=${ck.value};`).join(' ')
        // 时间戳
        let t_long = G.w().getTime()
        let t = ~~(+G.w() / 1000);
        // nonce生成
        let n = m(
          t + Math.random(new Date().getTime()).toString(),
        ).toLocaleUpperCase();
        let hkey = G.g(url, t, n)
        let _params = base === 'heychat' ? chat_default_params : default_params
        _params = {
          ..._params,
          heybox_id: user_login_cookies.find((_) => _.name === 'user_heybox_id')?.value || '-1',
          ...params,
          _time: t,
          _chat_time: t_long,
          nonce: n,
          hkey,
        }

        let search = new URLSearchParams()
        Object.keys(_params).forEach((k) => {
          search.append(k, _params[k])
        })
        url = `${ base === 'heybox' ? global.BASE_HEYBOX_API : base === 'heyacc' ? global.BASE_HEYACC_API : global.BASE_API }${url}?${search.toString()}`
        options = {
          url,
          method,
          headers: {
            'Referer': global.BASE_API,
            'content-type': ContentType,
            'Cookie': cookieString,
            // 'User-Agent': global.userAgent,
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) heybox-chat-electron/1.41.1 Chrome/130.0.6723.118 Electron/33.2.0 Safari/537.36',
            ...headers
          }
        }
      } else {
        options = {
          url,
          method,
          headers: {
            'User-Agent': global.userAgent,
            ...headers
          }
        }
      }
      let req = net.request(options)
      if (method === 'POST') {
        if(options.headers['content-type'].includes('application/x-www-form-urlencoded')) {
          let formData = new URLSearchParams()
          Object.keys(body).forEach((k) => {
            formData.append(k, body[k])
          })
          req.write(formData.toString())
        } else {
          req.write(JSON.stringify(body))
        }
      }
      req.on('response', (res) => {
        let resData = []
        res.on('data', (chunk) => {
          resData.push(chunk)
        })
        res.on('end', () => {
          let buffer = Buffer.concat(resData)
          try {
            let json = JSON.parse(buffer.toString('utf-8'))
            resolve(json)
          } catch (error) {
            reject(res.statusCode)
          }
        })
      })
      req.on('error', (error) => {
        reject(error)
      })
      req.end()
    })
  }

  $get (url, params = {}, base = 'heybox') {
    return this.$http(url, 'GET', params, {}, base)
  }
  $post (url, params = {}, body = {}, base = 'heybox', headers = {}) {
    return this.$http(url, 'POST', params, body, base, headers)
  }
}

function includeV2(str) {
  const regex = /\/v([2-9]|[1-9]\d+)\//;
  return regex.test(str);
}

module.exports = new Request()