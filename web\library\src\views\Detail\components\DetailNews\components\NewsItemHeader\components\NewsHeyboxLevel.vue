<template>
  <div
    class="cpt-news-heybox-level"
    ref="wrapper_ref"
  >
    <div
      class="level-tag-wrapper"
      :class="`level-${levelInfo.level}`"
      ref="inner_ref"
    >
      Lv.{{ levelInfo.level }}
    </div>
  </div>
</template>

<script setup name="NewsHeyboxLevel">
import { ref, toRefs, onMounted, watch } from 'vue';

const props = defineProps({
  levelInfo: {
    type: Object,
    default: () => {},
  },
});
const { levelInfo } = toRefs(props);
const wrapper_ref = ref(null);
const inner_ref = ref(null);

</script>

<style lang="scss">
.cpt-news-heybox-level {
  --angle-gradient-value: 150deg;
  position: relative;
  height: 20px;
  .level-tag-wrapper {
    // position: absolute;
    // top: 0;
    // left: 0;
    display: inline-block;
    height: 100%;
    padding: 1px 4px 1px;
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
    color: #fff;
    border-radius: 2px;
    // transform: scale(0.5);
    font-family: Helvetica;
    transform-origin: left top;
  }
  .level-1,
  .level-2,
  .level-3 {
    background-image: linear-gradient(
      var(--angle-gradient-value),
      rgb(232, 232, 232),
      rgb(208, 208, 208)
    );
  }
  .level-4,
  .level-5,
  .level-6 {
    background-image: linear-gradient(
      var(--angle-gradient-value),
      rgb(150, 225, 38),
      rgb(128, 209, 91)
    );
  }
  .level-7,
  .level-8,
  .level-9 {
    background-image: linear-gradient(
      var(--angle-gradient-value),
      rgb(50, 191, 254),
      rgb(50, 154, 254)
    );
  }
  .level-10,
  .level-11,
  .level-12 {
    background-image: linear-gradient(
      var(--angle-gradient-value),
      rgb(247, 70, 254),
      rgb(214, 70, 254)
    );
  }
  .level-13,
  .level-14,
  .level-15 {
    background-image: linear-gradient(
      var(--angle-gradient-value),
      rgb(254, 168, 37),
      rgb(254, 146, 37)
    );
  }
  .level-16,
  .level-17,
  .level-18 {
    background-image: linear-gradient(
      var(--angle-gradient-value),
      rgb(254, 50, 126),
      rgb(238, 67, 96)
    );
  }
  .level-19,
  .level-20,
  .level-21 {
    background-image: linear-gradient(
      var(--angle-gradient-value),
      rgb(50, 55, 60),
      rgb(20, 25, 30)
    );
  }
  .level-22,
  .level-23,
  .level-24,
  .level-25,
  .level-26,
  .level-27,
  .level-28,
  .level-29,
  .level-30,
  .level-31,
  .level-32,
  .level-33,
  .level-34,
  .level-35,
  .level-36,
  .level-37,
  .level-38,
  .level-39,
  .level-40 {
    background-repeat: no-repeat;
    background-position: left;
    background-size: auto 100%;
    animation: lv-animate 7s linear infinite;
  }
  .level-22,
  .level-23 {
    background-image: url('https://cdn.max-c.com/heybox/dailynews/img/********************************.png');
  }
  .level-24,
  .level-25 {
    background-image: url('https://cdn.max-c.com/heybox/dailynews/img/********************************.png');
  }
  .level-26,
  .level-27,
  .level-28,
  .level-29,
  .level-30,
  .level-31,
  .level-32,
  .level-33,
  .level-34,
  .level-35,
  .level-36,
  .level-37,
  .level-38,
  .level-39,
  .level-40 {
    background-image: url('https://imgheybox.max-c.com/oa/2022/05/09/********************************.png');
  }
}
</style>
