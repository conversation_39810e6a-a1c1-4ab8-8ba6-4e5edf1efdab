<template>
  <div class="cpt-action-news">
    <NewsItemHeader :user="newsData.user">
      <template #default>
        <div class="extension-content">
          <span class="text">将</span>
          <div class="icon">
            <img
              :src="newsData.action.game.icon"
              alt=""
            />
          </div>
          <span class="text">{{ newsData.action.game.name }}</span>
          <span class="text">{{ newsData.action.desc }}</span>
          <span class="time">{{ newsData.action.time ?? '4分钟前' }}</span>
        </div>
      </template>
    </NewsItemHeader>
  </div>
</template>
<script setup name="ActionNews">
import { defineProps } from 'vue';
import NewsItemHeader from '../NewsItemHeader/index.vue';

const props = defineProps({
  newsData: {
    type: Object,
    default: () => {},
  },
});
</script>

<style lang="scss"></style>
