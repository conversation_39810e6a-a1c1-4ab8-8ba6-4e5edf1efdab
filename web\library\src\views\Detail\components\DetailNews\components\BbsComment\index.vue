<template>
  <div class="cpt-bbs-comment">
    <NewsItemHeader :user="newsData.user">
      <template #default>
        <div class="extension-content">
          <span class="text">评价了</span>
          <div class="icon">
            <img
              :src="newsData.comment.game.icon"
              alt=""
            />
          </div>
          <span class="text">{{ newsData.comment.game.name }}</span>
        </div>
      </template>
    </NewsItemHeader>
    <div class="bbs-comment-rate-info">
      <Rate :rate="newsData.rate ?? 4" />
      <div class="play-time">{{ '总时长 1253.1h' }}</div>
    </div>
    <div
      class="bbs-comment-title"
      v-html="newsData.title"
    ></div>
    <div
      class="bbs-comment-content"
      v-if="newsData.description"
      v-html="newsData.description"
    ></div>
    <BbsImgs
      class="bbs-content-imgs"
      v-if="newsData.images"
      :imgs="newsData.images"
    />
    <BbsVideo
      class="bbs-content-video"
      v-if="newsData.video"
      :video="newsData.video"
    ></BbsVideo>
    <NewsItemBottom :newsData="newsData" />
  </div>
</template>

<script setup name="BbsContent">
import { defineProps, defineAsyncComponent } from 'vue';
import NewsItemHeader from '../NewsItemHeader/index.vue';
import NewsItemBottom from '../NewsItemBottom/index.vue';
import Rate from '@/components/func/Rate.vue';
const BbsImgs = defineAsyncComponent(() =>
  import('@/components/bbs/BbsImgs.vue')
);
const BbsVideo = defineAsyncComponent(() =>
  import('@/components/bbs/BbsVideo.vue')
);

const props = defineProps({
  newsData: {
    type: Object,
    default: () => {},
  },
});
</script>

<style lang="scss">
.cpt-bbs-comment {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 14px;
  align-self: stretch;

  cursor: pointer;

  .bbs-comment-rate-info {
    display: flex;
    align-items: center;
    gap: 6px;
    .play-time {
      color: var(---general-color-text-2, #64696e);
      font-size: 12px;
      white-space: nowrap;
    }
  }

  .bbs-comment-title {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    align-self: stretch;
    overflow: hidden;
    color: 1111111;
    text-overflow: ellipsis;
    font-family: 'PingFang SC';
    font-size: 17px;
    font-style: normal;
    font-weight: 500;
    line-height: 25px;
  }

  .bbs-comment-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    align-self: stretch;
    overflow: hidden;
    color: 1111111;
    font-feature-settings: 'liga' off, 'clig' off;
    text-overflow: ellipsis;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 23px;
  }
}
</style>
