const { contextBridge, ipc<PERSON>ender<PERSON> } = require('electron');
const store = require('@heybox/electron-utils/store')
const { version } = require('../../package.json');
const { NODE_BIT } = require('../../env');

window.exe_bit = NODE_BIT;
window.windows_version = '';

contextBridge.exposeInMainWorld('electronAPI', {
  // 发送消息到主进程
  send: (channel, ...args) => {
    ipcRenderer.send(channel, ...args);
  },

  // 从主进程接收消息
  receive: (channel, callback) => {
    // 添加新的监听器
    ipcRenderer.on(channel, (event, ...args) => callback(...args));
  },

  // 调用主进程方法并等待结果
  invoke: async (channel, ...args) => {
    return await ipcRenderer.invoke(channel, ...args);
  },

  sentryReport: (message, stack) =>
    ipcRenderer.send('sentryReport', message, stack),
  setCacheData: (k, v) => {
    ipcRenderer.send('levelstore:set', k, JSON.parse(v));
  },
  delCacheData: (k) => ipcRenderer.invoke('levelstore:del', k),
  getCacheData: (k) => ipcRenderer.invoke('levelstore:get', k),
  getStoreData: (k, base = 'popup_config') => {
    return store.get(`${base}${k ? `.${k}` : ''}`);
  },
  setStoreData: (k, v, base = 'popup_config') => {
    store.set(`${base}${k ? `.${k}` : ''}`, v);
  },
  delStoreData: (k, base = 'popup_config') => {
    try {
      if (!k) throw new Error('k is required');
      return store.del(`${base}.${k}`);
    } catch (error) {
      console.error('delStoreData error', error);
    }
  },
  onStoreChange: (k, cb, base = 'popup_config') => {
    return store.original_store.onDidChange(
      `${base}${k ? `.${k}` : ''}`,
      (v) => {
        cb(v);
      }
    );
  },
  getRegeditKey: (path, key) => ipcRenderer.invoke('getRegeditKey', path, key),
  getFileData: (path) => ipcRenderer.invoke('getFileData', path),
  getDeviceId: () => ipcRenderer.invoke('getDeviceId'),
  getLogFileData: (v) => ipcRenderer.invoke('get-log-file-zip'),
  openMiniProgram: (moduleName, options) => ipcRenderer.invoke('openMiniProgram', moduleName, options),
  base: 'popup_config',
});

contextBridge.exposeInMainWorld('popupAPI', {
  onShow: (callback) => ipcRenderer.on('popup:show', callback),
  onHide: (callback) => ipcRenderer.on('popup:hide', callback),
  showSuccess: () => ipcRenderer.send('popup:show-success'),
  // TODO sendEvent 包含组件名和事件名
  sendEvent: (event, cptName, eventName, ...args) =>
    ipcRenderer.send('popup:event', event, cptName, eventName, ...args),
  hideWindow: () => ipcRenderer.send('popup:close'),
});

contextBridge.exposeInMainWorld('webViewAPI', {
  load: (viewId) => ipcRenderer.send('webView:load', viewId),
  destroy: (viewId) => ipcRenderer.send('webView:destroy', viewId),
  postMessage: (...args) => ipcRenderer.send('webView:postMessage', ...args),
  onMessage: (event, callback) => {
    ipcRenderer.on('webView:receiveMessage', (receivedEvent, ...args) => {
      console.log("🚀 ~ ipcRenderer.on ~ event === receivedEvent:", event === receivedEvent)
      if (event === receivedEvent) {
        console.log("🚀 ~ ipcRenderer.on ~ event === receivedEvent:", event === receivedEvent)
        callback(...args);
      }
    });
  },
  loadDefaultWebView: () => ipcRenderer.send('webView:load', 'default'),
  getDefaultWebView: () => ipcRenderer.invoke('webView:getDefaultWebView'),
});

contextBridge.exposeInMainWorld('accAPI', {
  getUserInfo: () => ipcRenderer.invoke('acc-sdk:get-user-info'),
  onEventListener: (events) => {
    Object.keys(events).forEach(key => {
      ipcRenderer.on(key, (e, ...args) => {
        events[key](...args)
      })
    })
  },

  // sdk
  getProxyNodeList: (params) => ipcRenderer.invoke('heybox:get-proxy-node-list', params),
  stopAcc: (params) => ipcRenderer.invoke('heybox:sdk-stop-accelerate', params),
  startAcc: (params) => ipcRenderer.invoke('heybox:sdk-start-accelerate', params),
  recharge: (params) => ipcRenderer.invoke('heybox:recharge', params),
  sendRequest: (url, method, params, form) => ipcRenderer.invoke('heybox:send-request', url, method, params, form),
  openInBrowser: (url) => ipcRenderer.send('heybox:open-in-browser', url),
  getVersion: () => ipcRenderer.invoke('heybox:get-version'),
  getGameInfo: (params) => ipcRenderer.invoke('heybox:get-game-info', params),
  userCertify: (params) => ipcRenderer.invoke('heybox:user-certify', params),
})

contextBridge.exposeInMainWorld('versionAPI', {
  version,
  exe_bit: NODE_BIT,
  // 获取系统版本
  getWindowsVersion: () => ipcRenderer.invoke('get-windows-version'),
  // 设置下一次启动的系统版本
  setAsarVersion: (version, relaunch) =>
    ipcRenderer.send('set-asar-version', version, relaunch),
  // 获取客户端版本
  getClientVersion: () => ipcRenderer.invoke('get-client-version'),
  onUpdateExeProgress: (callback) => {
    ipcRenderer.on('update-exe-progress', callback)
  },
  onUpdateExeResult: (callback) => {
    ipcRenderer.on('update-exe-result', callback)
  },
  // 下载版本资源文件
  updateClientVersion: (data) => ipcRenderer.send('update-client-version', data),
  // 更新ASAR版本
  updateAsarResource: (version, download_url, callback) => {
    let callbackHandler = (event, action, param) => {
      callback(action, param)
      if (['success', 'error'].includes(action)) {
        ipcRenderer.off('updateAsarResource:callback', callbackHandler)
      }
    }
    ipcRenderer.on('updateAsarResource:callback', callbackHandler)
    ipcRenderer.send('updateAsarResource', version, download_url)
  },
})

contextBridge.exposeInMainWorld('steamAPI', {
  // Steam登录
  login: (options) => ipcRenderer.invoke('steam:login', options),
  
  // 切换Steam账号
  switchAccount: (accountName) => ipcRenderer.invoke('switchSteamAccount', accountName),
  
  // 设置离线模式
  setOfflineMode: (accountName) => ipcRenderer.invoke('setOfflineMode', accountName),
  
  // 监听Steam登录过程中的事件
  onGuardCodeRequired: (callback) => {
    ipcRenderer.on('steam:guard-code-required', (event, data) => {
      callback(data)
    })
  },

  onAppConfirmRequired: (callback) => {
    ipcRenderer.on('steam:app-confirm-required', (event, data) => {
      callback(data)
    })
  },

  onLoginErrorListener: (callback) => {
    ipcRenderer.on('steam:login-error', (event, data) => {
      callback(data)
    })
  },

  onLoginProgress: (callback) => {
    ipcRenderer.on('steam:login-progress', (event, progress) => {
      callback(progress)
    })
  },

  removeLoginProgressListener: () => {
    ipcRenderer.removeAllListeners('steam:login-progress')
  },

  removeLoginErrorListener: () => {
    ipcRenderer.removeAllListeners('steam:guard-code-required')
  },

  removeAppConfirmRequired: () => {
    ipcRenderer.removeAllListeners('steam:app-confirm-required')
  },

  // 发送用户输入
  sendGuardCode: (code) => ipcRenderer.send('steam:guard-code-input', code),
  sendAppConfirm: (result) => ipcRenderer.send('steam:app-confirm-input', result),
})
