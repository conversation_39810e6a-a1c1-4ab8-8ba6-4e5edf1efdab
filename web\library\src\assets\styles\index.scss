/* 全局CSS样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  &:not(.no-reset-scrollbar) {
    &::-webkit-scrollbar {
      height: 0;
      width: 8px;
    }
    // /* 两个滚动条交接处 -- x轴和y轴 */
    &::-webkit-scrollbar-corner {
      background-color: transparent;
    }
    /* 滚动条滑块 */
    &::-webkit-scrollbar-thumb {
      border-radius: 20px;
      box-shadow: 6px 0 0 #ffffff14 inset;
      // background: rgba(244, 0, 0, 0.1);
      border: rgba(0, 0, 0, 0) 2px solid;
    }
    /* 滚动条轨道 */
    &::-webkit-scrollbar-track {
      background: transparent;
    }
  }
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  user-select: none;
  overflow: hidden;
}

a {
  text-decoration: none;
  color: #409eff;
}

button, input, select, textarea {
  outline: none;
  font-family: inherit;
}

ul, ol {
  list-style: none;
}
