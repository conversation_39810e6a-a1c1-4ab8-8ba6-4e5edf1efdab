const {
  getLowerName,
} = require('./utils')
const { 
  TASK_BLACKLIST, 
} = require('./assets/constant')
const { log } = require('@heybox/electron-utils')

/**
 * 获取进程在游戏进程中的匹配数据
 * @param {Object} processItem
 * @param {Object} game_process_map
 * @returns {Object} { processItem: { process_name, window_name }, game: { game_name, certification }, match_levelL}
 */
function matchOfficialGame(processItem, game_process_map) {
  let { process_name, window_name } = processItem
  window_name = window_name && getLowerName(window_name)
  process_name = getLowerName(process_name)
  let match_process_name = process_name
  if (global.process_blacklist[process_name]) {
    return null
  }
  let process_match = game_process_map[process_name]
  if (!process_match) {
    let new_process_name = process_name.replace('.exe', '')
    process_match = game_process_map[new_process_name]
    match_process_name = new_process_name
  }
  if (process_match) {
    // 如果进程map中存在window_name，需要确保window_name也正确
    if (window_name !== undefined && process_match.window_name && process_match.window_name !== window_name) {
      return null
    }
    return { 
      processItem, 
      game: { game_name: process_match.game_name, title: window_name, certification: true }, 
      match_level: {
        level: process_match.level || 0,
        match_window_name: !!process_match.window_name
      },
      match_process_name,
    }
  }
}

/**
 * 获取进程在用户游戏识别进程中的匹配数据
 * @param {Object} processItem
 * @param {Object} user_game_process_map
 * @returns {Object} { processItem: { process_name, window_name }, game: { game_name, certification }, is_user_game: true}
 */
function matchUserGame(processItem, user_game_process_map) {
  let { process_path } = processItem
  let match_data = user_game_process_map[getLowerName(process_path)]
  return match_data && {
    processItem,
    game: match_data,
    is_user_game: true,
  }
}

/**
 * 获取更高优先级的 进程在游戏进程中的匹配数据
 * 优先级判断通过三步
 * @param {Object} data1 data1和data2都是matchOfficialGame的返回值
 * @param {Object} data2
 * @returns {Object} data1 || data2
 */
function getHigherPriorityProcessMatch(data1, data2) {
  if (!data1 || !data2) return data1 || data2

  try {
    // 如果都有match_level字段，说明都是官方库中的游戏，按照游戏的优先级匹配
    // * 1. 有window_name的优先级更高
    // * 2. match_level小的优先级更高
    if (data1.match_level && data2.match_level) {
      const match_level_1 = data1.match_level || {}
      const match_level_2 = data2.match_level || {}
      if (match_level_1.match_window_name !== match_level_2.match_window_name) {
        if (match_level_2.match_window_name) {
          return data2
        } else if (match_level_1.match_window_name) {
          return data1
        }
      }

      const level_1 = match_level_1.level || 0
      const level_2 = match_level_2.level || 0
      if (level_1 <= level_2) {
        return data1
      } else {
        return data2
      }
    } else {
      // 如果不有match_level字段，说明不都是官方库中的游戏
      // 如果一个是用户游戏列表中的进程，一个不是，优先返回是用户游戏列表里的进程
      if (data1.is_user_game !== data2.is_user_game) {
        if (data1.is_user_game) {
          return data1
        } else if (data2.is_user_game) {
          return data2
        }
      } else {
        // 最后优先返回是游戏的进程
        if (data2.match_level) {
          return data2
        } else {
          return data1
        }
      }
    }
  } catch (e) {
    log.info('[getHigherPriorityProcessMatch error]', e)
    return data1
  }

}

function filterProcessBlackList(processList) {
  return processList.filter((v) => { // 过滤进程黑名单
    if (v.process_path && TASK_BLACKLIST.find(black => v.process_path.includes(black))) {
      return false
    }
    if (TASK_BLACKLIST.find(black => v.process_name.includes(black))) {
      return false
    }
    return true
  })
}

module.exports = {
  matchOfficialGame,
  matchUserGame,
  getHigherPriorityProcessMatch,
  filterProcessBlackList,
}