<template>
  <div class="cpt-friends">
    <div class="comp-title friends-title">游戏好友</div>

    <!-- 正在游玩的好友 -->
    <div class="friends-section playing-section" v-if="playingFriends.length > 0">
      <div class="section-title">{{ playingFriends.length }} 位好友正在游玩</div>
      <div class="friends-list">
        <FriendUserItem
          v-for="friend in playingFriends"
          :key="friend.heybox_id"
          :friend="friend"
          :isPlaying="true"
        />
      </div>
    </div>

    <!-- 最近游玩的好友 -->
    <div class="friends-section" v-if="recentFriends.length > 0">
      <div class="section-title">{{ recentFriends.length }} 位好友最近玩过</div>
      <div class="friends-grid">
        <FriendUserItem
          v-for="friend in recentFriends"
          :key="friend.heybox_id"
          :friend="friend"
          :showPlayTime="true"
          :playTime="friend.playTime"
        />
      </div>
      <div class="view-all-text">查看全部 8 位正在游玩好友</div>
    </div>

    <!-- 之前玩过的好友 -->
    <div class="friends-section" v-if="previousFriends.length > 0">
      <div class="section-title">{{ previousFriends.length }} 位好友之前玩过</div>
      <div class="friends-avatars friends-before">
        <Avatar
          v-for="friend in previousFriends"
          :key="friend.heybox_id"
          :avatar="friend.avatar"
          :width="34"
          :showDecoration="false"
          :onlineStatus="friend.status"
        />
      </div>
      <div class="view-all-text">查看全部 8 位正在游玩好友</div>
    </div>

    <!-- 愿望单好友 -->
    <div class="friends-section" v-if="wishlistFriends.length > 0">
      <div class="section-title">{{ wishlistFriends.length }} 位好友加入愿望单</div>
      <div class="friends-avatars friends-before">
        <Avatar
          v-for="friend in wishlistFriends"
          :key="friend.heybox_id"
          :avatar="friend.avatar"
          :width="34"
          :showDecoration="false"
          :onlineStatus="friend.status"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="Friends">
import { defineProps, computed } from 'vue';
import FriendUserItem from './components/FriendUserItem.vue';
import Avatar from '@/components/func/Avatar.vue';
import {
  playingFriends as mockPlayingFriends,
  recentFriends as mockRecentFriends,
  previousFriends as mockPreviousFriends,
  wishlistFriends as mockWishlistFriends
} from '../../mock/friends';

const props = defineProps({
  friendsData: {
    type: Object,
    default: () => ({}),
  },
});

// mock 数据，限制显示数量
const playingFriends = computed(() => mockPlayingFriends.slice(0, 5));
const recentFriends = computed(() => mockRecentFriends.slice(0, 6));
const previousFriends = computed(() => mockPreviousFriends.slice(0, 16));
const wishlistFriends = computed(() => mockWishlistFriends.slice(0, 3));
</script>

<style lang="scss">
.cpt-friends {
  background: var(---general-color-primary-0, #fff);
  padding-bottom: 14px;
  border-radius: 8px;

  .friends-title {
    padding: 14px 16px 10px 16px;
  }

  .friends-section {

    &.playing-section {
      .section-title {
        padding: 2px 16px;
      }
    }

    .section-title {
      font-size: 13px;
      font-weight: 500;
      color: #111111;
      font-family: "PingFang SC";
      line-height: normal;
      padding: 8px 16px 2px 16px;
    }

    .friends-list {
      display: flex;
      flex-direction: column;
    }

    .friends-grid {
      display: flex;
      align-items: flex-start;
      align-content: flex-start;
      align-self: stretch;
      flex-wrap: wrap;

      .cpt-friend-user-item {
        min-width: 160px;
      }
    }

    .friends-avatars {
      display: flex;
      flex-wrap: wrap;
      width: 100%;
      overflow: hidden;

      .cpt-account-avatar {
        flex-shrink: 0; // 防止头像被压缩
        .user-avatar {
          border-radius: 50%;
          img {
            border-radius: 50%;
          }
        }
      }

      &.friends-before {
        gap: 4px;
        padding: 6px 16px;
      }
    }

    .view-all-text {
      color: var(---general-color-text-3, #8C9196);
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      padding: 2px 16px 8px 16px;
    }
  }
}
</style>
