import { createStore } from 'vuex'
import { getMyAppList, searchAppList } from '_api/steam'       

// 创建一个新的 store 实例
const store = createStore({
  state () {
    return {
      steam_games: [],
      steam_accounts: [],
      current_game: '',
      current_account: '',
      device_id: '',
      game_order: 'by_2week',
      offset: 0,
      limit: 50,
      is_load_all_games: false,
      is_loading_games: false,
      filter_offset: 0,
      filter_limit: 50,
      is_load_all_filter_games: false,
      is_loading_filter_games: false,
      filters: null,
      filter_games: [],
      is_init_steam_data: false,
      is_library_loading: true,
    }
  },
  mutations: {
    setSteamGames (state, list) {
      state.steam_games = list
    },
    setSteamAccounts (state, { accountList, currentAccount }) {
      state.steam_accounts = accountList
      state.current_account = currentAccount
    },
    setCurrentAccount (state, steam_id) {
      state.current_account = steam_id
    },
    setCurrentGame (state, appid) {
      state.current_game = appid
    },
    setDeviceId (state, deviceId) {
      state.device_id = deviceId
    },
    setOffset (state, offset) {
      state.offset = offset
    },
    setIsLoadingGames (state, isLoading) {
      state.is_loading_games = isLoading
    },
    setIsLoadAllGames (state, isLoadAll) {
      state.is_load_all_games = isLoadAll
    },
    setFilterOffset (state, offset) {
      state.filter_offset = offset
    },
    setFilterLimit (state, limit) {
      state.filter_limit = limit
    },
    setIsLoadAllFilterGames (state, isLoadAll) {
      state.is_load_all_filter_games = isLoadAll
    },
    setIsLoadingFilterGames (state, isLoading) {
      state.is_loading_filter_games = isLoading
    },
    setFilterGames (state, games) {
      state.filter_games = games
    },
    setGameOrder (state, order) {
      state.game_order = order
    },
    resetGameList (state) {
      state.steam_games = []
      state.offset = 0
      state.is_load_all_games = false
      state.is_loading_games = false
    },
    resetFilterGameList (state) {
      state.filter_games = []
      state.filter_offset = 0
      state.is_load_all_filter_games = false
      state.is_loading_filter_games = false
    },
    setFilters (state, filters) {
      state.filters = filters
    },
    setIsInitSteamData (state, isInit) {
      state.is_init_steam_data = isInit
    },
    setIsLibraryLoading (state, isLoading) {
      state.is_library_loading = isLoading
    }
  },
  actions: {
    async getSteamGames ({ commit, state }) {
      if(state.is_loading_games || state.is_load_all_games) {
        return
      }
      try {
        commit('setIsLoadingGames', true)
        const res = await getMyAppList({
          device_id: state.device_id,
        },{
          sort_by: state.game_order,
          offset: state.offset,
          limit: state.limit,
        })
        if(res.data.status === 'ok') {
          state.steam_games.push(...res.data.result.app_list)
          commit('setOffset', state.offset + state.limit)
          commit('setIsLoadAllGames', res.data.result.app_list.length < state.limit)
        } else {
          console.error('Failed to get steam games:', res.data.message)
          commit('setIsLoadingGames', false)
        }
      } catch (error) {
        console.error('Failed to get steam games:', error)
      } finally {
        commit('setIsLoadingGames', false)
      }
    },
    async searchGames ({ commit, state }) {
      if(state.is_loading_filter_games || state.is_load_all_filter_games) {
        return
      }
      try {
        commit('setIsLoadingFilterGames', true)
        const res = await searchAppList({
          device_id: state.device_id,
        },{
          sort_by: state.game_order,
          offset: state.filter_offset,
          limit: state.filter_limit,
          ...state.filters,
        })  
        if(res.data.status === 'ok') {
          state.filter_games.push(...res.data.result.app_infos)
          commit('setFilterOffset', state.filter_offset + state.filter_limit)
          commit('setIsLoadAllFilterGames', res.data.result.app_infos.length < state.filter_limit)
        } else {
          console.error('Failed to search steam games:', res.data.message)
          commit('setIsLoadingFilterGames', false)
        }
      } catch (error) {
        console.error('Failed to search steam games:', error)
      } finally {
        commit('setIsLoadingFilterGames', false)
      }
    },
  },
  getters: {
    currentSidebarGames: (state) => {
      return state.filters
        ? state.filter_games
        : state.steam_games
    }
  }
})

export default store