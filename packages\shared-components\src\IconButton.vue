<template>
  <button 
    :class="[
      'icon-button', 
      'test',
      `icon-button--${type}`, 
      { 'icon-button--disabled': disabled }
    ]"
    :disabled="disabled"
    @click="handleClick"
  >
    <span v-if="iconPosition === 'left'" class="icon-wrapper left">
      <slot name="icon"></slot>
    </span>
    <span class="button-text">
      <slot></slot>
    </span>
    <span v-if="iconPosition === 'right'" class="icon-wrapper right">
      <slot name="icon"></slot>
    </span>
  </button>
</template>

<script>
export default {
  name: 'IconButton',
  props: {
    type: {
      type: String,
      default: 'primary',
      validator: value => ['primary', 'success', 'warning', 'danger', 'info'].includes(value)
    },
    disabled: {
      type: Boolean,
      default: false
    },
    iconPosition: {
      type: String,
      default: 'left',
      validator: value => ['left', 'right'].includes(value)
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const handleClick = (event) => {
      if (!props.disabled) {
        emit('click', event);
      }
    };

    return {
      handleClick
    };
  }
};
</script>

<style scoped>
.icon-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.icon-button--primary {
  background-color: #1976d2;
  color: white;
}

.icon-button--success {
  background-color: #4caf50;
  color: white;
}

.icon-button--warning {
  background-color: #ff9800;
  color: white;
}

.icon-button--danger {
  background-color: #f44336;
  color: white;
}

.icon-button--info {
  background-color: #2196f3;
  color: white;
}

.icon-button--disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.icon-button:hover:not(.icon-button--disabled) {
  opacity: 0.9;
}

.icon-wrapper {
  display: flex;
  align-items: center;
}

.left {
  margin-right: 6px;
}

.right {
  margin-left: 6px;
}
</style> 