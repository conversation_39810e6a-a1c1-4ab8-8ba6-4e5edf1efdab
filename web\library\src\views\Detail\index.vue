<template>
  <div class="view-game-detail">
    <SkeletonGameDetail v-if="isLoading" />

    <div v-else class="game-detail-content">
      <div class="top-banner-container">
        <div class="top-banner-bg" :class="{ 'no-hero': !gameInfo.library_image?.hero }">
          <img
            v-if="gameInfo.library_image?.hero"
            :src="gameInfo.library_image?.hero"
            alt="banner"
          />
          <div
            v-if="shouldShowGameLogo(gameInfo.library_image)"
            class="game-logo"
            :class="getLogoPositionClasses(gameInfo.library_image?.logo_pinned_position)"
            :style="getLogoInlineStyles(gameInfo.library_image)"
          >
            <img :src="gameInfo.library_image.logo" alt="game logo" />
          </div>
          <div v-else-if="!gameInfo.library_image?.logo" class="game-fallback">
            <div class="fallback-logo">
              <img src="https://imgheybox.max-c.com/dev/bbs/2025/07/01/********************************.png" alt="缺省图标" />
            </div>
            <div v-if="gameInfo.name" class="game-name">{{ gameInfo.name }}</div>
          </div>
        </div>
        <div
          class="back-btn"
          @click="handleBackButton"
        >
          <svg
            width="16"
            height="16"
            viewBox="0 0 14 14"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fill-rule="evenodd"
              clip-rule="evenodd"
              d="M10.1257 1.21956C10.4184 1.49607 10.4316 1.95757 10.1551 2.25034L5.66963 6.99968L10.1551 11.749C10.4316 12.0418 10.4184 12.5033 10.1257 12.7798C9.83289 13.0563 9.37139 13.0431 9.09489 12.7503L4.13655 7.50034C3.8711 7.21932 3.8711 6.78003 4.13655 6.49901L9.09489 1.24901C9.37139 0.95624 9.83289 0.943054 10.1257 1.21956Z"
              fill="white"
              fill-opacity="1"
            />
          </svg>
        </div>
        <div
          class="status-bar"
          :class="{ 'no-hero-blur': !gameInfo.library_image?.hero_blur }"
          :style="gameInfo.library_image?.hero_blur ? {
            background: `url(${gameInfo.library_image?.hero_blur}) no-repeat right bottom`,
            backgroundSize: 'auto 350px',
          } : {}"
        >
          <div class="status-item">
            <div class="status-item-value">
              {{ formatPlaytime(gameInfo.play_stat?.playtime_forever || 0) }}h
              <span class="status-item-value-tag">{{ formatPercent(gameInfo.play_stat?.playtime_forever_percent || 0) }}%</span>
            </div>
            <div class="status-item-label">当前时长</div>
          </div>
          <div class="status-item">
            <div class="status-item-value">
              {{ formatPlaytime(gameInfo.play_stat?.playtime_2weeks || 0) }}h
              <span class="status-item-value-tag">{{ formatPercent(gameInfo.play_stat?.playtime_2weeks_percent || 0) }}%</span>
            </div>
            <div class="status-item-label">两周内时长</div>
          </div>
          <div class="status-item">
            <div
              class="status-item-value"
              :style="{ fontSize: formatLastPlayed(gameInfo.play_stat?.last_played) === '暂无记录' ? '14px' : '' }"
            >
              {{ formatLastPlayed(gameInfo.play_stat?.last_played) }}
            </div>
            <div class="status-item-label">最后运行时间</div>
          </div>
          <div class="status-item" v-if="gameInfo.play_stat?.total_achievement > 0">
            <div class="achievement-item">
              <div class="achievement-item-bg" :style="calcAchivementsStyle"></div>
              <span>{{ gameInfo.play_stat?.achieved_achievement || 0 }}/{{ gameInfo.play_stat?.total_achievement || 0 }}</span>
              <div v-if="gameInfo.play_stat?.achieved_achievement >= gameInfo.play_stat?.total_achievement" class="achievement-item-icon">
                <img
                  src="https://imgheybox.max-c.com/dev/bbs/2025/06/04/********************************.png"
                  alt=""
                />
              </div>
            </div>
            <div class="status-item-label">成就进度</div>
          </div>
        </div>
      </div>
      <div class="game-info-container">
        <div class="game-info-main">
          <Tools 
            v-if="gameInfo.mini_program_list && gameInfo.mini_program_list.length > 0"
            :tools="gameInfo.mini_program_list" />
          <DetailNews :news="news" v-if="news && news.length > 0"/>
          <EmptyNews v-else />
        </div>
        <div class="game-info-sub">
          <template v-if="(gameInfo.friends && gameInfo.friends.length > 0) || hasValidAchievementData(gameInfo.achievement_stat)">
            <Friends
              v-if="gameInfo.friends && gameInfo.friends.length > 0"
              :friendsData="gameInfo.friends"
            />
            <Achievement
              v-if="hasValidAchievementData(gameInfo.achievement_stat)"
              :achievementData="gameInfo.achievement_stat"
            />
          </template>
          <EmptyFriendsAchievement v-else />
        </div>
      </div>

      <BottomActionBar
        :tools="gameInfo.mini_program_list"
        :boosterInfo="gameInfo.booster_info"
        :gameStatus="gameStatus"
        :accounts="gameInfo.accounts"
        @gameStatusChanged="handleGameStatusChanged"
      />

    </div>
  </div>
</template>

<script setup name="ViewGameDetail">
import { ref, watch, onMounted, onUnmounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { getMyAppDetail } from '_api/steam';
import { getOSType, getLogoPositionClasses, getLogoInlineStyles, shouldShowGameLogo, formatLastPlayed, formatPlaytime } from '@heybox-app-web-shared/utils';
import steamGames from '@/assets/js/steam-games.js';
import Tools from './components/Tools/index.vue';
import Friends from './components/Friends/index.vue';
import Achievement from './components/Achievement/index.vue';
import EmptyFriendsAchievement from './components/Friends/components/EmptyFriendsAchievement.vue';
import BottomActionBar from './components/BottomActionBar/index.vue';
import DetailNews from './components/DetailNews/index.vue';
import EmptyNews from './components/DetailNews/components/EmptyNews/index.vue';
import SkeletonGameDetail from './components/Skeleton/index.vue';

const router = useRouter();

const appid = ref(router.currentRoute.value.params.appid);
const gameInfo = ref({});
const news = ref([]);
const isLoading = ref(true);

let gameStatusInterval = null;

const gameStatus = ref({
  status: 'ready', // ready, installing, updating, running, toSteam
  progress: 0,
  speed: '2.9MB/s',
});

const downloadStatusCallback = ref(null);

const checkGameStatus = async () => {
  if (!appid.value) return;

  if (gameStatus.value.isDownloading) {
    return;
  }

  const localExeFile = window.electronAPI.getStoreData(`local_exe_${appid.value}`, 'library_config');

  if (localExeFile && localExeFile.path) {
    await checkLocalExeStatus(localExeFile);
  } else {
    await checkSteamGameStatus();
  }
};

// 检查本地 exe 文件状态
const checkLocalExeStatus = async (localExeFile) => {
  try {
    const isRunning = await window.electronAPI.invoke('check-local-exe-running', localExeFile.path);

    gameStatus.value = {
      status: isRunning ? 'running' : 'ready',
      progress: 0,
      speed: '',
      isLocalExe: true
    };

    console.log(`[Detail] Local exe status: ${isRunning ? 'running' : 'ready'}`);
  } catch (error) {
    console.error('[Detail] Failed to check local exe status:', error);
    // 如果检查失败，默认为 ready 状态
    gameStatus.value = {
      status: 'ready',
      progress: 0,
      speed: '',
      isLocalExe: true
    };
  }
};

// 检查 Steam 游戏状态
const checkSteamGameStatus = async () => {
  try {
    const status = await steamGames.getGameButtonStatus(parseInt(appid.value));
    gameStatus.value = status;
    console.log(`[Detail] Steam game status: ${status.status}`);
  } catch (error) {
    console.error('[Detail] Failed to check Steam game status:', error);

    if (error.message.includes('Steam restart required')) {
      gameStatus.value = {
        status: 'toSteam',
        progress: 0,
        speed: '',
        requiresSteamRestart: true
      };
    } else {
      gameStatus.value = {
        status: 'toSteam',
        progress: 0,
        speed: ''
      };
    }
  }
};

const handleDownloadStatusUpdate = (downloadStatus) => {
  if (!appid.value || !downloadStatus) return;

  const isCurrentGame = downloadStatus.appid && downloadStatus.appid.toString() === appid.value.toString();

  if (isCurrentGame) {
    handleCurrentGameDownloadStatus(downloadStatus);
  } else {
    handleOtherGameDownloadStatus();
  }
};

const handleCurrentGameDownloadStatus = (downloadStatus) => {
  if (downloadStatus.downloading) {
    updateDownloadingStatus(downloadStatus);
  } else if (downloadStatus.paused) {
    updatePausedStatus();
  }
};

// 更新下载中状态
const updateDownloadingStatus = (downloadStatus) => {
  const networkSpeed = downloadStatus.network_speed || 0;
  const speed = `${networkSpeed}MB/s`;
  const progress = downloadStatus.progress || 0;
  const progressText = downloadStatus.progress_text || '';
  const status = downloadStatus.is_update ? 'updating' : 'installing';

  gameStatus.value = {
    status,
    progress,
    speed,
    progressText,
    isDownloading: true
  };

  console.log(`[Detail] Download status: ${status} ${progress}% ${speed}`);
};

// 更新暂停状态
const updatePausedStatus = () => {
  gameStatus.value = {
    status: 'ready',
    progress: 0,
    speed: '',
    isDownloading: false
  };
};

// 处理其他游戏的下载状态
const handleOtherGameDownloadStatus = () => {
  if (gameStatus.value.isDownloading) {
    gameStatus.value.isDownloading = false;
    checkGameStatus();
  }
};

const startStatusCheck = () => {
  checkGameStatus();

  // 设置下载状态监听
  setupDownloadStatusListener();

  // 启动实时游戏状态监控
  startGameStatusMonitoring();
};

// 设置下载状态监听器
const setupDownloadStatusListener = () => {
  if (downloadStatusCallback.value) {
    steamGames.offDownloadStatusChange(downloadStatusCallback.value);
  }

  downloadStatusCallback.value = handleDownloadStatusUpdate;
  steamGames.onDownloadStatusChange(downloadStatusCallback.value);
};

// 启动游戏状态监控
const startGameStatusMonitoring = () => {
  stopGameStatusMonitoring(); // 先停止现有的监控

  const monitoringInterval = 2000;

  gameStatusInterval = setInterval(async () => {
    try {
      await checkGameStatus();
    } catch (error) {
      console.error('[Detail] Error during status monitoring:', error);
    }
  }, monitoringInterval);

  console.log(`[Detail] Game status monitoring started (interval: ${monitoringInterval}ms)`);
};

const stopGameStatusMonitoring = () => {
  if (gameStatusInterval) {
    clearInterval(gameStatusInterval);
    gameStatusInterval = null;
  }
};

const calcAchivementsStyle = computed(() => {
  if(gameInfo.value.play_stat?.total_achievement > 0) {
    return {
      width: `${gameInfo.value.play_stat?.achieved_achievement / gameInfo.value.play_stat?.total_achievement * 100}%`,
      borderRadius: gameInfo.value.play_stat?.achieved_achievement === gameInfo.value.play_stat?.total_achievement ? '6px' : '6px 0 0 6px',
    }
  }
  return {
    width: '0%',
    borderRadius: '6px 0 0 6px',
  }
})

const stopStatusCheck = () => {
  // 停止下载状态监听
  stopDownloadStatusListener();

  // 停止游戏状态监控
  stopGameStatusMonitoring();
};

const stopDownloadStatusListener = () => {
  if (downloadStatusCallback.value) {
    steamGames.offDownloadStatusChange(downloadStatusCallback.value);
    downloadStatusCallback.value = null;
  }
};

const handleGameStatusChanged = () => {
  console.log('[Detail] Game status changed, rechecking...');

  const maxChecks = 10;
  const checkInterval = 1000;

  performStatusRecheck(0, maxChecks, checkInterval);
};

// 执行状态重新检查
const performStatusRecheck = async (checkCount, maxChecks, interval) => {
  checkCount++;

  await checkGameStatus();

  const currentStatus = gameStatus.value.status;

  // 简化的继续检查条件：状态不是 running 且未达到最大检查次数
  const shouldContinueChecking = currentStatus !== 'running' && checkCount < maxChecks;

  if (shouldContinueChecking) {
    setTimeout(() => performStatusRecheck(checkCount, maxChecks, interval), interval);
  } else {
    handleStatusCheckCompleted(currentStatus, checkCount);
  }
};

// 处理状态检查完成
const handleStatusCheckCompleted = (finalStatus, totalChecks) => {
  console.log(`[Detail] Status check completed after ${totalChecks} attempts. Final status: ${finalStatus}`);

  // 重新启动状态监控，确保持续监控游戏状态
  startGameStatusMonitoring();
};

const hasValidAchievementData = (achievementStat) => {
  if (!achievementStat || typeof achievementStat !== 'object') {
    return false;
  }

  const { rare_list = [], ordinary_list = [], recent_list = [] } = achievementStat;
  return rare_list.length > 0 || ordinary_list.length > 0 || recent_list.length > 0;
};

const init = async () => {
  isLoading.value = true;
  gameInfo.value = {};

  try {
    const device_id = await window.electronAPI.getDeviceId();
    const res = await getMyAppDetail({
      device_id: device_id,
      x_app: 'heybox_pc',
      x_client_type: 'pc',
      x_os_type: getOSType(),
      version: '999.0.4',
      os_version: window.navigator.userAgent
    }, {
      appid: Number(appid.value),
      "bit": 64,
      "electron_version": "v28"
    });

    if (res.data.status === 'ok') {
      const result = res.data.result;

      if (result.name) {
        gameInfo.value.name = result.name;
      }

      if (result.library_image) {
        gameInfo.value.library_image = result.library_image;
      }

      if (result.booster_info) {
        gameInfo.value.booster_info = result.booster_info;
      }

      if (result.play_stat) {
        gameInfo.value.play_stat = result.play_stat;
      }

      if (result.mini_program_list) {
        gameInfo.value.mini_program_list = result.mini_program_list;
      }

      if (result.achievement_stat) {
        gameInfo.value.achievement_stat = result.achievement_stat;
      }

      if (result.accounts) {
        gameInfo.value.accounts = result.accounts;
      }
    }
  } catch (error) {
    console.error('Failed to fetch app detail:', error);
  }

  // const mockData = await import('./mock/info.json');
  // const mockObj = { ...mockData.default || mockData };

  // 补充gameInfo中不存在的字段
  // Object.keys(mockObj).forEach(key => {
  //   if (gameInfo.value[key] === undefined) {
  //     gameInfo.value[key] = mockObj[key];
  //   }
  // });

  // news.value = (await import('./mock/news.json')).news;

  // 加载完成
  isLoading.value = false;
};

watch(
  () => router.currentRoute.value.params.appid,
  (newAppid) => {
    appid.value = newAppid;
    init();
  },
  {
    immediate: true,
  }
);

watch(appid, (newAppid) => {
  if (newAppid) {
    stopStatusCheck();
    startStatusCheck();
  }
}, { immediate: true });

onMounted(() => {
  startStatusCheck();
});

onUnmounted(() => {
  stopStatusCheck();
});

const handleBackButton = () => {
  router.push({
    path: '/app/home',
  });
};

const formatPercent = (percent) => {
  return parseFloat(percent || 0).toFixed(1);
};
</script>

<style lang="scss">
@import '@/assets/styles/detail_sub_block.scss';

.view-game-detail {
  height: 100vh;
  width: 100%;
  overflow: scroll;
  background: var(---general-color-bg-3, #FAFBFC);
  .top-banner-container {
    width: 100%;
    height: 350px;
    position: relative;
    .top-banner-bg {
      width: 100%;
      height: 100%;
      position: relative;

      &.no-hero {
        background: linear-gradient(46deg, var(---greadient-color-primary-left, #464B50) -0.9%, var(---greadient-color-primary-right, #14191E) 100.9%);
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .game-logo {
        position: absolute;

        &.upper-left {
          top: 0;
          left: 0;
        }
        &.upper-center {
          top: 0;
          left: 50%;
          transform: translateX(-50%);
        }
        &.upper-right {
          top: 0;
          right: 0;
        }
        &.center-left {
          top: 50%;
          left: 0;
          transform: translateY(-50%);
        }
        &.center-center {
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
        }
        &.center-right {
          top: 50%;
          right: 0;
          transform: translateY(-50%);
        }
        &.bottom-left {
          bottom: 24px;
          left: 24px;
        }
        &.bottom-center {
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
        }
        &.bottom-right {
          bottom: 0;
          right: 0;
        }
        img {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      .game-fallback {
        position: absolute;
        bottom: 44px;
        left: 39px;
        display: flex;
        flex-direction: column;
        align-items: flex-start;

        .fallback-logo {
          margin-bottom: 10px;

          img {
            width: 80px;
            height: 80px;
          }
        }

        .game-name {
          color: #FFF;
          font-family: "PingFang SC";
          font-size: 24px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
        }
      }
    }
    .back-btn {
      position: absolute;
      top: 12px;
      left: 24px;
      cursor: pointer;
      display: flex;
      height: 28px;
      width: 28px;
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      background: var(--balck-gamerecord-color-black-50a, rgba(0, 0, 0, 0.5));
    }

    .status-bar {
      position: absolute;
      bottom: 0;
      right: 0;
      display: flex;
      width: auto;
      height: 73px;
      padding: 0px 12px;
      align-items: center;
      border-radius: 8px 0 0 0;
      background: var(
        --white-gamerecord-color-white-05a,
        rgba(255, 255, 255, 0.05)
      );

      &.no-hero-blur {
        opacity: 0.8;
        background: linear-gradient(45deg, #736E7D 0%, #1B2025 100%);
      }

      .status-item {
        display: flex;
        padding: 0px 12px 4px 12px;
        flex-direction: column;
        align-items: flex-start;
        height: 41px;
        justify-content: space-between;
        gap: 2px;
        .status-item-value {
          font-size: 16px;
          line-height: 20px;
          color: var(--white-gamerecord-color-white-100a, #fff);
          font-family: 'ALIBABA Font';
          .status-item-value-tag {
            color: #66b9ff;
            font-size: 8px;
            line-height: 15px;
          }
        }
        .status-item-label {
          color: var(
            --white-gamerecord-color-white-50a,
            rgba(255, 255, 255, 0.5)
          );
          font-size: 10px;
        }

        .achievement-item {
          width: 67px;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 14px;
          border-radius: 6px;
          padding: 0 17px;
          position: relative;
          .achievement-item-bg {  
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            border-radius: 6px 0 0 6px;
            background: linear-gradient(
              90deg,
              #ffd000 0%,
              #e6d50b 35%,
              #92e730 100%
            );
          }

          span {
            color: #fff;
            text-align: center;
            font-family: 'ALIBABA Font';
            font-size: 12px;
            line-height: 14px;
            display: flex;
            align-items: center;
            position: relative;
            z-index: 1;
          }
          .achievement-item-icon {
            width: 22px;
            height: 22px;
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            right: -4px;
            z-index: 1;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
  .game-info-container {
    display: flex;
    padding: 16px 24px 86px;
    /* flex-wrap: wrap; */
    gap: 16px;
    & > div {
      height: fit-content;
    }
    .game-info-main {
      flex: 6;
      min-width: 392px;
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
    .game-info-sub {
      flex: 4;
      min-width: 280px;
      display: flex;
      flex-direction: column;
      gap: 16px;
    }

    .comp-title {
      align-self: stretch;
      color: 1111111;
      font-size: 16px;
      font-weight: 500;
      font-family: 'PingFang SC';
    }
  }
}
</style>
