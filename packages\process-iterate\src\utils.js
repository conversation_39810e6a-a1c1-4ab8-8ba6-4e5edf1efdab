/**
* 检查进程是否为Bilibili直播进程
* @param {Object} param0 - 包含进程路径和进程名称的对象
* @param {string} param0.process_path - 进程的路径
* @param {string} param0.process_name - 进程的名称
* @returns {boolean} 是否为Bilibili直播进程
*/
function checkIsBiliBiliLive({ process_path, process_name }) {
  const { BILIBILI_LIVE_SOFTWARE_TITLE } = require('./assets/constant')
  let exe_name = process_path.split('\\').slice(-1)[0] || process_name
  return exe_name.toLowerCase() === BILIBILI_LIVE_SOFTWARE_TITLE
}


/**
 * 获取与小程序绑定进程关联的进程名称
 * @param {string} process_name - 进程名称
 * @returns {string|undefined} 如果找到，返回匹配的进程名称；否则返回undefined
 */
function getMiniMapKeysByProcessName(process_name) {
  let match_ids = []
  if (process_name) {
    process_name = process_name.toLowerCase()
    for (let mini_pro_id in mini_map) {
      let mini_data = mini_map[mini_pro_id]
      if (mini_data.process_list?.find(data => process_name === data.process_name || process_name.replace('.exe', '') === data.process_name)) {
        match_ids.push(mini_pro_id)
      }
    }
  }
  return match_ids
}

/**
* 处理游戏名的方法 将获取到的游戏名进行格式化 
* @param {String} name 
* @returns 转换为小写
*/
function getLowerName(name) {
  if (!name) return ''
  return name.toLowerCase()
}

/**
 * 获取当前的可执行文件名
 * @param {string} process_path 进程路径
 */
function getExeName(process_path) {
  const pathSplit = process_path.split('\\')
  const pathName = getLowerName(pathSplit[pathSplit.length - 1])
  return pathName
}

/**
* 判断是否需要监听小程序游戏状态的变化
* @returns {boolean} 如果需要监听游戏状态变化，返回true；否则返回false
*/
function isNeedListenMiniProgramProcessState(data) {
  if (!data) return
  return data.listen_exit || data.listen_start || data.isOverlay
}

/**
 * 上报在玩的游戏数量
 */
// function reportGamesCount(count) {
//   const { report } = require('../assets/js/report')
//   report('monitor_game_counts', {
//     count
//   })
// }

module.exports = {
  checkIsBiliBiliLive,
  getMiniMapKeysByProcessName,
  getLowerName,
  getExeName,
  isNeedListenMiniProgramProcessState,
  // reportGamesCount,
}