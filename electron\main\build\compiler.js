const fs = require('fs')
const path = require('path')
const terser = require('terser')
const compiler = require('../bytenode/compiler/index')
const { app } = require('electron')

const outputDir = './source'
const inputDir = './src'
const sourmapDir = './src_'
// const positionCheck = `global.__map_position_check__="${' '.repeat(29)}"`

const whiteList = [
  'preload',
]

let waitCompileFiles = []

async function main () {
  removeHistoryFile()
  walkFiles()
  await compilerJS()
  app.quit()
}
main()

function compilerJS () {
  let count = 0
  return new Promise((resolve) => {
    for (let i=0; i<waitCompileFiles.length; i++) {
      let file = fs.readFileSync(path.join(__dirname, '../', waitCompileFiles[i].path)).toString()
      console.log(waitCompileFiles[i])
      terser.minify(file, {
        sourceMap: {
          filename: waitCompileFiles[i].relativePath,
          includeSources: true
        }
      }).then(({ code, map }) => {
        const targetDir = path.dirname(path.join(sourmapDir, waitCompileFiles[i].relativePath))
        ensureDir(targetDir)
        fs.writeFileSync(
          path.join(__dirname, '../', sourmapDir, waitCompileFiles[i].relativePath),
          code,
          'utf8'
        )
        fs.writeFileSync(
          path.join(__dirname, '../', sourmapDir, waitCompileFiles[i].relativePath + '.map'),
          map,
          'utf8'
        )

        const byteDir = path.dirname(path.join(outputDir, waitCompileFiles[i].relativePath))
        ensureDir(byteDir)
        compiler.compileFile(
          path.join(__dirname, '../', waitCompileFiles[i].path.replace('src\\', 'src_\\')), 
          path.join(__dirname, '../', byteDir),
          code
        )
        console.log(` - Compile: ${waitCompileFiles[i].relativePath}`)
      }).catch((e) => {
        console.log(e)
      }).finally(() => {
        if (++count === waitCompileFiles.length) {
          resolve()
        }
      })
    }
  })
}

function walkFiles () {
  const dirs = [inputDir]
  let currentDir
  while (currentDir = dirs.shift()) {
    const files = fs.readdirSync(currentDir, {
      withFileTypes: true
    })
    for (let i = 0; i < files.length; i += 1) {
      const file = files[i]
      const currentPath = path.join(currentDir, file.name)
      if (file.isDirectory()) {
        dirs.push(currentPath)
        continue
      }

      const fileExt = path.extname(file.name).toLowerCase()
      const fileInfo = Object.freeze({
        path: currentPath,
        relativePath: path.relative(inputDir, currentPath),
        name: file.name,
        ext: fileExt,
        isScript: fileExt === '.js'
      })

      try {
        const action = getFileAction(fileInfo)
        if (!fileInfo.isScript && action === 'compile') {
          throw new Error(`不能编译非JS文件: ${fileInfo.relativePath}`)
        }
        switch (action) {
          case 'compile':
            waitCompileFiles.push(fileInfo)
            break;
          case 'copy':
            const targetDir = path.dirname(path.join(outputDir, fileInfo.relativePath))
            ensureDir(targetDir)
            fs.copyFileSync(fileInfo.path, path.join(targetDir, fileInfo.name))
            console.log(` - Copy: ${fileInfo.relativePath}`)
            break;
          default:
            break;
        }
      } catch (e) {
        console.log(fileInfo, e)
      }
    }
  }
}

function removeHistoryFile () {
  let sourcePath = path.join(__dirname, '../source')
  let src_Path = path.join(__dirname, '../src_')
  if (fs.existsSync(sourcePath)) {
    fs.rmSync(sourcePath, {
      recursive: true
    })
  }
  if (fs.existsSync(src_Path)) {
    fs.rmSync(src_Path, {
      recursive: true
    })
  }
}

function ensureDir(dirPath) {
  try {
    fs.statSync(dirPath)
  } catch (e) {
    fs.mkdirSync(dirPath, {
      recursive: true
    })
  }
}

function getFileAction (fileInfo) {
  let flag = false
  whiteList.forEach((item) => {
    if (fileInfo.relativePath.indexOf(item) >= 0) {
      flag = true
    }
  })
  if (fileInfo.ext === '.map') {
    return 'ignore'
  }

  if (flag) {
    return 'copy'
  } else {
    if (fileInfo.isScript) {
      return 'compile'
    } else {
      return 'copy'
    }
  }
}