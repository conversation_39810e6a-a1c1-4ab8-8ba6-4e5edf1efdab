// import { defineAsyncComponent } from 'vue';

// /**
//  * 定义异步组件
//  * @param {string} cptPath 异步组件的路径
//  * @returns {import('vue').AsyncComponent}
//  */
// const Define = (cptPath) => {
//   return defineAsyncComponent(() => import(/* @vite-ignore */ cptPath));
// };

// export const useDynamicDialogImport = () => {
//   return {
//     UpdateDialog: () => import('../components/UpdateDialog.vue'),
//     QueryDialog: () => import('../components/QueryDialog.vue'),
//     ToolsDialog: () => import('../components/ToolsDialog.vue'),
//     AccelerateDialog: () => import('../components/AccelerateDialog/AccelerateDialog.vue'),
//   };
// };

// TODO 动态组件打包报错，暂时注释
import UpdateDialog from '../components/UpdateDialog.vue';
import QueryDialog from '../components/QueryDialog.vue';
import ToolsDialog from '../components/ToolsDialog.vue';
import AccelerateDialog from '../components/AccelerateDialog/AccelerateDialog.vue';
import UserSettingDialog from '../components/UserSettingDialog/UserSettingDialog.vue';
import FeedbackDetailDialog from '../components/UserSettingDialog/FeedbackDetailDialog.vue';
import RealnameAuth from '../components/AccelerateDialog/RealnameAuth.vue';
import Underage from '../components/AccelerateDialog/Underage.vue';
import SteamLoginDialog from '../components/SteamLoginDialog/SteamLoginDialog.vue';
import LoginDialog from '../components/LoginDialog/LoginDialog.vue';
import SteamInstallDialog from '../components/SteamInstallDialog.vue';

export const useDynamicDialogImport = () => {
  return {
    UpdateDialog,
    QueryDialog,
    ToolsDialog,
    AccelerateDialog,
    UserSettingDialog,
    FeedbackDetailDialog,
    RealnameAuth,
    Underage,
    SteamLoginDialog,
    LoginDialog,
    SteamInstallDialog,
  };
};