const { BrowserWindow } = require('electron')
const path = require('path')
const utils = require('@heybox/electron-utils')
class LoadingWindow {
  windows = {}
  constructor() {}
  start (id) {
    if (this.windows[id]) return
    this.windows[id] = new BrowserWindow({
      width: 320,
      height: 320,
      center: true,
      resizable: false,
      movable: false,
      minimizable: false,
      maximizable: false,
      frame: false,
      focusable: false,
      alwaysOnTop: true,
      transparent: true,
      titleBarStyle: true,
      icon: path.join(__dirname, '../assets/images/favicon.ico'),
      webPreferences: {
        nodeIntegration: false,
        sandbox: true,
        preload: path.join(__dirname, './loading_preload.js')
      }
    })
    this.windows[id].loadFile(path.join(__dirname, './loading/index.html'))
  }
  stop (id, isFinish) {
    if (!this.windows[id]) return
    this.windows[id].close()
    if (!isFinish) {
      this.showMain()
    }
    delete this.windows[id]
  }
  showMain () {
    utils.focusWindow(global.mainWindow)
  }
}

module.exports = new LoadingWindow();