<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>小黑盒加速器插件</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/theme.css">
    <link rel="stylesheet" href="../assets/font/iconfont.css">
  </head>
  <body>
    <div class="dialog-cpt">
      <div class="tip">
        <i class="tip-icon iconfont"></i>
        <div class="tip-content">
          <p class="title"></p>
          <p class="desc"></p>
        </div>
      </div>
      <div class="button-wrapper">
        <button id="confirm-button" class="full-button primary-button">确定</button>
      </div>
    </div>
  </body>
  <script src="./common/initTheme.js"></script>
  <script type="module">
    import { getUrlParam } from './common/utils.js'
    let config = JSON.parse(getUrlParam('config'))
    function init() {
      console.log('config', config)
      document.querySelector('.title').innerHTML = config.content
      document.querySelector('.desc').innerHTML = config.desc ? config.desc : ''
      document.querySelector('.tip-icon').classList.add(`icon-toast-${config.type}`)
      document.getElementById('confirm-button').addEventListener('click', () => {
        window.electronAPI.close('dialog')
      })
      if(config.link) {
        document.querySelector('.link').addEventListener('click', () => {
          window.electronAPI.openInBrowser(config.link)
        })
      }
    }
    init()
  </script>
  <style>
    .dialog-cpt {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 24px;
      background-color: var(--nb1r);
      border-radius: 16px;
      overflow: hidden;
      .tip {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 16px;
        margin-bottom: 12px;
        line-height: 40px;
        .tip-icon {
          font-size: 40px;
        }
        .icon-toast-success {
          color: var(--success);
        }
        .icon-toast-warn {
          color: var(--warn);
        }
        .tip-content {
          .title {
            font-size: 16px;
            font-weight: 500;
            line-height: 18px;
            color: var(--nf1r);
            
          }
          .desc {
            margin-top: 5px;
            font-size: 14px;
            line-height: 24px;
            color: var(--nf3r);
          }
        }
      }
    }
  </style>
</html>
