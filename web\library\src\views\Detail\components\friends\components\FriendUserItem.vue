<template>
  <div class="cpt-friend-user-item">
    <Avatar
      :avatar="friend.avatar"
      :width="avatarSize"
      :showDecoration="false"
      :onlineStatus="friend.status"
    />
    <div class="friend-info">
      <div class="friend-name" :class="{ offline: !friend.status }">{{ friend.nickname }}</div>
      <div class="friend-desc" :class="descriptionClass" v-if="description">{{ description }}</div>
    </div>
  </div>
</template>

<script setup name="FriendUserItem">
import { defineProps, computed } from 'vue';
import Avatar from '@/components/func/Avatar.vue';

const props = defineProps({
  friend: {
    type: Object,
    required: true,
  },
  avatarSize: {
    type: Number,
    default: 34,
  },
  showPlayTime: {
    type: Boolean,
    default: false,
  },
  playTime: {
    type: String,
    default: '',
  },
  isPlaying: {
    type: Boolean,
    default: false,
  },
});

const description = computed(() => {
  if (props.showPlayTime && props.playTime) {
    return props.playTime;
  }
  return props.friend.description || '';
});

const descriptionClass = computed(() => {
  if (props.isPlaying) {
    return 'playing';
  } else if (props.showPlayTime) {
    return 'recent';
  }
  return '';
});
</script>

<style lang="scss">
.cpt-friend-user-item {
  display: flex;
  align-items: center;
  padding: 6px 16px;

  .cpt-account-avatar {
    .user-avatar {
      border-radius: 50%;
      img {
        border-radius: 50%;
      }
    }
  }

  .friend-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-left: 10px;

    .friend-name {
      font-size: 13px;
      font-weight: 400;
      color: #111111;
      line-height: normal;
      font-family: "PingFang SC";

      &.offline {
        color: var(---general-color-text-3, #8C9196);
        font-weight: 400;
      }
    }

    .friend-desc {
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;

      // 默认（近期游玩）
      color: var(---general-color-text-3, #8C9196);

      // 正在游玩状态
      &.playing {
        color: var(---general-color-success, #32B846);
      }

      // 近期游玩状态
      &.recent {
        color: var(---general-color-text-3, #8C9196);
      }
    }
  }
}
</style>
