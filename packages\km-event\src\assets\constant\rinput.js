const rinput_map = { 
  1: "GAMEPAD_X", // 方块 键 
  2: "GAMEPAD_A", // X 键
  4: "GAMEPAD_B", // 圈 键 
  8: "GAMEPAD_Y", // 三角 键
  16: "GAMEPAD_LEFT_SHOULDER", // L1 键
  32: "GAMEPAD_RIGHT_SHOULDER", // R1 键
  64: "LEFT_TRIGGER", // L2 键
  128: "RIGHT_TRIGGER", // R2 键
  256: "GAMEPAD_BACK", // 创建 键（左侧）
  512: "GAMEPAD_START", // 选项 键（右侧）
  1024: "GAMEPAD_LEFT_THUMB", // 左摇杆按键
  2048: "GAMEPAD_RIGHT_THUMB", // 右摇杆按键
};

/**
 * 获取被按下的按钮名称（PS5 手柄）
 * @param {number} value - 按钮的位掩码值
 * @param {number} dpad - 方向键的位掩码值
 * @returns {string[]} - 被按下的按钮名称数组
 */
function getPressedRinputButtons(value, dpad) {
  const pressedButtons = [];

  pressedButtons.push(...getPressedDpadButtons(dpad));

  // 遍历其他按钮
  for (const [bitmask, buttonName] of Object.entries(rinput_map)) {
    if (value & bitmask) {
      pressedButtons.push(buttonName);
    }
  }
  return pressedButtons;
}

/**
 * 获取被按下的方向键名称
 * @param {number} dpad - 方向键的值
 * @returns {string[]} - 被按下的方向键名称数组
 */
function getPressedDpadButtons(dpad) {
  const directionMap = {
    0: ["UP"],
    2: ["RIGHT"],
    4: ["DOWN"],
    6: ["LEFT"],
    1: ["UP", "RIGHT"],
    3: ["RIGHT", "DOWN"],
    5: ["DOWN", "LEFT"],
    7: ["LEFT", "UP"],
  };
  const buttons = (directionMap[dpad] || []).map((direction) => `GAMEPAD_DPAD_${direction}`);
  return buttons;
}

module.exports = { rinput_map, getPressedRinputButtons };