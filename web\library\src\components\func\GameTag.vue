<template>
  <div class="cpt-game-tag">
    <div class="game-tag-icon">
      <img
        :src="game.icon"
        alt="game-tag-icon"
      />
    </div>
    <div class="game-tag-text">{{ game.name }}</div>
  </div>
</template>

<script setup name="GameTag">
import { defineProps } from 'vue';

const props = defineProps({
  game: {
    type: Object,
    default: () => {},
  },
});
</script>

<style lang="scss">
.cpt-game-tag {
  display: flex;
  height: 18px;
  padding: 3px 6px 3px 2px;
  align-items: center;
  gap: 4px;
  border-radius: 2px;
  background-color: var(---general-color-bg-1, #f3f4f5);
  .game-tag-icon {
    width: 16px;
    height: 16px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .game-tag-text {
    color: var(---general-color-text-3, #8c9196);
    font-family: 'PingFang SC';
    font-size: 11px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
  }
}
</style>
