const env = require('./env.js')
const packageJson = require('../main/package.json')
const path = require('path')
const fs = require('fs')
const buildResources = require('@heybox/electron-build-resources')

const outputDir = `out/HeyboxApp_${env.ELECTRON_ENV || 'prod'}_${env.NODE_BIT}_${packageJson.version}`

let files = [];
(() => {
  const NODE_BIT = process.arch.includes('64') ? 64 : 32;
  const result = [
    'src/**/*', 
    'env.js', 
    'package.json', 
    '!**/out', 
    '!**/build',
    '!**/builder.config.js',
    '!**/node_modules/@heybox/**/*',
    '!**/node_modules/@heybox/electron-utils/src/**/*',
    '**/node_modules/@heybox/electron-utils/src/log.js',
    '**/node_modules/@heybox/electron-utils/src/regedit.js',
    '**/node_modules/@heybox/electron-utils/src/require_addon.js',
    '**/node_modules/@heybox/electron-utils/src/version.js',
    '**/node_modules/@heybox/electron-utils/package.json',
  ];

  // 根据node_bit来获取对应的文件
  if (NODE_BIT === 64) {
    result.push(
      '**/node_modules/@heybox/electron-utils/src/addon/reg_helper.node',
      '**/node_modules/@heybox/electron-utils/src/addon/get_endpoint_info.node',
    );
  } else {
    result.push(
      '**/node_modules/@heybox/electron-utils/src/addon/reg_helper_32.node',
      '**/node_modules/@heybox/electron-utils/src/addon/get_endpoint_info_32.node',
    );
  }

  // 清除多余的electron-utils的文件
  const redundantFiles = [
  ]
  result.push(...redundantFiles)
  files = result
})()

module.exports = {
  removePackageScripts: true,
  removePackageKeywords: true,
  appId: "com.heybox.app",
  productName: "HeyboxApp",
  asar: true,
  win: {
    target: ["dir"],
    icon: buildResources.icon,
    signAndEditExecutable: true,
  },
  files,
  directories: {
    output: outputDir,
  },
  beforeBuild: async () => {
    // 在开始构建前清理输出目录
    const outputPath = path.join(process.cwd(), outputDir)
    if (fs.existsSync(outputPath)) {
      console.log('Cleaning output directory:', outputPath)
      fs.rmSync(outputPath, { recursive: true, force: true })
    }
    return true
  },
} 