<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>小黑盒加速器插件</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/theme.css">
    <link rel="stylesheet" href="../assets/font/iconfont.css">
  </head>
  <body>
    <div class="shading"></div>
    <div class="login-cpt">
      <div class="title-wrapper">
        <div class="title">
          登录小黑盒加速器
        </div>
        <svg id="close-button" class="close pointer" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="Dismiss">
            <path id="Shape" d="M4.08859 4.21569L4.14645 4.14645C4.32001 3.97288 4.58944 3.9536 4.78431 4.08859L4.85355 4.14645L10 9.293L15.1464 4.14645C15.32 3.97288 15.5894 3.9536 15.7843 4.08859L15.8536 4.14645C16.0271 4.32001 16.0464 4.58944 15.9114 4.78431L15.8536 4.85355L10.707 10L15.8536 15.1464C16.0271 15.32 16.0464 15.5894 15.9114 15.7843L15.8536 15.8536C15.68 16.0271 15.4106 16.0464 15.2157 15.9114L15.1464 15.8536L10 10.707L4.85355 15.8536C4.67999 16.0271 4.41056 16.0464 4.21569 15.9114L4.14645 15.8536C3.97288 15.68 3.9536 15.4106 4.08859 15.2157L4.14645 15.1464L9.293 10L4.14645 4.85355C3.97288 4.67999 3.9536 4.41056 4.08859 4.21569L4.14645 4.14645L4.08859 4.21569Z" fill="#424242"/>
          </g>
        </svg>
      </div>
      <div class="qr-code-wrapper">
        <div class="qr-code-content">
          <img src="https://imgheybox.max-c.com/oa/2025/04/24/********************************.png">
          <div class="qr-code-state">
            <canvas id="qr-code"></canvas>
            <div class="mask"></div>
            <div class="state state-0">
              <div class="t1">二维码生成中</div>
            </div>
            <div class="state state-2">
              <div class="t2">二维码已失效</div>
              <div class="t2">点击刷新</div>
            </div>
            <div class="state state-3">
              <svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                x="0px" y="0px" viewBox="0 0 48 48" style="enable-background:new 0 0 48 48;" xml:space="preserve">
                <circle cx="24" cy="24" r="24" fill="#46B222" />
                <polyline points="13.5,24 21.5,32 35,17" fill="none" stroke="#ffffff" stroke-width="4"
                  stroke-linecap="round" stroke-linejoin="round" />
              </svg>
              <div class="t1">扫描成功<br>请在手机上确认登录</div>
            </div>
          </div>
        </div>
        <div class="tips">
          <p>打开最新版本 <a class="download-link link">小黑盒App</a></p>
          <p>点击「我的」右上角扫一扫登录</p>
        </div>
      </div>
      <div class="wechat-login-wrapper">
        <div id="qr-code-wechat"></div>
        <div class="tips">
          <p>使用微信扫一扫登录</p>
          <p>“小黑盒”</p>
        </div>
      </div>
      <div class="content-wrapper">
        <div class="content">
          <div class="input-wrapper">
            <input type="text" id="phone" placeholder="请输入手机号">
          </div>
          <div class="input-wrapper">
            <!-- <input type="password" id="password" placeholder="请输入密码"> -->
            <input type="text" id="code" placeholder="请输入验证码">
            <div id="get-code-button"></div>
            <!-- <button id="get-code-button">获取验证码</button> -->
          </div>
          <div class="tip-line">
            <div class="tip">
              <input type="checkbox" name="agree" id="agree">
              我同意
              <a id="privacy-terms" class="link">《<span class="underline">小黑盒加速器服务及隐私条款</span>》</a>
            </div>
            <button class="primary-button disabled" id="login-button">登录/注册</button>
          </div>
        </div>
      </div>
      <div class="footer-wrapper">
        <!-- <button class="ghost-button" id="change-type-button">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="0icon/social/wechat">
              <path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M10.5 11V13C10.5 13.2899 10.3162 13.5 10.125 13.5H4.875C4.68385 13.5 4.5 13.2899 4.5 13V3.5C4.5 3.21011 4.68385 3 4.875 3H10.125C10.3162 3 10.5 3.21011 10.5 3.5V5H8C7.44772 5 7 5.44772 7 6V10C7 10.5523 7.44772 11 8 11H10.5ZM14 5H11.5V3.5C11.5 2.68532 10.9003 2 10.125 2H4.875C4.09966 2 3.5 2.68532 3.5 3.5V13C3.5 13.8147 4.09966 14.5 4.875 14.5H10.125C10.9003 14.5 11.5 13.8147 11.5 13V11H14C14.5523 11 15 10.5523 15 10V6C15 5.44772 14.5523 5 14 5ZM8 6.65085V10H10.5H11.5H14V6.65085L11.3201 8.88411C11.1347 9.03863 10.8653 9.03863 10.6799 8.88411L8 6.65085ZM13.219 6H8.78102L11 7.84915L13.219 6Z" fill="#ADADAD"/>
            </g>
          </svg>
          <p>验证码登录/注册</p>
        </button> -->
        <button class="ghost-button" id="qr-login-button">
          <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="0icon/social/wechat">
              <path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M10.5 4C10.2239 4 10 3.77614 10 3.5C10 3.22386 10.2239 3 10.5 3H11.5C12.3284 3 13 3.67157 13 4.5V5.5C13 5.77614 12.7761 6 12.5 6C12.2239 6 12 5.77614 12 5.5V4.5C12 4.22386 11.7761 4 11.5 4H10.5ZM12 10.5C12 10.2239 12.2239 10 12.5 10C12.7761 10 13 10.2239 13 10.5V11.5C13 12.3284 12.3284 13 11.5 13H10.5C10.2239 13 10 12.7761 10 12.5C10 12.2239 10.2239 12 10.5 12H11.5C11.7761 12 12 11.7761 12 11.5V10.5ZM6 12.5C6 12.2239 5.77614 12 5.5 12H4.5C4.22386 12 4 11.7761 4 11.5V10.5C4 10.2239 3.77614 10 3.5 10C3.22386 10 3 10.2239 3 10.5V11.5C3 12.3284 3.67157 13 4.5 13H5.5C5.77614 13 6 12.7761 6 12.5ZM4 5.5C4 5.77614 3.77614 6 3.5 6C3.22386 6 3 5.77614 3 5.5V4.5C3 3.67157 3.67157 3 4.5 3H5.5C5.77614 3 6 3.22386 6 3.5C6 3.77614 5.77614 4 5.5 4H4.5C4.22386 4 4 4.22386 4 4.5V5.5ZM3 7.5C2.72386 7.5 2.5 7.72386 2.5 8C2.5 8.27614 2.72386 8.5 3 8.5H13C13.2761 8.5 13.5 8.27614 13.5 8C13.5 7.72386 13.2761 7.5 13 7.5H3Z" fill="#616161"/>
            </g>
          </svg>
          <p>扫码登录</p>
        </button>
        <button class="ghost-button" id="wechat-login-button">
          <i class="iconfont icon-log-in-wechat"></i>
          <p>微信登录</p>
        </button>
      </div>
    </div>
  </body>
  <script src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js"></script>
  <script src="./common/initTheme.js"></script>
  <script type="module">
    import './assets/js/sm.js'
    import { toastError } from './common/toast.js'
    
    const QRCode = require('qrcode')
    let login_type = 'code'
    let qr_interval = null
    let code_interval = null
    let code_time = 0
    async function userLogin() {
      let phoneInput = document.getElementById('phone')
      // let passwordInput = document.getElementById('password')
      let codeInput = document.getElementById('code')
      if(!phoneInput.value || !codeInput.value) {
        return
      }
      if(!document.getElementById('agree').checked) {
        toastError('请先同意小黑盒加速器服务及隐私条款')
        return
      }
      // if(login_type === 'password') {
      //   let res = await window.electronAPI.loginByPassword({
      //     phone: phoneInput.value,
      //     code: passwordInput.value,
      //     type: 'pwd',
      //   })
      //   if(res.status !== 'ok') {
      //     toastError(res.msg)
      //     console.error(res.msg)
      //   }
      // } else if(login_type === 'code') {
        let res = await window.electronAPI.loginByCode({
          phone: document.getElementById('phone').value,
          code: document.getElementById('code').value,
          type: 'code',
        })
        if(res.status !== 'ok') {
          toastError(res.msg)
          console.error(res.msg)
        }
      // }
    }
    async function getCode() {
      if(code_time > 0) return
      let res = await window.electronAPI.getCode({
        phone: document.getElementById('phone').value,
      })
      if(res.status === 'ok') {
        console.log(res.result)
        code_time = res.result.remain_time
        let code_button = document.getElementById('get-code-button')
        code_button.innerHTML = `重新发送(${code_time})`
        code_button.disabled = true

        code_interval = setInterval(() => {
          code_time--
          if(code_time <= 0) {
            clearInterval(code_interval)
            code_interval = null
            code_button.innerHTML = '获取验证码'
            code_button.disabled = false
          } else {
            code_button.innerHTML = `重新发送(${code_time})`
          }
        }, 1000)
      } else {
        toastError(res.msg)
        console.error(res.msg)
      }
    }
    function init() {
      initButtons()
      document.getElementById('login-button').addEventListener('click', userLogin)
      // document.getElementById('change-type-button').addEventListener('click', changeLoginType)
      document.getElementById('get-code-button').addEventListener('click', getCode)
      document.getElementById('qr-login-button').addEventListener('click', initQrCode)
      document.getElementById('wechat-login-button').addEventListener('click', initWechatQrCode)
      document.getElementById('close-button').addEventListener('click', close)
      document.getElementById('privacy-terms').addEventListener('click', () => openURL('https://api.xiaoheihe.cn/account/acc_privacy_introduce/?type=all'))
      document.querySelector('.download-link').addEventListener('click', () => openURL('https://www.xiaoheihe.cn/home'))
      let phoneInput = document.getElementById('phone')
      // let passwordInput = document.getElementById('password')
      let codeInput = document.getElementById('code')
      phoneInput.addEventListener('input', () => {
        if(phoneInput.value.length > 0 && codeInput.value.length > 0) {
          document.getElementById('login-button').classList.remove('disabled')
        } else {
          document.getElementById('login-button').classList.add('disabled')
        }
      })
      // passwordInput.addEventListener('input', () => {
      //   if(passwordInput.value.length > 0 && phoneInput.value.length > 0) {
      //     document.getElementById('login-button').classList.remove('disabled')
      //   } else {
      //     document.getElementById('login-button').classList.add('disabled')
      //   }
      // })
      codeInput.addEventListener('input', () => {
        if(codeInput.value.length > 0 && phoneInput.value.length > 0) {
          document.getElementById('login-button').classList.remove('disabled')
        } else {
          document.getElementById('login-button').classList.add('disabled')
        }
      })
      document.addEventListener('keydown', (e) => {
        if(e.key === 'Enter') {
          userLogin()
        }
      })
      window.electronAPI.getWechatQrCode().then(res => {
        console.log('getWechatQrCode', res)
        if(res.status === 'ok') {
          console.log('res.result.url', new URL(res.result.url).searchParams.get('redirect_uri'))
          let redirect_uri = encodeURIComponent(new URL(res.result.url).searchParams.get('redirect_uri'))
          var obj = new WxLogin({
            id: "qr-code-wechat",
            appid: 'wxced0cbce486f737e',
            scope: "snsapi_login",
            redirect_uri: redirect_uri,
            state: 'xiaoheihe',
            href: 'https://static.max-c.com/static/heybox-chat/wechat_qr.css'
          });
          console.log('obj', obj)
          // document.getElementById('qr-code-wechat').src = res.result.url
          window.electronAPI.loginByWechat((event, res) => {
            console.log('loginByWechat', res)
            if(res.status !== 'ok') {
              console.error('loginByWechat', res.msg)
            }
          })
        } else {
          console.error('getWechatQrCode', res.msg)
        }
      })
    }
    function initQRCodeState() {
      if(qr_interval) {
        clearInterval(qr_interval)
        qr_interval = null
      }
      qrCodeStateChange(0)
      window.electronAPI.getQrCode().then(res => {
        console.log('getQrCode', res)
        if(res.status === 'ok') {
          // document.getElementById('qr-code').src = res.result.qr_url
          let qr_url = res.result.qr_url
          let qr = qr_url.split('=')[1]
          console.log('qr', qr)
          QRCode.toCanvas(document.getElementById('qr-code'), qr_url, (err) => {
            if(err) {
              console.error('QRCode', err)
            }
            qrCodeStateChange(1)
            qr_interval = setInterval(() => {
              window.electronAPI.getQrState({ qr }).then(res2 => {
                console.log('getQrState', res2)
                if(res2.status === 'ok') {
                  const state = res2.result.error
                  const msg = res2.result.error_msg
                  if (state === 'cancel') { // 取消登录
                    qrCodeStateChange(2)
                  } else if (state === 'ready') { // 扫码后等待确认
                    qrCodeStateChange(3)
                  } else if (state === 'ok') {
                    clearInterval(qr_interval)
                    window.electronAPI.loginByQr({
                      pkey: res2.result.pkey,
                      heybox_id: Number(res2.result.heyboxid),
                    }).then(res3 => {
                      console.log('loginByQr', res3)
                      if(res3.status !== 'ok') {
                        console.error('loginByQr', res3.msg)
                      }
                    })
                  } else if (state === 'failed') { // 失败
                    toastError.error(msg)
                  } else if(state !== 'wait') {
                    toastError.error(msg)
                  }
                }
              })
            }, 1000)
          })
        } else {
          console.error('getQrCode', res.msg)
        }
      })
    }
    function qrCodeStateChange(state) {
      document.querySelectorAll('.state').forEach(el => el.style.display = 'none')
      if(state === 1) {
        document.querySelector('.mask').style.display = 'none'
      } else {
        document.querySelector('.mask').style.display = 'block'
        document.querySelector(`.state-${state}`).style.display = 'flex'
      }
      if(state === 2) {
        document.querySelector('.qr-code-state').style.cursor = 'pointer'
        document.querySelector('.qr-code-state').addEventListener('click', initQRCodeState)
      } else {
        document.querySelector('.qr-code-state').style.cursor = 'default'
        document.querySelector('.qr-code-state').removeEventListener('click', initQRCodeState)
      }
    }
    function initQrCode() {
      document.querySelector('.qr-code-wrapper').style.display = 'block'
      document.querySelector('.content-wrapper').style.display = 'none'
      document.querySelector('.footer-wrapper').style.display = 'none'
      let title = document.querySelector('.title')
      title.innerHTML = `
        <i class="iconfont icon-arrow-left-bold"></i>
        登录小黑盒加速器
      `
      title.classList.add('pointer')
      title.addEventListener('click', back)
      initQRCodeState()
    }
    function initWechatQrCode() {
      document.querySelector('.wechat-login-wrapper').style.display = 'block'
      document.querySelector('.content-wrapper').style.display = 'none'
      document.querySelector('.footer-wrapper').style.display = 'none'
      let title = document.querySelector('.title')
      title.innerHTML = `
        <i class="iconfont icon-arrow-left-bold"></i>
        扫描微信二维码登录
      `
      title.classList.add('pointer')
      title.addEventListener('click', back)
    }
    function openURL(url) {
      window.electronAPI.openInBrowser(url)
    }
    function close() {
      window.electronAPI.close('login')
    }
    function back() {
      document.querySelector('.qr-code-wrapper').style.display = 'none'
      document.querySelector('.wechat-login-wrapper').style.display = 'none'
      document.querySelector('.content-wrapper').style.display = 'block'
      document.querySelector('.footer-wrapper').style.display = 'flex'
      let title = document.querySelector('.title')
      title.innerHTML = `
        登录小黑盒加速器
      `
      title.classList.remove('pointer')
      title.removeEventListener('click', back)
      if(qr_interval) {
        clearInterval(qr_interval)
        qr_interval = null
      }
    }
    function initButtons() {
      let getCodeButton = new Button({
        id: 'get-code-button',
        text: '获取验证码',
        el: document.getElementById('get-code-button'),
        onClick: getCode,
      })
    }
    // function changeLoginType() {
    //   console.log('changeLoginType')
    //   login_type = login_type === 'password' ? 'code' : 'password'
    //   if(login_type === 'password') {
    //     console.log(1)
    //     document.getElementById('code').style.display = 'none'
    //     document.getElementById('password').style.display = 'block'
    //     document.getElementById('login-button').innerHTML = '登录'
    //     document.getElementById('get-code-button').style.display = 'none'
    //     document.getElementById('change-type-button').innerHTML = `
    //       <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    //         <g id="0icon/social/wechat">
    //           <path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M10.5 11V13C10.5 13.2899 10.3162 13.5 10.125 13.5H4.875C4.68385 13.5 4.5 13.2899 4.5 13V3.5C4.5 3.21011 4.68385 3 4.875 3H10.125C10.3162 3 10.5 3.21011 10.5 3.5V5H8C7.44772 5 7 5.44772 7 6V10C7 10.5523 7.44772 11 8 11H10.5ZM14 5H11.5V3.5C11.5 2.68532 10.9003 2 10.125 2H4.875C4.09966 2 3.5 2.68532 3.5 3.5V13C3.5 13.8147 4.09966 14.5 4.875 14.5H10.125C10.9003 14.5 11.5 13.8147 11.5 13V11H14C14.5523 11 15 10.5523 15 10V6C15 5.44772 14.5523 5 14 5ZM8 6.65085V10H10.5H11.5H14V6.65085L11.3201 8.88411C11.1347 9.03863 10.8653 9.03863 10.6799 8.88411L8 6.65085ZM13.219 6H8.78102L11 7.84915L13.219 6Z" fill="#ADADAD"/>
    //         </g>
    //       </svg>
    //       验证码登录/注册
    //     `
    //   } else if(login_type === 'code') {
    //     console.log(2)
    //     document.getElementById('code').style.display = 'block'
    //     document.getElementById('password').style.display = 'none'
    //     document.getElementById('get-code-button').style.display = 'block'
    //     document.getElementById('change-type-button').innerHTML = `
    //       <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    //         <g id="0icon/social/wechat">
    //           <path id="Union" fill-rule="evenodd" clip-rule="evenodd" d="M11.5 5C11.5 3.067 9.933 1.5 8 1.5C6.067 1.5 4.5 3.067 4.5 5V5.5H4C3.17157 5.5 2.5 6.17157 2.5 7V12C2.5 12.8284 3.17157 13.5 4 13.5H12C12.8284 13.5 13.5 12.8284 13.5 12V7C13.5 6.17157 12.8284 5.5 12 5.5H11.5V5ZM10.5 5.5H5.5V5C5.5 3.61929 6.61929 2.5 8 2.5C9.38071 2.5 10.5 3.61929 10.5 5V5.5ZM3.5 7C3.5 6.72386 3.72386 6.5 4 6.5H5H11H12C12.2761 6.5 12.5 6.72386 12.5 7V12C12.5 12.2761 12.2761 12.5 12 12.5H4C3.72386 12.5 3.5 12.2761 3.5 12V7ZM9 8.5C9 8.87014 8.7989 9.19331 8.5 9.36622V11C8.5 11.2761 8.27614 11.5 8 11.5C7.72386 11.5 7.5 11.2761 7.5 11V9.36622C7.2011 9.19331 7 8.87014 7 8.5C7 7.94772 7.44772 7.5 8 7.5C8.55228 7.5 9 7.94772 9 8.5Z" fill="#ADADAD"/>
    //         </g>
    //       </svg>
    //       密码登录
    //     `
    //   }
    // }
    init()
  </script>
  <style lang="less">
    .login-cpt {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 24px 24px;
      background-color: var(--nb1r);
      border-radius: 16px;
      overflow: hidden;
      min-width: 480px;
      .input-wrapper {
        margin: 10px 0;
      }
      .content-wrapper {
        position: relative;
        z-index: 1;
        .input-wrapper {
          margin: 10px 0;
        }
        .input-wrapper:first-child {
          margin-bottom: 20px;
        }
      }
      .tip-line {
        margin: 14px 0 8px;
        color: var(--nf4r);
        font-size: 12px;
        line-height: 20px;
        padding-bottom: 20px;
        border-bottom: 1px solid var(--ns3r);
        display: flex;
        justify-content: space-between;
        align-items: center;
        .tip {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
      .footer-wrapper {
        padding: 20px 12px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        gap: 20px;
      }
      .qr-code-wrapper {
        display: none;
        .qr-code-content {
          display: flex;
          align-items: flex-end;
          justify-content: center;
          margin-bottom: 20px;
          gap: 20px;
          img {
            width: 162px;
            height: 162px;
          }
          
          .qr-code-state {
            width: 144px;
            height: 144px;
            position: relative;
            .mask {
              position: absolute;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              border-radius: 4px;
              background-color: rgba(0, 0, 0, 0.7);
              display: none;
            }
            canvas {
              position: absolute;
              width: 144px !important;
              height: 144px !important;
              border-radius: 4px;
            }
          }
          .state {
            width: 144px !important;
            height: 144px !important;
            border-radius: 4px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            z-index: 1;

            .t1 {
              color: white;
              font-size: 12px;
              line-height: 20px;
              text-align: center;
            }

            .t2 {
              color: white;
              font-size: 14px;
              line-height: 20px;
            }

          }
          .state-1 {
            display: none;
          }
          .state-2 {
            display: none;
          }
          .state-3 {
            display: none;
            svg {                                                                                                                                                                                                                                                                                                                                                                                                                                      
              width: 16px;
              height: 16px;
              margin-bottom: 10px;
            }
          }
        }
        .tips {
          text-align: center;
          p {
            color: var(--nf4r);
            text-align: center;
            font-size: 12px;
            line-height: 12px;
            margin-bottom: 0;
            a {
              color: var(--bf2r);
            }
          }
        }
      }
      .wechat-login-wrapper {
        display: none;
        iframe {
          width: 156px !important;
          height: 156px !important;
          margin-left: 50%;
          transform: translateX(-50%) scale(0.92);
          transform-origin: center;
        }
        #qr-code-wechat {
          height: 144px;
          margin-top: 34px;
          margin-bottom: 26px;
        }
        .tips p {
          color: var(--nf4r);
          font-size: 12px;
          text-align: center;
        }
      }
      #login-button {
        width: 96px;
        height: 36px;
        text-align: center;
        line-height: 36px;
      }
      #wechat-login-button {
        color: var(--success);
      }
    }
  </style>
  
</html>
