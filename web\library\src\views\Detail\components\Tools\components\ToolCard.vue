<template>
  <div class="cpt-mini-program-card" @click="handleClickTool">
    <div class="miniprogram-img">
      <img :src="tool.img" />
    </div>
    <div class="content">
      <div class="minipro-name">
        <span class="minipro-name-text">{{ tool.name }}</span>
        <svg
          class="new-tip"
          v-if="tool.is_new"
          width="30"
          height="14"
          viewBox="0 0 30 14"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <g clip-path="url(#clip0_583_13227)">
            <rect
              y="0.5"
              width="30"
              height="13"
              rx="6.5"
              fill="#D83C3F"
            />
            <path
              d="M10.5811 2.89111V10.0005H9.35547L6.16699 4.90771V10.0005H4.94141V2.89111H6.16699L9.36523 7.99365V2.89111H10.5811ZM16.6943 9.02881V10.0005H12.9199V9.02881H16.6943ZM13.2666 2.89111V10.0005H12.041V2.89111H13.2666ZM16.2012 5.85986V6.81689H12.9199V5.85986H16.2012ZM16.6699 2.89111V3.86768H12.9199V2.89111H16.6699ZM19.4775 8.40381L20.8936 2.89111H21.6357L21.5527 4.31201L20.0391 10.0005H19.2676L19.4775 8.40381ZM18.418 2.89111L19.5654 8.35986L19.668 10.0005H18.8477L17.2021 2.89111H18.418ZM23.2422 8.34521L24.375 2.89111H25.5957L23.9502 10.0005H23.1299L23.2422 8.34521ZM21.9238 2.89111L23.3252 8.41846L23.5303 10.0005H22.7588L21.2695 4.31201L21.1914 2.89111H21.9238Z"
              fill="white"
            />
          </g>
          <defs>
            <clipPath id="clip0_583_13227">
              <rect
                y="0.5"
                width="30"
                height="13"
                rx="6.5"
                fill="white"
              />
            </clipPath>
          </defs>
        </svg>
        <div
          v-else-if="tool.beta"
          class="beta-tip"
        >
          <span class="block">Beta</span>
        </div>
      </div>
      <div class="minipro-desc">
        {{ tool.desc }}
      </div>
      <div class="minipro-avatar">
        <div>
          <img
            :src="tool.icon"
            style="width: 46px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="ToolCard">
import { ipcService } from '@heybox-app-web-shared/ipc';
const props = defineProps({
  tool: {
    type: Object,
    default: () => ({}),
  },
});

const handleClickTool = () => {
  if (props.tool && props.tool.protocol) {
    ipcService.sendToMain('protocol', props.tool.protocol)
  }
};
</script>

<style lang="scss">
.cpt-mini-program-card {
  height: 86px;
  width: 100%;
  min-width: 260px;

  flex-shrink: 0;
  position: relative;
  border-radius: 8px;
  transition: background 0.2s ease-out;
  cursor: pointer;
  display: flex;
  background: var(---general-color-bg-2, #f7f8f9);
  .miniprogram-img {
    width: 122px;
    flex-shrink: 0;
    border-radius: 8px 0 0 8px;
    overflow: hidden;
    img {
      width: 160px;
      max-width: unset;
      border-radius: inherit;
    }
  }
  .content {
    padding: 13px 0 0 10px;
    width: 0;
    flex: 1;
    .minipro-name {
      white-space: nowrap;
      display: flex;
      align-items: center;
      padding-right: 8px;
      margin-bottom: 5px;
      .minipro-name-text {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 13px;
        line-height: 18px;
        letter-spacing: 0.13px;
        font-weight: 700;
      }
      .new-tip {
        width: 30px;
        height: 14px;
        margin-left: 4px;
        flex-shrink: 0;
        background: var(--f-error-fill, #d83c3f);
        border-radius: 8px;
      }
      .beta-tip {
        display: inline-block;
        width: 29px;
        height: 13px;
        margin: 0;
        margin-left: 4px;
        flex-shrink: 0;
        background: $general-color-bg-6;
        color: $general-color-text-2;
        border-radius: 8px;
        .block {
          display: block;
          font-size: 10px;
          color: $general-color-text-2;
          font-weight: 600;
          line-height: 14px;
          text-align: center;
        }
      }
    }
    .minipro-avatar {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 67px;
      height: 46px;
      border-radius: 33.333px;
      overflow: hidden;
      display: flex;
      justify-content: flex-end;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .minipro-desc {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;

      font-size: 12px;
      line-height: 18px;
      letter-spacing: 0.12px;
      color: rgba(0, 0, 0, 0.4);
      overflow: hidden;
      padding-right: 39px;
    }
  }
  .minipro-collect {
    .icon-collect {
      &::before {
        width: 12px;
        height: 12px;
        font-size: 12px;
        color: #fff;
      }
    }
    &.collected {
      .icon-collect {
        background-color: #000;
        &::before {
          color: #fff;
        }
      }
    }
  }
  &:hover {
    .minipro-collect {
      opacity: 1;
    }
  }
}
</style>
