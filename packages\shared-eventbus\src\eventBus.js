import mitt from 'mitt'

// 创建事件总线实例
const eventBus = mitt()

/**
 * 事件总线工具类
 * 基于mitt实现的事件发布订阅模式
 */
class EventBus {
  constructor() {
    this.emitter = eventBus
  }

  /**
   * 发射事件
   * @param {string} type 事件类型
   * @param {any} data 事件数据
   */
  emit(type, data) {
    this.emitter.emit(type, data)
  }

  /**
   * 监听事件
   * @param {string} type 事件类型
   * @param {Function} handler 事件处理函数
   */
  on(type, handler) {
    this.emitter.on(type, handler)
  }

  /**
   * 移除事件监听
   * @param {string} type 事件类型
   * @param {Function} handler 事件处理函数（可选）
   */
  off(type, handler) {
    this.emitter.off(type, handler)
  }

  /**
   * 监听一次性事件
   * @param {string} type 事件类型
   * @param {Function} handler 事件处理函数
   */
  once(type, handler) {
    const onceHandler = (data) => {
      handler(data)
      this.off(type, onceHandler)
    }
    this.on(type, onceHandler)
  }

  /**
   * 清除所有事件监听
   */
  clear() {
    this.emitter.all.clear()
  }

  /**
   * 获取所有已注册的事件类型
   */
  getAllEvents() {
    return Array.from(this.emitter.all.keys())
  }

  /**
   * 获取指定事件类型的监听器数量
   * @param {string} type 事件类型
   */
  getListenerCount(type) {
    const handlers = this.emitter.all.get(type)
    return handlers ? handlers.length : 0
  }
}

// 创建全局事件总线实例
const globalEventBus = new EventBus()

// 导出实例和类
export { globalEventBus as default, EventBus } 