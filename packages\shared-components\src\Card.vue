<template>
  <div class="card" :class="{ 'card-shadow': shadow }">
    <div v-if="$slots.header" class="card-header">
      <slot name="header"></slot>
    </div>
    <div class="card-body">
      <slot></slot>
    </div>
    <div v-if="$slots.footer" class="card-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Card',
  props: {
    shadow: {
      type: Boolean,
      default: true
    }
  }
}
</script>

<style scoped>
.card {
  border-radius: 8px;
  background-color: #fff;
  border: 1px solid #ebeef5;
  overflow: hidden;
  margin-bottom: 16px;
  transition: 0.3s;
}

.card-shadow {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-header {
  padding: 12px 16px;
  border-bottom: 1px solid #ebeef5;
  font-weight: bold;
}

.card-body {
  padding: 16px;
}

.card-footer {
  padding: 12px 16px;
  border-top: 1px solid #ebeef5;
}
</style> 