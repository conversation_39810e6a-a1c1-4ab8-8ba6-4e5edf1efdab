<template>
  <div class="skeleton-games-wrapper">
    <div
      class="skeleton-game-card"
      v-for="i in 20"
      :key="i"
    >
      <div class="card-content">
        <div class="card-img"></div>
      </div>
    </div>
  </div>
</template>

<script setup name="SkeletonGames">
// 游戏列表骨架屏组件
</script>

<style lang="scss" scoped>
.skeleton-games-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 0 24px;

  .skeleton-game-card {
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    background-color: #e8e8e8;

    .card-content {
      width: 100%;
      position: relative;
      aspect-ratio: 15/22;
      border-radius: inherit;

      .card-img {
        width: 100%;
        height: 100%;
        background: var(---general-color-bg-0, #F1F2F3);
        border-radius: inherit;
      }
    }

    // 超大屏幕 (>1600px) - 7列
    @media (min-width: 1600px) {
      width: calc((100% - 6 * 16px) / 7);
    }

    // 大屏幕 (1400px-1599px) - 6列
    @media (min-width: 1400px) and (max-width: 1599px) {
      width: calc((100% - 5 * 16px) / 6);
    }

    // 中大屏幕 (1200px-1399px) - 5列
    @media (min-width: 1200px) and (max-width: 1399px) {
      width: calc((100% - 4 * 16px) / 5);
    }

    // 中屏幕 (1000px-1199px) - 4列
    @media (min-width: 1000px) and (max-width: 1199px) {
      width: calc((100% - 3 * 16px) / 4);
    }

    // 小屏幕 (800px-999px) - 3列
    @media (min-width: 800px) and (max-width: 999px) {
      width: calc((100% - 2 * 16px) / 3);
    }

    // 超小屏幕 (<800px) - 2列
    @media (max-width: 799px) {
      width: calc((100% - 1 * 16px) / 2);
    }
  }
}
</style>
