const concurrently = require('concurrently');
const { execSync } = require('child_process');

// 获取命令行参数中的环境变量
const envArg = process.argv.find(arg => arg.startsWith('--env='));
const pathArgs = process.argv
  .filter(arg => arg.startsWith('--path='))
  .map(arg => arg.split('=')[1]);
const env = envArg ? envArg.split('=')[1] : 'local';

console.log('envArg', envArg, 'pathArgs', pathArgs);

// 构建路径参数字符串
const pathParams = pathArgs.length > 0 
  ? pathArgs.map(path => ` --path=${path}`).join('')
  : '';

// 使用 concurrently 启动进程
(async () => {
  try {
    await concurrently([
      { command: `yarn dev:web${pathParams}`, name: 'web' },
      { command: `yarn dev:electron --env=${env}${pathParams}`, name: 'electron' }
    ], {
      prefix: 'name',
      killOthers: ['failure', 'success'],
      restartTries: 3,
    });

    execSync('node scripts/check-timers.js', { stdio: 'inherit' });
  } catch (error) {
    console.error('启动失败:', error);
  }
})(); 