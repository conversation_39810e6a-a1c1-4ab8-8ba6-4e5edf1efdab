const path = require('path')
const node_inject = require('@heybox/node-inject').overlay
const { BrowserWindow, ipcMain, screen } = require('electron')
const log = require('@heybox/electron-utils/log')

const {
  checkIsInjectWhiteList,
  getNoticeData,
  checkCanShowKmNotice,
  getInstalledMiniProgram,
  getShortcutCode,
  setNeverShowGuildMiniproId,
  setCdGuildMiniproId,
} = require('./utils')
const {
  registerInterval,
  SYSTEM_WINDOW_TYPE,
} = require('@heybox/electron-utils')
const {
  matchOfficialGame,
  getHigherPriorityProcessMatch,
} = require('@heybox/process-iterate/match')

class IngameNotice {
  constructor() {
    this.showed_window_id = [] // 展示过提示的pid 同一个pid的窗口进程只展示一次
    this.notice_win = null
    this.guild_win = null
    ipcMain.on('minipro_km_notice:hide', this.hideKmNotice.bind(this))
    ipcMain.on('minipro_guide:click', this.handleGuildNotice.bind(this))
    ipcMain.on('minipro_guide:hide', this.hideGuildNotice.bind(this))
    ipcMain.on('minipro_guide:neverShow', this.guildNoticeNeverShow.bind(this))
  }

  // 开始进行窗口轮训，检测是否需要展示提示
  startCheckTopWindowNeedNotice() {
    registerInterval(SYSTEM_WINDOW_TYPE.TOP, this.windowListCheck.bind(this))
  }

  async windowListCheck(windows) {
      // 如果未登录 不展示提示
      if (!global.user_info || !global.game_process_map) return

      let final_match_result = null, win = null;

      for (let p of windows) {
        const game_data = matchOfficialGame({
          process_name: p.processName, 
          window_name: p.title,
        }, global.game_process_map)
        if(game_data) {
          game_data._win = p
          final_match_result = getHigherPriorityProcessMatch(final_match_result, game_data)
        }
      }
      if (final_match_result) {
        win = final_match_result._win
        win._mini_data = {
          processName: final_match_result.match_process_name, 
        }
      }
      
      
      try {
        if (win) {
          if (this.showed_window_id.includes(win.windowId)) return
          this.showed_window_id.push(win.windowId)

          log.info('[ingame_notice] check_func win', win)
          // 获取是否允许进行注入
          let allow_inject = checkIsInjectWhiteList(win)
          let res = await getNoticeData(win._mini_data.processName)
          log.info('[ingame_notice] getNoticeData', JSON.stringify(res))
          if (res.status === 'ok') {
            let { notices, guide } = res.result
            // 快捷键提示必须支持注入
            if (notices && allow_inject) {
              this.showKmNotice(win, notices)
            }
            // guild提示不注入也可以展示
            if (guide) {
              this.showGuildNotice(win, guide, allow_inject)
            }
          }
        }
      } catch (e) {
        console.error(e)
      }
  }

  // 展示快捷键提示弹窗
  showKmNotice(gameWin, notices) {
    if (this.notice_win) return

    let { windowId } = gameWin
    if (!windowId) return

    const installed_mini_programs = getInstalledMiniProgram()
    for (let data of notices) {
      if (checkCanShowKmNotice(data, installed_mini_programs)) {
        let shortcut_keys = getShortcutCode(data.shortcut_key)
        if (shortcut_keys) {
          let params = {
            shortcut_keys: JSON.stringify(shortcut_keys),
            desc: data.desc,
            avatar: data.icon,
            icon: 'chat-icon'
          }
          log.info('[ingame_notice] showKmNotice', params)
          this.createNoticeWindow(gameWin, params)
          return
        }
      }
    }
  }
  // 隐藏快捷键提示弹窗
  hideKmNotice() {
    if (!this.notice_win) return
    this.notice_win.destroy()
    this.notice_win = null
  }
  // 展示guild提示弹窗
  showGuildNotice(gameWin, guide, allow_inject) {
    if (this.guild_win) return

    let { windowId } = gameWin
    if (!windowId) return
    log.info('[ingame_notice] showGuildNotice', guide, allow_inject)
    this.createGuildeWindow(gameWin, guide, allow_inject)
    setCdGuildMiniproId(guide.mini_pro_id)
  }
  // 隐藏guild提示弹窗
  hideGuildNotice() {
    if (!this.guild_win) return
    this.guild_win.destroy()
    this.guild_win = null
  }
  
  // 创建快捷键提示窗口
  async createNoticeWindow(gameWin, query) {
    let window_config = {
      height: 1000,
      width: 300,
      webPreferences: {
        preload: path.join(__dirname, './km_notice/preload.js'),
      },

      fullscreen: false,
      minimizable: false,
      maximizable: false,
      resizable: false,
      show: false,
      frame: false,
      skipTaskbar: true,
      transparent: true,
      followInjectWindowSize: false,
    }
    this.notice_win = node_inject.createOffscreenWindow(window_config, gameWin)
    this.notice_win.loadFile(path.join(__dirname, './km_notice/index.html'), {
      query,
    })
    this.notice_win.on('close', () => {
      this.notice_win = null
    })
    try {
      await new Promise((sub_resolve) => {
        this.notice_win.once('ready-to-show', () => {
          sub_resolve()
        })
      })

      let { width } = node_inject.getGameWindowSize(gameWin)
      await node_inject.injectWindow({
        window: this.notice_win,
        gameWin,
        listenEvents: {
          'game.connection.close': (payload) => {
            this.hideKmNotice()
          },
        },
        rect: {
          x: width - 300,
          y: 240,
          height: 1000,
          width: 300,
        },
        autoMoveInGamwWindow: false,
      })
    } catch (e) {
      log.info(e)
    }
    if (this.notice_win) {
      this.notice_win.webContents.send('minipro_km_notice:onShow')
      main_config.ingameDevtool && this.notice_win.webContents.openDevTools()
    }
  }
  // 创建guild窗口
  async createGuildeWindow(gameWin, query, allow_inject) {
    let window_config = {
      height: 366,
      width: 322,
      webPreferences: {
        preload: path.join(__dirname, './guild_notice/preload.js'),
      },

      fullscreen: false,
      minimizable: false,
      maximizable: false,
      resizable: false,
      show: false,
      frame: false,
      skipTaskbar: true,
      transparent: true,
      followInjectWindowSize: false,
    }

    if (allow_inject) {
      let { width, height } = node_inject.getGameWindowSize(gameWin)

      this.guild_win = node_inject.createOffscreenWindow(window_config, gameWin)
      this.guild_win.loadFile(path.join(__dirname, './guild_notice/index.html'), {
        query,
      })
      this.guild_win.on('close', () => {
        this.guild_win = null
      })
      try {
        await new Promise((sub_resolve) => {
          this.guild_win.once('ready-to-show', () => {
            sub_resolve()
          })
        })

        await node_inject.injectWindow({
          window: this.guild_win,
          gameWin,
          listenEvents: {
            'game.connection.close': (payload) => {
              this.hideGuildNotice()
            },
          },
          rect: {
            x: width - window_config.width - 20,
            y: height - window_config.height,
            height: window_config.height,
            width: window_config.width,
          }
        })
      } catch (e) {
        log.info(e)
      }
    } else {
      let { width, height } = screen.getPrimaryDisplay().workArea
      window_config.x = width - window_config.width - 20
      window_config.y = height - window_config.height
      this.guild_win = new BrowserWindow(window_config)
      this.guild_win.on('close', () => {
        this.guild_win = null
      })
      this.guild_win.once('ready-to-show', () => {
        if (this.guild_win && !this.guild_win.isDestroyed()) {
          this.guild_win.setAlwaysOnTop(true, 'pop-up-menu')
          this.guild_win.show()
        }
      })
      this.guild_win.loadFile(path.join(__dirname, './guild_notice/index.html'), {
        query,
      })
    }
    if (this.guild_win) {
      this.guild_win.webContents.send('minipro_guide:onShow')
      main_config.ingameDevtool && this.guild_win.webContents.openDevTools()
    }
  }
  // 响应guild提示的点击按钮事件
  handleGuildNotice(_, protocol) {
    log.info('[ingame_notice] handleGuildNotice', protocol)
    global.mainWindow?.webContents?.send('execute-protocol', protocol)
  }
  // 设置guild窗口不再展示数据
  guildNoticeNeverShow(_, data) {
    log.info('[ingame_notice] guildNoticeNeverShow', data)
    setNeverShowGuildMiniproId(data.mini_program_id)
  }
}

const instance = new IngameNotice()
module.exports = {
  startCheckTopWindowNeedNotice: instance.startCheckTopWindowNeedNotice.bind(instance),
  createGuildeWindow: instance.createGuildeWindow.bind(instance),
  createNoticeWindow: instance.createNoticeWindow.bind(instance),
  instance: instance
}
