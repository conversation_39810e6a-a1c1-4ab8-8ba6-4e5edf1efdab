#!/usr/bin/env node

/**
 * Vue文件定时器清理检查脚本
 * 在开发启动时自动检测Vue文件中的定时器问题
 */

const fs = require('fs');
const glob = require('glob');

function parseVueFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');

  // 提取script部分
  const scriptMatch = content.match(/<script[^>]*>([\s\S]*?)<\/script>/);
  if (!scriptMatch) return null;

  const scriptContent = scriptMatch[1];

  const hasTimers = /setTimeout\s*\(|setInterval\s*\(|requestAnimationFrame\s*\(|requestIdleCallback\s*\(/.test(scriptContent);

  if (!hasTimers) {
    return { filePath, hasIssues: false };
  }

  const hasUnmountHook = /beforeDestroy\s*\(|beforeUnmount\s*\(|onBeforeUnmount\s*\(|onUnmounted\s*\(|destroyed\s*\(|unmounted\s*\(/.test(scriptContent);

  if (!hasUnmountHook) {
    // 有定时器但没有卸载钩子
    return { filePath, hasIssues: true };
  }

  let hookContent = '';

  const optionsApiPattern = /(beforeDestroy|beforeUnmount|destroyed|unmounted)\s*\([^)]*\)\s*\{/;
  const optionsMatch = scriptContent.match(optionsApiPattern);

  const compositionApiPattern = /(onBeforeUnmount|onUnmounted)\s*\(\s*\(\s*\)\s*=>\s*\{/;
  const compositionMatch = scriptContent.match(compositionApiPattern);

  if (optionsMatch || compositionMatch) {
    const match = optionsMatch || compositionMatch;
    const startIndex = scriptContent.indexOf(match[0]) + match[0].length;

    let braceCount = 1;
    let endIndex = startIndex;

    for (let i = startIndex; i < scriptContent.length && braceCount > 0; i++) {
      if (scriptContent[i] === '{') braceCount++;
      if (scriptContent[i] === '}') braceCount--;
      endIndex = i;
    }

    hookContent = scriptContent.substring(startIndex, endIndex);
  }

  const hasClearCode = hookContent && /clearTimeout\s*\(|clearInterval\s*\(|cancelAnimationFrame\s*\(|cancelIdleCallback\s*\(/.test(hookContent);

  return {
    filePath,
    hasIssues: !hasClearCode 
  };
}

function checkTimers() {
  // 查找所有Vue文件
  const vueFiles = glob.sync('**/*.vue', {
    ignore: ['node_modules/**', 'dist/**', 'build/**']
  });

  if (vueFiles.length === 0) {
    return;
  }
  
  const issues = [];

  vueFiles.forEach(file => {
    const result = parseVueFile(file);
    if (result && result.hasIssues) {
      issues.push(result);
    }
  });

  if (issues.length === 0) {
    console.log('✅ Vue定时器检查通过');
  } else {
    console.log(`⚠️ 发现 ${issues.length} 个Vue文件存在定时器内存泄漏风险`);
    issues.forEach(issue => {
      console.log(`   ${issue.filePath}`);
    });
  }
}

checkTimers();
