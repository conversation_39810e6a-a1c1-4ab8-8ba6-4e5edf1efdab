{"appid": 1, "name": "小黑盒的大爆游戏", "img": "https://via.placeholder.com/150", "desc": "小黑盒的大爆游戏是一款非常有趣的游戏，玩家可以在游戏中体验到非常多的乐趣。", "banner": "https://imgheybox.max-c.com/dev/bbs/2025/06/04/********************************.png", "blur": "https://imgheybox.max-c.com/dev/bbs/2025/06/04/********************************.png", "tools": [{"mini_pro_id": "wukong_path", "name": "黑神话路线", "desc": "111", "avatar": "https://imgheybox.max-c.com/oa/2024/04/10/********************************.png", "img": "https://imgheybox.max-c.com/web/bbs/2024/06/15/********************************.png", "need_client": true, "protocol": "heyboxchat://client-mini-program/?type=game_overlay_manager&params=%7B%22querys%22%3A%7B%22appid%22%3A%222358720%22%2C%22mini_pro_id%22%3A%22wukong_path%22%7D%7D", "beta": false, "is_new": true, "appid": 2358720, "channel_icon": "https://imgheybox.max-c.com/oa/2024/08/22/********************************.png"}, {"mini_pro_id": "dota2_overlay", "name": "dota2游戏内覆盖", "desc": "", "avatar": "https://imgheybox.max-c.com/oa/2024/04/10/********************************.png", "img": "https://imgheybox.max-c.com/web/bbs/2024/08/23/********************************.png", "need_client": true, "protocol": "heyboxchat://client-mini-program/?type=game_overlay_manager&params=%7B%22querys%22%3A%7B%22appid%22%3A%22570%22%2C%22mini_pro_id%22%3A%22dota2_overlay%22%7D%7D", "beta": false, "appid": 570, "channel_icon": "https://imgheybox.max-c.com/oa/2024/04/10/********************************.png"}, {"mini_pro_id": "pal_map_overlay", "name": "帕鲁地图覆盖", "desc": "帕鲁地图覆盖", "avatar": "https://imgheybox.max-c.com/oa/2024/04/10/********************************.png", "img": "https://imgheybox.max-c.com/web/bbs/2024/01/22/********************************.png", "need_client": true, "protocol": "heyboxchat://client-mini-program/?type=game_overlay_manager&params=%7B%22querys%22%3A%7B%22appid%22%3A%221623730%22%2C%22mini_pro_id%22%3A%22pal_map_overlay%22%7D%7D", "beta": true, "appid": 1623730, "channel_icon": "https://imgheybox.max-c.com/oa/2024/08/05/********************************.png"}], "friends": {"owned_stats": {"recent_play_count": 1, "sample_list": [{"avatar": "https://imgheybox.max-c.com/heybox/avatar/steamcommunity/public/images/2023/05/23/94fe9c6d7556430fa16500af4df896b3.jpg", "is_recent_play": true, "jump_to_heybox_page": "heybox://%7B%22user_id%22%3A%2222196651%22%2C%22protocol_type%22%3A%22openUser%22%7D", "jump_to_steam_page": "heybox://%7B%22need_login%22%3A0%2C%22path%22%3A%22game%5C%2Fsteam%22%2C%22params%22%3A%7B%22user_id%22%3A%2222196651%22%7D%2C%22protocol_type%22%3A%22openRouterPath%22%7D", "steamid": "76561198283236692"}, {"avatar": "https://imgheybox.max-c.com/heybox/avatar/steamcommunity/public/images/2023/05/23/030f9fbcc04fb587a7efd2dff43edba9.jpg", "is_recent_play": false, "jump_to_heybox_page": "heybox://%7B%22user_id%22%3A%2229077380%22%2C%22protocol_type%22%3A%22openUser%22%7D", "jump_to_steam_page": "heybox://%7B%22need_login%22%3A0%2C%22path%22%3A%22game%5C%2Fsteam%22%2C%22params%22%3A%7B%22user_id%22%3A%2229077380%22%7D%2C%22protocol_type%22%3A%22openRouterPath%22%7D", "steamid": "76561198363800573"}, {"avatar": "https://imgheybox.max-c.com/heybox/avatar/steamcommunity/public/images/2025/03/24/2f566c9bb80e8faf34cabfac455dc6fd.jpg", "is_recent_play": false, "jump_to_heybox_page": "heybox://%7B%22user_id%22%3A%2267836884%22%2C%22protocol_type%22%3A%22openUser%22%7D", "jump_to_steam_page": "heybox://%7B%22need_login%22%3A0%2C%22path%22%3A%22game%5C%2Fsteam%22%2C%22params%22%3A%7B%22user_id%22%3A%2267836884%22%7D%2C%22protocol_type%22%3A%22openRouterPath%22%7D", "steamid": "76561199283407486"}], "total_count": 3}, "wishlist_stats": {"sample_list": [{"avatar": "https://imgheybox.max-c.com/heybox/avatar/steamcommunity/public/images/2025/03/24/2f566c9bb80e8faf34cabfac455dc6fd.jpg", "is_recent_play": false, "jump_to_heybox_page": "heybox://%7B%22user_id%22%3A%2267836884%22%2C%22protocol_type%22%3A%22openUser%22%7D", "jump_to_steam_page": "heybox://%7B%22need_login%22%3A0%2C%22path%22%3A%22game%5C%2Fsteam%22%2C%22params%22%3A%7B%22user_id%22%3A%2267836884%22%7D%2C%22protocol_type%22%3A%22openRouterPath%22%7D", "steamid": "76561199283407486"}], "total_count": 2}, "play_progress_bar": [{"desc": "No.3", "is_me": true, "jump_to_heybox_page": "heybox://%7B%22user_id%22%3A%2267836884%22%2C%22protocol_type%22%3A%22openUser%22%7D", "jump_to_steam_page": "heybox://%7B%22need_login%22%3A0%2C%22path%22%3A%22game%5C%2Fsteam%22%2C%22params%22%3A%7B%22user_id%22%3A%2267836884%22%7D%2C%22protocol_type%22%3A%22openRouterPath%22%7D", "play_hour": "17", "playtime_second": 37200, "steam_info": {"steamid": "76561199283407486", "nickname": "三葉", "avatar": "https://imgheybox.max-c.com/heybox/avatar/steamcommunity/public/images/2025/03/24/2f566c9bb80e8faf34cabfac455dc6fd.jpg"}}, {"desc": "黑盒均值", "is_me": false, "jump_to_heybox_page": null, "jump_to_steam_page": null, "play_hour": "12.1", "playtime_second": 43619, "steam_info": null}, {"desc": "好友第一", "is_me": false, "jump_to_heybox_page": "heybox://%7B%22user_id%22%3A%2267836884%22%2C%22protocol_type%22%3A%22openUser%22%7D", "jump_to_steam_page": "heybox://%7B%22need_login%22%3A0%2C%22path%22%3A%22game%5C%2Fsteam%22%2C%22params%22%3A%7B%22user_id%22%3A%2267836884%22%7D%2C%22protocol_type%22%3A%22openRouterPath%22%7D", "play_hour": "17", "playtime_second": 61200, "steam_info": {"steamid": "76561199283407486", "nickname": "三葉", "avatar": "https://imgheybox.max-c.com/heybox/avatar/steamcommunity/public/images/2025/03/24/2f566c9bb80e8faf34cabfac455dc6fd.jpg"}}]}, "achievement_progress": {"achieved_count": 1, "group_list": [{"ach_list": [{"ach_desc": "在未引爆炸弹的情况下完成游戏节目", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 3, "ach_name": "冷手山芋", "achieved": 0, "achieved_percent": "2.0", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "GameshowNoExplosion", "game_score": 0}, {"ach_desc": "", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 3, "ach_name": "蛇！！！", "achieved": 0, "achieved_percent": "2.8", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "ShootCardboardBox", "game_score": 0}, {"ach_desc": "在所有6张长椅上休息", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 3, "ach_name": "姐妹：两个闺蜜的故事", "achieved": 0, "achieved_percent": "3.6", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "FindAllBenches", "game_score": 0}], "group_achieved": 0, "group_level": 3, "group_rarity_desc": "(完成率0~10%)", "group_total": 9}, {"ach_list": [{"ach_desc": "谁能料到呢", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 2, "ach_name": "三石一鸟", "achieved": 0, "achieved_percent": "11.4", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "ThrowStones", "game_score": 0}, {"ach_desc": "起开……", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 2, "ach_name": "负重前行", "achieved": 0, "achieved_percent": "13.0", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "<PERSON><PERSON><PERSON><PERSON>", "game_score": 0}, {"ach_desc": "我需要你最强效的药剂", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 2, "ach_name": "药剂大师", "achieved": 0, "achieved_percent": "15.8", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "AllCauldronForms", "game_score": 0}], "group_achieved": 0, "group_level": 2, "group_rarity_desc": "(完成率10~100%)", "group_total": 11}], "recent_achieved_list": [{"ach_desc": "我需要你最强效的药剂", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 2, "ach_name": "药剂大师", "achieved": 0, "achieved_percent": "15.8", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "AllCauldronForms", "game_score": 0}, {"ach_desc": "别担心，这只是模拟", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 3, "ach_name": "我们是反派吗？", "achieved": 0, "achieved_percent": "8.4", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "Antihero", "game_score": 0}, {"ach_desc": "尽情释放", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 3, "ach_name": "猪突猛进", "achieved": 0, "achieved_percent": "9.2", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "BigGas", "game_score": 0}, {"ach_desc": "你要担心的不是大灰狼", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 2, "ach_name": "吹呀吹呀吹", "achieved": 0, "achieved_percent": "28.4", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "BrickHouse", "game_score": 0}, {"ach_desc": "全自动区分计算机和人类的图灵测试", "ach_icon": "https://heyboxbj.max-c.com/gameimg/2025/03/07/********************************.jpg", "ach_level": 3, "ach_name": "你不是机器人", "achieved": 0, "achieved_percent": "3.7", "achieved_time": 0, "achieved_time_desc": "未完成", "api_name": "CaptchaCompleted", "game_score": 0}], "total_count": 3}}