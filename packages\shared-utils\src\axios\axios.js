import axios from 'axios'
import qs from 'qs'
import m from 'js-md5';
import { G } from './encrypt/index'

import { handleLogout } from './logout'
import { useToast } from 'vue-toastification'
const toast = useToast()

const REPORT_SM_DEVICE_ID_APIS = [
  // 登录相关
  '/account/login/',
  '/account/wechat/login/v2/heybox_chat/',
  '/account/login_code/',
  '/account/get_login_code/',
  '/account/heybox_pc/get_qr/',
  '/account/web_login_with_token/',

  // 支付相关
  '/pay/heychat_pay',

  // 帖子相关
  '/bbs/app/link/web/view',
  '/bbs/app/link/tree',
  '/bbs/app/topic/feeds',
  '/bbs/app/api/link/post',
  '/bbs/app/profile/events',
  '/bbs/app/profile/history/visit',
  '/bbs/app/comment/create',
  '/bbs/app/comment/support',
]

const Axios = axios.create()
Axios.create = axios.create
Axios.all = axios.all

let timeoutToastLock = false

// Axios连续失败次数
let axiosErrorTime = 0

// Axios连续失败次数重置 
let axiosErrorTimeout = null

// Axios连续失败提示更改DNS设置
let isAxiosErrorAlert = false


let logoutPromise = null

Axios.interceptors.request.use(
  async (config) => {
    if (REPORT_SM_DEVICE_ID_APIS.indexOf(config.url.replace(/^.*\/\/[^/]+/, '')) >= 0) {
      let device_id = await getSmDeviceId()
      if (!device_id) {
        toast.error('获取数美device_id失败')
        return Promise.reject('getSmDeviceId 获取数美device_id失败');
      }
      config.data = {
        x_xhh_tokenid: device_id,
        ...config.data
      }
    }

    let p = {
      ...config.params
    }

    // 时间戳
    let t = ~~(+G.w() / 1000);

    // nonce生成
    let n = m(
      t + Math.random(new Date().getTime()).toString(),
    ).toLocaleUpperCase();

    p.hkey = G.g(new URL(config.url).pathname, t, n)

    p.nonce = n
    p._time = t
    config.params = p

    if (config.method === `POST`) {
      if (!config.upload) {
        config.data = {
          ...config.data,
        }
        if (!config.headers['Content-Type'].includes('application/json')) {
          config.data = qs.stringify(config.data, { indices: false });
        }
      }
    }
    config.withCredentials = true
    return config;
  },
  (error) => {
    return Promise.reject(error.message);
  }
)


Axios.interceptors.response.use(
  (res) => {
    axiosErrorTime = 0
    let status_key = res.data.status
    // TODO 完善逻辑
    if (status_key == 'relogin' && !res.config.params['_norelogin']) {
      if(!logoutPromise){
        logoutPromise = handleLogout()
      }
    } else if (status_key != 'ok' && res.data.msg) {
      if (res.data.msg.indexOf('数据上报失败') == -1 && res.data.msg != '请先验证' &&  res.data.msg != 'no_toast' && !res.config.params['_notip']) {
        toast.error(res.data.msg)
      }
    }
    return res
  },
  (error) => {
    if (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1) {
      // 处理超时错误
      if (!timeoutToastLock) {
        toast.error('请求超时，请刷新重试')
        timeoutToastLock = true
        setTimeout(() => {
          timeoutToastLock = false
        }, 2000)
      }
    } else {
      // 处理其他错误
      console.error(error);
    }
  
    return Promise.reject(error);
  }
)

async function getSmDeviceId() {
  return await window.getSmDeviceId()
}

export default Axios