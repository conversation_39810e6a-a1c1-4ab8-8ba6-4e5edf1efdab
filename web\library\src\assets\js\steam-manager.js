import { getSteamAccountsInfo, getSteamInstalledGameInfo, setOfflineMode, setOnlineMode, deleteSteamAccount } from './steam'
import { reportMyLibraryV2, getMyAccountList, getSteamStatusInfo } from '_api/steam'
import store from '../../store'
import { useToast } from 'vue-toastification'
import { eventBus } from '@heybox-app-web-shared/eventbus'
import { compressAndEncrypt } from '@heybox-app-web-shared/utils'

const toast = useToast()

class SteamManager {
  installedGames = []
  userSteamApps = {}
  userSteamPackages = {}
  accountList = {}
  steamStatusInfoInterval = null
  currentAccount = null
  isReportedGames = false
  constructor() {
    store.commit('setIsInitSteamData', true)
    store.commit('setIsLibraryLoading', true)
  }

  async init() {
    let device_id = await window.electronAPI.getDeviceId()
    store.commit('setDeviceId', device_id)
    this.installedGames = await getSteamInstalledGameInfo()

    // 获取本地账号上报
    // 账号获取
    let { accountList, currentAccount } = await getSteamAccountsInfo()
    this.accountList = accountList
    this.currentAccount = currentAccount
    store.commit('setCurrentAccount', this.currentAccount)
    if(accountList.length === 0) return
    await this.reportLibrary()
    await this.getMyAccounts()
    
    store.commit('setIsInitSteamData', false)
    
    store.commit('resetGameList')
    store.dispatch('getSteamGames')
    store.commit('setIsLibraryLoading', false)

    // 上报游戏库
    await this.getUserSteamApps()
    console.log('[steam-manager] userSteamApps', this.userSteamApps)
    this.reportLibrary().then(() => {
      console.log('[steam-manager] reportLibrary completed, refreshing game list')
      this.isReportedGames = true
      store.commit('resetGameList')
      store.dispatch('getSteamGames')
    }).catch(error => {
      console.error('[steam-manager] reportLibrary failed:', error)
    })
  }

  async getUserSteamApps() {
    this.userSteamApps = {}
    this.userSteamPackages = {}
    let account_name = this.accountList.find(account => account.steam_id === this.currentAccount).account_name
    let res = await window.steamAPI.getSteamApps(account_name)
    this.userSteamApps[this.currentAccount] = res.appinfos
    this.userSteamPackages[this.currentAccount] = res.pkgids
  }

  async reportLibrary(steam_id) {
    let currentAccount = steam_id || this.currentAccount
    store.commit('setCurrentAccount', currentAccount)
    let params = {
      installed_steam_apps: this.installedGames.map(game => game.appid),
      current_steam_account: currentAccount,
      local_steam_accounts: this.accountList.map(account => account.steam_id),
      user_steam_apps: this.userSteamApps,
      user_steam_packages: this.userSteamPackages,
    }
    console.log('[steam-manager] reportLibrary params', params)
    
    // 对params数据进行gzip压缩和AES加密
    try {
      const encryptedResult = compressAndEncrypt(params)
      console.log('[steam-manager] 压缩加密后的数据:', {
        encryptedData: encryptedResult.encryptedData,
        key: encryptedResult.key,
        sid: encryptedResult.sid,
        time: encryptedResult.time,
        originalSize: JSON.stringify(params).length,
      })
      
      // 将加密后的数据替换原始params
      params = {
        data: encryptedResult.encryptedData,
        key: encryptedResult.key,
        sid: encryptedResult.sid,
        time: encryptedResult.time,
      }
    } catch (error) {
      console.error('[steam-manager] 压缩加密失败:', error)
      // 如果压缩加密失败，继续使用原始数据
    }
    let reportRes = await reportMyLibraryV2({
      device_id: store.state.device_id
    }, params)
    if(reportRes.data.status !== 'ok') {
      toast.error(reportRes.data.message)
      return
    }
  }

  async getMyAccounts() {
    let res = await getMyAccountList({
      device_id: store.state.device_id
    }, {
      platform: 'steam',
    })
    
    if(res.data.status === 'ok') {
      let { accounts, current_account } = res.data.result.steam_account
      let current = accounts.find(account => account.id === store.state.current_account)
      current = Object.assign(current, current_account)
      accounts.forEach(account => {
        let steam_account_info = this.accountList.find(item => item.steam_id === account.id)
        account = Object.assign(account, steam_account_info)
      })
      await this.getSteamStatusInfo(current.online_state_url, current)
      this.steamStatusInfoInterval = setInterval(async () => {
        await this.getSteamStatusInfo(current.online_state_url, current)
      }, 1000 * 30)
      store.commit('setSteamAccounts', { accountList: accounts, currentAccount: current.id })
    }
  }

  async setCurrentAccount(account, isOnline = true) {
    clearInterval(this.steamStatusInfoInterval)
    await this.reportLibrary(account.steam_id)
    await this.getMyAccounts()
    if(isOnline) {
      await setOnlineMode(account.steam_id)
    }
    let res = await window.electronAPI.switchSteamAccount(account.account_name)
    store.commit('resetGameList')
    store.dispatch('getSteamGames')
  }

  async setOfflineMode(account) {
    try {
      await setOfflineMode(account.steam_id)
      await this.setCurrentAccount(account, false)
    } catch(e) {
      console.error('[setOfflineMode] error', e)
    }
  }

  async deleteAccount(account) {
    try {
      await deleteSteamAccount(account.steam_id)
      // 若steam账号如果没有登录，可能会出现当前account被删除的情况，因此需要重新从文件读取账号列表
      let { accountList, currentAccount } = await getSteamAccountsInfo()
      this.accountList = accountList
      this.currentAccount = currentAccount
      await this.reportLibrary(currentAccount)
      await this.getMyAccounts()
      store.commit('resetGameList')
      store.dispatch('getSteamGames')
    } catch(e) {
      console.error('[deleteAccount] error', e)
    }
  }

  async getSteamStatusInfo(url, current) {
    try {
      // 获取steam状态刷新时可能间隔较短时间触发，出现多次请求的报错
      let res = await getSteamStatusInfo(url)
      if(res.statusText === 'OK') {
        let statusInfo = res.data.response.players[0]
        if(statusInfo) {
          Object.assign(current, { online_state: statusInfo })
          eventBus.emit('updateSteamStatus')
        }
      }
    } catch(e) {
      console.error('[getSteamStatusInfo] error', e)
    }
  }

  destroy() {
    clearInterval(this.steamStatusInfoInterval)
  }
}

export default new SteamManager() 