<template>
  <div
    class="cpt-bbs-imgs-wrapper"
    :style="image_wrapper_style"
  >
    <div
      class="base-img"
      v-for="(img, index) in images"
      :key="index"
      :style="img.style"
    >
      <img :src="img.src" />
    </div>

    <div
      class="img-cnt-wrapper"
      :style="img_cnt_data.style"
    >
      <div class="img-cnt-text">
        {{ img_cnt_data.text }}
      </div>
    </div>
  </div>
</template>

<script setup name="BbsImgs">
import {
  defineProps,
  watch,
  ref,
  computed,
  toRefs,
  onBeforeUnmount,
} from 'vue';

const props = defineProps({
  link: {
    type: Object,
    default: () => {},
  },
  outer_ref: {
    type: Object,
    default: () => {},
  },
});
const { link, outer_ref } = toRefs(props);

function useImage(link, content_ref) {
  const is_show_image_wrapper = computed(
    () => link.value.positions && link.value.positions.img_cnt >= 1
  );
  let inital_width = ref(0);
  let current_width = ref(0);

  const handleResizeFunc = (html) => {
    current_width.value = html.clientWidth;
  };

  watch(
    () => content_ref.value,
    (html) => {
      if (html) {
        inital_width.value = html.clientWidth;
        current_width.value = html.clientWidth;
        window.addEventListener('resize', handleResizeFunc);
      }
    },
    { immediate: true }
  );

  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResizeFunc);
  });

  const image_wrapper_style = computed(() => {
    const to_scale =
      inital_width.value === 0 || current_width.value === 0
        ? 1
        : current_width.value / inital_width.value;
    const images = link.value.positions?.imgs ?? [];
    return {
      height: images[0] ? `${images[0].height * to_scale}px` : '',
    };
  });
  const images = computed(() => {
    const to_scale =
      inital_width.value === 0 || current_width.value === 0
        ? 1
        : current_width.value / inital_width.value;
    return (link.value?.positions?.imgs ?? []).map((img, index) => {
      return {
        src: link.value.imgs[index],
        width: img.width,
        height: img.height,
        style: {
          top: `${img.top * to_scale}px`,
          left: `${img.left * to_scale}px`,
          width: `${img.width * to_scale}px`,
          height: `${img.height * to_scale}px`,
        },
      };
    });
  });

  const video_wrapper_style = computed(() => {
    const { width, height } = link.value.video_info;

    if (width < height) {
      return {
        height: container_width.value * 0.61 + 'px',
        width: container_width.value * 0.61 * (3 / 4) + 'px',
      };
    } else {
      return {
        height: container_width.value * 0.61 * (3 / 4) + 'px',
        width: container_width.value * 0.61 + 'px',
      };
    }
  });

  const img_cnt_data = computed(() => {
    if (
      !link.value.positions?.imgs ||
      !('more_img' in (link.value?.positions ?? {}))
    )
      return;
    let left_top = link.value.positions?.imgs.reduce(
      (pre, cur) => {
        if (cur.left + cur.width >= pre.left) {
          pre.left = cur.left + cur.width;
          if (cur.height + cur.top > pre.height) {
            pre.height = cur.height + cur.top;
          }
        }
        return pre;
      },
      { left: 0, top: 0, height: 0 }
    );
    console.log(left_top.height);

    return {
      style:
        link.value.positions.more_img === 1
          ? {
              top: `0px`,
              left: `${left_top.left}px`,
              'border-radius': `0 5px 0 0`,
            }
          : {
              top: `${left_top.height - 17}px`,
              left: `${left_top.left}px`,
              'border-radius': `0 0 5px 0`,
            },
      text:
        link.value.positions?.img_cnt > 10
          ? `10+张`
          : `共${link.value.positions?.img_cnt}张`,
    };
  });

  return {
    is_show_image_wrapper,
    image_wrapper_style,
    images,
    img_cnt_data,
    video_wrapper_style,
  };
}

const { is_show_image_wrapper, image_wrapper_style, images, img_cnt_data } =
  useImage(link, outer_ref);
</script>

<style lang="scss">
.cpt-bbs-imgs-wrapper {
  position: relative;
  width: 100%;
  max-width: 100%;
  overflow: hidden;
  .base-img {
    position: absolute;
    top: 0;
    left: 0;
    border-radius: 5px;
    overflow: hidden;
    img {
      width: 100%;
      height: 100%;
      border-radius: inherit;
      object-fit: cover;
    }
  }
  .img-cnt-wrapper {
    position: absolute;
    top: 0;
    left: 0;
    background-color: #000;
    padding: 0px 3px;

    white-space: nowrap;

    transform: translateX(-100%);
    .img-cnt-text {
      color: #fff;
      text-align: center;
      font-size: 10px;
      font-weight: 500;
    }
    // border-radius: 0px 5px 0px 0px;
  }
}
</style>
