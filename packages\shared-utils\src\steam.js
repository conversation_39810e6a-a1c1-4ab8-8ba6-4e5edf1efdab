/**
 * Steam 相关工具函数
 */

/**
 * 获取游戏 logo 的 CSS 类名
 * @param {string} position - logo 定位位置 (BottomLeft, CenterCenter, UpperRight, etc.)
 * @returns {object} CSS 类名对象
 */
export function getLogoPositionClasses(position) {
  const actualPosition = position || 'BottomLeft';

  const classMap = {
    'BottomLeft': 'bottom-left',
    'CenterCenter': 'center-center',
    'UpperRight': 'upper-right',
    'UpperLeft': 'upper-left',
    'BottomRight': 'bottom-right',
    'UpperCenter': 'upper-center',
    'BottomCenter': 'bottom-center',
    'CenterLeft': 'center-left',
    'CenterRight': 'center-right'
  };
  
  const className = classMap[actualPosition];
  if (!className) {
    console.warn(`Unknown logo position: ${actualPosition}, using default BottomLeft`);
    return { 'bottom-left': true };
  }
  
  return { [className]: true };
}

/**
 * 获取游戏 logo 的内联样式
 * @param {object} logoData - logo 数据对象
 * @param {string} logoData.logo_width_pct - logo 宽度百分比
 * @param {string} logoData.logo_height_pct - logo 高度百分比
 * @param {object} options
 * @param {number} options.maxWidth - 最大宽度（像素），默认为 500
 * @param {number} options.maxHeight - 最大高度（像素），默认为 350
 * @returns {object} 内联样式对象
 */
export function getLogoInlineStyles(logoData, options = {}) {
  if (!logoData) return {};
  
  const { maxWidth = 500, maxHeight = 175 } = options;
  const styles = {};
  
  const widthPct = logoData.logo_width_pct || '28';
  styles.width = `${widthPct}%`;
  styles.maxWidth = `${maxWidth}px`;

  const heightPct = logoData.logo_height_pct || '40';
  styles.height = `${heightPct}%`;
  styles.maxHeight = `${maxHeight}px`;
  
  return styles;
}

/**
 * 检查是否应该显示游戏 logo
 * @param {object} libraryImage - 库图片数据
 * @returns {boolean} 是否显示 logo
 */
export function shouldShowGameLogo(libraryImage) {
  return !!(libraryImage && libraryImage.logo);
}
