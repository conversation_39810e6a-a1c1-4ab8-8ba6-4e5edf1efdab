const {getLocalMiniProEnable, getMiniProgramSetting} = require('./mini_sdk')
const fs = require('fs');
const path = require('path');

function preRequireMinipro(mini_map) {
  for (mini_pro_id in mini_map) {
    const mini_program = mini_map[mini_pro_id]
    let mini_pro_enable = getLocalMiniProEnable(mini_pro_id) || getMiniProgramSetting(mini_pro_id)?.enable
    if(mini_pro_id === 'hardware_info') mini_pro_enable = getLocalMiniProEnable('hardware_detect')
    if (mini_program.pre_require && mini_pro_enable) {
      // let miniproPath = path.join(global.miniprogram_base_dir, `miniprogram/${mini_pro_id === 'hardware_info' ? 'hardware_detect' : mini_pro_id}`)
      // if (fs.existsSync(miniproPath)) {
      global.MiniProgram.initInstance(mini_pro_id, (instance) => {
        let preRquirePath = path.join(instance.moduleDir, `./${instance.latestVersion}/app.asar/preRequire.js`)
        if (fs.existsSync(preRquirePath)) {
          require(preRquirePath)
        }
      }, {
        showLoading: false,
        addInstanceGroup: false
      })
      // }
    }
  }
}

module.exports = {
  preRequireMinipro
}