const _module = require("module");
const path = require("path");
const fs = require("fs");
const v8 = require("v8");
const vm = require("vm");
v8.setFlagsFromString('--no-flush-bytecode');
const utils_1 = require("./utils");
function loadBytecode(filename) {
    const byteBuffer = fs.readFileSync(filename, null);
    let bytesource = '';
    try {
        bytesource = fs.readFileSync(filename.replace(/\.bytecode$/i, '.jsm'), 'utf-8');
    }
    catch (e) { }
    utils_1.headerUtils.set(byteBuffer, 'flag_hash', (0, utils_1.getReferenceFlagHash)());
    const sourceLength = utils_1.headerUtils.buf2num(utils_1.headerUtils.get(byteBuffer, 'source_hash'));
    const dummySource = bytesource.length === sourceLength ? bytesource : ' '.repeat(sourceLength);
    const script = new vm.Script(dummySource, {
        filename: filename,
        cachedData: byteBuffer
    });
    if (script.cachedDataRejected) {
        console.log(script.cachedDataRejected)
        throw new Error('v8 bytecode校验失败');
    }
    return script;
}
exports.loadBytecode = loadBytecode;
function execByteCode(filename) {
    const script = loadBytecode(filename);
    return script.runInThisContext();
}
exports.execByteCode = execByteCode;
_module._extensions['.jsc'] = function loadModule(module, filename) {
    const wrapperFn = execByteCode(filename);
    const require = (0, utils_1.makeRequireFunction)(module);
    wrapperFn.bind(module.exports)(module.exports, require, module, filename, path.dirname(filename));
};
