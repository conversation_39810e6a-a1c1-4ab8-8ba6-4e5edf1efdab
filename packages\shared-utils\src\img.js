// 格式化webp参数
export function formatWebpParams(urlObj, quality = 75) {
  let cdnReg = /^(https|http):\/\/[a-zA-Z0-9]+\.(max-c|maxjia)\.com/;
  if (window.SUPPORT_WEBP) {
    if (typeof urlObj === "object") {
      Object.keys(urlObj).forEach((key) => {
        if (cdnReg.test(urlObj[key])) {
          let url = urlObj[key].split("?")[0];
          urlObj[key] = `${url}?imageMogr2/format/webp/quality/${quality}`;
        }
      });
    } else if (urlObj) {
      if (cdnReg.test(urlObj)) {
        let url = urlObj.split("?")[0];
        urlObj = `${url}?imageMogr2/format/webp/quality/${quality}`;
      }
    }
  }
  return urlObj;
}

export function webpFormat(urlObj, quality) {
  return new Promise((resolve) => {
    if (window.SUPPORT_WEBP) {
      urlObj = formatWebpParams(urlObj, quality);
      resolve(urlObj);
    } else {
      var img = new Image();
      img.onload = function () {
        var result = img.width > 0 && img.height > 0;
        if (result) {
          window.SUPPORT_WEBP = true;
          urlObj = formatWebpParams(urlObj, quality);
        }
        resolve(urlObj);
      };
      img.onerror = function () {
        window.SUPPORT_WEBP = false;
        resolve(urlObj);
      };
      img.src =
        "data:image/webp;base64,UklGRlIAAABXRUJQVlA4WAoAAAASAAAAAAAAAAAAQU5JTQYAAAD/////AABBTk1GJgAAAAAAAAAAAAAAAAAAAGQAAABWUDhMDQAAAC8AAAAQBxAREYiI/gcA";
    }
  });
}