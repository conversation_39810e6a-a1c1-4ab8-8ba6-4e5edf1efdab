import { createApp, h } from 'vue'
import DirectiveTooltip from './DirectiveTooltip.vue'

export default class TooltipManager {
  static instance = null
  tooltipInstance = null
  currentTarget = null
  mountEl = null

  static getInstance() {
    if (!TooltipManager.instance) {
      TooltipManager.instance = new TooltipManager();
    }
    return TooltipManager.instance;
  }

  createTooltip(el) {
    const data = el.binding_value || {}
    if (data.disable) return
    
    // 先销毁现有的tooltip
    if (this.tooltipInstance) {
      this.destroyTooltip(this.currentTarget)
    }
    
    this.currentTarget = el
    const targetRect = el.getBoundingClientRect();
    
    // 尝试创建Vue组件版本的tooltip
    try {
      const app = createApp(DirectiveTooltip, {
        target_el: el,
        targetRect: {
          x: targetRect.x,
          y: targetRect.y,
          width: targetRect.width,
          height: targetRect.height,
          top: targetRect.top,
          right: targetRect.right,
          bottom: targetRect.bottom,
          left: targetRect.left
        },
        placement: data.placement || 'top',
        popperClass: data.popperClass || '',
        text: data.text || '',
        pointer: data.pointer || 'center',
        autoEllipsis: data.autoEllipsis || false,
        maxWidth: data.maxWidth || null,
        offset: data.offset || { x: 0, y: 0 },
      });
    
      const vueMountEl = document.createElement('div')
      this.tooltipInstance = app.mount(vueMountEl)
      this.mountEl = vueMountEl
      document.body.appendChild(vueMountEl);
    } catch (error) {
      console.error('Failed to create Vue tooltip:', error);
      // 如果Vue组件创建失败，创建一个简单的tooltip作为备选
      const simpleTooltip = document.createElement('div')
      simpleTooltip.style.cssText = `
        position: fixed;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 8px 12px;
        border-radius: 4px;
        font-size: 14px;
        z-index: 9999;
        pointer-events: none;
        left: ${targetRect.left + targetRect.width / 2}px;
        top: ${targetRect.top - 40}px;
        transform: translateX(-50%);
      `
      simpleTooltip.textContent = data.text || 'Tooltip'
      document.body.appendChild(simpleTooltip)
      this.mountEl = simpleTooltip
    }
  }

  updateTooltip(el) {
    if (el !== this.currentTarget) return
    const data = el.binding_value || {}
    if (this.tooltipInstance && this.tooltipInstance.updateText) {
      this.tooltipInstance.updateText(data.text)
    }
    if (data.disable && this.tooltipInstance) {
      TooltipManager.getInstance().destroyTooltip(el);
    }
  }

  destroyTooltip(el) {
    if (el !== this.currentTarget) return
    if (this.mountEl) {
      this.mountEl.remove()
      this.tooltipInstance = null
      this.mountEl = null
      this.currentTarget = null
    }
  }
}

export const tooltipDirective = {
  mounted(el, binding) {
    el.binding_value = binding.value
    var enterListener = function(event) {
      if (event.currentTarget !== el) return;
      TooltipManager.getInstance().createTooltip(el);
    };

    var leaveListener = function(event) {
      if (event.currentTarget !== el) return;
      TooltipManager.getInstance().destroyTooltip(el);
    };

    el.addEventListener('mouseenter', enterListener);
    el.addEventListener('mouseleave', leaveListener);

    function unbindDirective() {
      el.removeEventListener('mouseenter', enterListener);
      el.removeEventListener('mouseleave', leaveListener);
      TooltipManager.getInstance().destroyTooltip(el);
      el.unbindDirective = null
      el.binding_value = null
    }

    el.unbindDirective = unbindDirective;
  },

  updated(el, binding) {
    const instance = TooltipManager.getInstance()
    el.binding_value = binding.value
    if (instance.tooltipInstance) {
      instance.updateTooltip(el);
    }
  },

  unmounted(el) {
    el.unbindDirective?.();
  }
}