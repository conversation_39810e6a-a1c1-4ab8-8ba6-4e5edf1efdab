{"name": "@heybox/acc-sdk", "version": "1.0.0", "description": "npm sdk template", "main": "./src/index.js", "module": "./lib/index.esm.js", "scripts": {"build": "npm run rollup", "rollup": "rollup --config build/rollup.config.mjs"}, "keywords": ["heybox", "acc"], "files": ["lib"], "dependencies": {"@babel/runtime-corejs2": "^7.27.1", "axios": "^1.8.4", "core-js": "^2.6.12", "electron-squirrel-startup": "^1.0.1", "js-md5": "^0.8.3", "qrcode": "^1.5.4", "qs": "^6.14.0"}, "peerDependencies": {"electron": ">=33.2.0"}, "devDependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.22.0", "@babel/plugin-proposal-class-properties": "^7.3.4", "@babel/plugin-transform-runtime": "^7.22.0", "@babel/preset-env": "^7.22.0", "@rollup/plugin-babel": "^6.0.4", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "rollup": "^3.28.0", "rollup-plugin-copy": "^3.5.0"}, "author": "xucheng", "license": "MIT"}