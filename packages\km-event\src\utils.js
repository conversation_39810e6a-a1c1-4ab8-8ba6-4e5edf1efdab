const { getPressedXinputButtons } = require('./assets/constant/xinput');
const { getPressedRinputButtons } = require('./assets/constant/rinput');
const { requireAddon } = require('@heybox/electron-utils')
const { eventBus: EventBus } = require('@heybox/electron-utils')

// 判断是否是非法的快捷键配置，快捷键包含鼠标左键或仅为鼠标右键 为非法快捷键
function isIllegalConfig(setting) {
  if (!setting) return false
  if (setting.length === 1 && setting[0] === 2) return true
  if (setting.includes(0)) return true
}

function getKmAddon() {
  return requireAddon("km_event")
}

// 通过addon检测鼠标键盘按键是否被真的按下, 防止由于管理员权限卡快捷键
function getAvalibleKey(list) {
  let mouse_list = [], keyboard_list = []
  // 鼠标按键需要映射为 VKcode, 因此拆分鼠标和键盘的code，鼠标的code范围是0-4 
  // mouse_list = list.filter(k => k <= 4).map(v => MOUSE_CODE_TO_VK[v])
  mouse_list = list.filter(k => k <= 4)
  keyboard_list = list.filter(k => k > 4)
  // TODO 鼠标事件AreTheKeysDown有问题 先不执行
  // mouse_list.length > 0 && (mouse_list = getKmAddon().AreTheKeysDown(mouse_list).map(v => VK_TO_MOUSE_CODE[v]))
  keyboard_list.length > 0 && (keyboard_list = getKmAddon().AreTheKeysDown(keyboard_list))
  return [...mouse_list, ...keyboard_list]
}

// 判断l2中是否包含所有的l1按键，快捷键按下逻辑判断
function checkAllContains(l1, l2) {
  for (let k of l1) {
    if (!l2.includes(k)) {
      return false
    }
  }
  return true
}

// 获取key在km_config中的value
function getValueInKmConfig(key = '', default_value) {
  let keys = key.split('.')
  let result = global.km_config
  try {
    keys.forEach((k) => {
      result = result[k]
    })
    return result ?? default_value
  } catch (e) {
    return null ?? default_value
  }
}

function emitCallback(data, isDown) {
  if (isDown) {
    data.down_callback && data.down_callback(data)
  } else {
    data.up_callback && data.up_callback(data)
  }
}

// 获取xbox手柄按下或抬起事件回调
function xinputCallback(r){
  const triggers = {
    leftTrigger: r.leftTrigger,
    rightTrigger: r.rightTrigger
  }
  EventBus.emit('xinput_event', r, getPressedXinputButtons(r.buttons, triggers), 'xinput') // xbox手柄
}

// 获取ps手柄按下或抬起事件回调
function rinputCallback(r){
  EventBus.emit('xinput_event', r, getPressedRinputButtons(r.buttons, r.dpad), 'rinput') // ps手柄
}

module.exports = {
  isIllegalConfig,
  getKmAddon,
  getAvalibleKey,
  checkAllContains,
  getValueInKmConfig,
  emitCallback,
  xinputCallback,
  rinputCallback,
}