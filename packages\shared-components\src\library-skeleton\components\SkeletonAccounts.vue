<template>
  <div class="skeleton-accounts-wrapper">
    <!-- 账号管理内容区域 -->
    <div class="skeleton-accounts-content">
      <!-- 蓝色渐变背景区域 -->
      <div class="skeleton-accounts-banner">
        <div class="skeleton-accounts-header">
          <div class="skeleton-title">账号管理</div>
          <div class="skeleton-add-btn" @click="handleAddAccount">添加账号</div>
        </div>

        <!-- 已登录状态：三个账号框 -->
        <div v-if="isLoggedIn" class="skeleton-accounts-grid">
          <div class="skeleton-account-card skeleton-account-card--large">
          </div>
          <div class="skeleton-account-card skeleton-account-card--small">
          </div>
          <div class="skeleton-account-card skeleton-account-card--small">
          </div>
        </div>

        <!-- 未登录状态：添加账号区域 -->
        <div v-else class="skeleton-unlogged-content">
          <div class="skeleton-add-account-section">
            <img class="skeleton-add-account-icon" src="https://imgheybox.max-c.com/dev/bbs/2025/07/30/********************************.png" alt="">
            <div class="skeleton-add-account-text">
              <div class="skeleton-add-account-title" @click="handleAddAccount">添加账号</div>
              <div class="skeleton-add-account-subtitle">解锁更多精彩内容</div>
            </div>
          </div>

          <div class="skeleton-placeholder-container">
            <div class="skeleton-placeholder-first-box">
              <div class="skeleton-placeholder-hexagon">
                <svg width="30" height="30" viewBox="0 -4 48 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M24 4L41.569 14V34L24 44L6.431 34V14L24 4Z" stroke="#FF4444" stroke-width="2" fill="none"/>
                  <text x="24" y="28" text-anchor="middle" fill="#FFF" font-family="Microsoft YaHei" font-size="12" font-weight="600">***</text>
                </svg>
              </div>
              <div class="skeleton-placeholder-first-content">
                <div class="skeleton-info-text">国家：*</div>
                <div class="skeleton-info-text">年限：*</div>
              </div>
            </div>

            <div class="skeleton-placeholder-second-box">
              <div class="skeleton-placeholder-item">
                <div class="skeleton-placeholder-icon">***</div>
                <div class="skeleton-placeholder-text">游戏价值￥</div>
              </div>
              <div class="skeleton-placeholder-item">
                <div class="skeleton-placeholder-icon">***</div>
                <div class="skeleton-placeholder-text">游戏时长 h</div>
              </div>
              <div class="skeleton-placeholder-item">
                <div class="skeleton-placeholder-icon">***</div>
                <div class="skeleton-placeholder-text">游戏数量</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="SkeletonAccounts">
// 账号管理区域骨架屏

const props = defineProps({
  isLoggedIn: {
    type: Boolean,
    default: true
  },
  isSteamInstalled: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['addAccount']);

const handleAddAccount = () => {
  emit('addAccount');
};
</script>

<style lang="scss" scoped>
.skeleton-accounts-wrapper {
  margin-bottom: 24px;

  .skeleton-accounts-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 21px;
    height: 52px;

    .skeleton-title {
      color: var(--white-gamerecord-color-white-100a, #FFF);

      font-family: "Microsoft YaHei";
      font-size: 16px;
      font-style: normal;
      font-weight: 700;
      line-height: 22px;
      letter-spacing: 0.16px;
    }

    .skeleton-add-btn {
      display: inline-flex;
      height: 30px;
      flex-direction: column;
      align-items: center;
      flex-shrink: 0;
      padding: 6px 10px;
      border-radius: var(---26x32, 4px);
      background: var(--white-gamerecord-color-white-20a, rgba(255, 255, 255, 0.20));
      color: var(--white-gamerecord-color-white-100a, #FFF);
      text-align: center;
      font-family: "Microsoft YaHei";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      letter-spacing: 0.12px;
      cursor: pointer;
    }
  }

  .skeleton-accounts-content {
    .skeleton-accounts-banner {
      width: 100%;
      height: 270px;
      background: linear-gradient(90deg, #253F78 0%, #1B73A5 100%);
      border-radius: 8px;
      position: relative;
      overflow: hidden;

      .skeleton-accounts-header {
        padding: 20px 24px 16px 24px;
        position: relative;
        z-index: 1;
        border-bottom: 1px solid var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
      }

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 50%,
          rgba(0, 0, 0, 0.1) 100%
        );
        z-index: 1;
        pointer-events: none;
      }

      .skeleton-accounts-grid {
        display: flex;
        gap: 16px;
        padding: 0 24px 24px 24px;
        position: relative;
        z-index: 1;

        .skeleton-account-card {
          height: 170px;
          background: var(--white-gamerecord-color-white-05a,rgba(255, 255, 255, 0.05));
          border: 1px solid var(--white-gamerecord-color-white-05a,rgba(255, 255, 255, 0.05));
          border-radius: 8px;
          backdrop-filter: blur(10px);
          transition: all 0.2s ease;

          &--large {
            width: 508px;
          }

          &--small {
            width: 158px;
          }
        }
      }
    }

    .skeleton-unlogged-content {
      margin-right: 24px;
      margin-left: 24px;
      height: 170px;
      flex: 1 0 0;
      border-radius: 8px;
      border: 1px solid var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
      background: var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
      // padding: 14px 16px 16px 16px;
      position: relative;

      .skeleton-add-account-section {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-top: 14px;
        margin-left: 16px;
        margin-bottom: 12px;

        .skeleton-add-account-icon {
          flex-shrink: 0;
          width: 72px;
          height: 72px;
        }

        .skeleton-add-account-text {
          .skeleton-add-account-title {
            color: #FFF;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            cursor: pointer;
          }

          .skeleton-add-account-subtitle {
            color: var(--white-gamerecord-color-white-70a, rgba(255, 255, 255, 0.70));
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
          }
        }
      }

      .skeleton-placeholder-container {
        display: flex;
        gap: 10px;
        padding: 0 16px 14px 16px;
      }

      .skeleton-placeholder-first-box {
        display: flex;
        width: 116px;
        height: 58px;
        padding: 12px 10px;
        align-items: center;
        flex-shrink: 0;
        border-radius: 5px;
        border: 1px solid var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
        background: var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
        gap: 8px;

        .skeleton-placeholder-hexagon {
          flex-shrink: 0;
        }

        .skeleton-placeholder-first-content {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .skeleton-info-text {
            color: var(--white-gamerecord-color-white-50a, rgba(255, 255, 255, 0.50));
            font-family: "PingFang SC";
            font-size: 10px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }
        }
      }

      .skeleton-placeholder-second-box {
        display: flex;
        flex: 1;
        height: 58px;
        padding: 14px 10px;
        align-items: center;
        flex-shrink: 0;
        border-radius: 5px;
        border: 1px solid var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));
        background: var(--white-gamerecord-color-white-05a, rgba(255, 255, 255, 0.05));

        .skeleton-placeholder-item {
          flex: 1;
          text-align: center;
          height: 36px;

          .skeleton-placeholder-icon {
            color: var(--white-gamerecord-color-white-100a, #FFF);
            font-family: "ALIBABA Font";
            font-size: 20px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
          }

          .skeleton-placeholder-text {
            color: var(--white-gamerecord-color-white-50a, rgba(255, 255, 255, 0.50));
            text-align: center;
            font-family: "PingFang SC";
            font-size: 10px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }
        }
      }
    }
  }
}
</style>
