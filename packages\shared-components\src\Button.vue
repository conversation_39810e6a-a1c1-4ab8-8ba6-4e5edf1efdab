<template>
  <button 
    :class="[
      'heybox-button',
      `heybox-button--${type}`,
      { 'is-disabled': disabled }
    ]"
    :disabled="disabled"
    @click="handleClick"
  >
    <slot></slot>
  </button>
</template>

<script>
export default {
  name: 'HeyboxButton',
  props: {
    type: {
      type: String,
      default: 'default',
      validator: (value) => ['default', 'primary', 'success', 'warning', 'danger'].includes(value)
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClick(evt) {
      this.$emit('click', evt);
    }
  }
};
</script>

<style>
.heybox-button {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  line-height: 1;
  height: 32px;
  white-space: nowrap;
  cursor: pointer;
  color: #333;
  text-align: center;
  box-sizing: border-box;
  outline: none;
  transition: 0.1s;
  font-weight: 500;
  user-select: none;
  vertical-align: middle;
  -webkit-appearance: none;
  background-color: #fff;
  border: 1px solid #dcdfe6;
  padding: 8px 15px;
  font-size: 14px;
  border-radius: 4px;
}

.heybox-button:hover,
.heybox-button:focus {
  color: #409eff;
  border-color: #c6e2ff;
  background-color: #ecf5ff;
}

.heybox-button:active {
  color: #3a8ee6;
  border-color: #3a8ee6;
  outline: none;
}

.heybox-button--primary {
  color: #fff;
  background-color: #409eff;
  border-color: #409eff;
}

.heybox-button--primary:hover,
.heybox-button--primary:focus {
  background-color: #66b1ff;
  border-color: #66b1ff;
  color: #fff;
}

.heybox-button--primary:active {
  background-color: #3a8ee6;
  border-color: #3a8ee6;
  color: #fff;
}

.heybox-button--success {
  color: #fff;
  background-color: #67c23a;
  border-color: #67c23a;
}

.heybox-button--success:hover,
.heybox-button--success:focus {
  background-color: #85ce61;
  border-color: #85ce61;
  color: #fff;
}

.heybox-button--success:active {
  background-color: #5daf34;
  border-color: #5daf34;
  color: #fff;
}

.heybox-button--warning {
  color: #fff;
  background-color: #e6a23c;
  border-color: #e6a23c;
}

.heybox-button--warning:hover,
.heybox-button--warning:focus {
  background-color: #ebb563;
  border-color: #ebb563;
  color: #fff;
}

.heybox-button--warning:active {
  background-color: #cf9236;
  border-color: #cf9236;
  color: #fff;
}

.heybox-button--danger {
  color: #fff;
  background-color: #f56c6c;
  border-color: #f56c6c;
}

.heybox-button--danger:hover,
.heybox-button--danger:focus {
  background-color: #f78989;
  border-color: #f78989;
  color: #fff;
}

.heybox-button--danger:active {
  background-color: #dd6161;
  border-color: #dd6161;
  color: #fff;
}

.heybox-button.is-disabled,
.heybox-button.is-disabled:hover,
.heybox-button.is-disabled:focus,
.heybox-button.is-disabled:active {
  color: #c0c4cc;
  cursor: not-allowed;
  background-image: none;
  background-color: #fff;
  border-color: #ebeef5;
}

.heybox-button--primary.is-disabled,
.heybox-button--primary.is-disabled:hover,
.heybox-button--primary.is-disabled:focus,
.heybox-button--primary.is-disabled:active,
.heybox-button--success.is-disabled,
.heybox-button--success.is-disabled:hover,
.heybox-button--success.is-disabled:focus,
.heybox-button--success.is-disabled:active,
.heybox-button--warning.is-disabled,
.heybox-button--warning.is-disabled:hover,
.heybox-button--warning.is-disabled:focus,
.heybox-button--warning.is-disabled:active,
.heybox-button--danger.is-disabled,
.heybox-button--danger.is-disabled:hover,
.heybox-button--danger.is-disabled:focus,
.heybox-button--danger.is-disabled:active {
  color: #fff;
  background-color: #a0cfff;
  border-color: #a0cfff;
}
</style> 