<!DOCTYPE html>
<html lang="zh-cmn-Hans">

<head>
  <meta charset="utf-8" />
  <meta httpequiv="Content-Type" content="text/html; charset=utf-8">
  <meta httpequiv="Cache-Control" content="no-transform">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="renderer" content="webkit">
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title></title>
  <style>
    * {
      margin: 0;
      border: none;
      box-sizing: border-box;
    }

    html {
      width: 300px;
      min-height: 120px;
      margin: 0;
      overflow: hidden;
    }

    body {
      width: 100%;
      height: 100%;
    }

    #app {
      width: 100%;
      height: 100%;
      padding-right: 20px;
      color: #fff;
      border-radius: 8px 0 0 8px;
      overflow: hidden;
      position: relative;
    }

    .cpt-press-btn-v2 {
      height: 23px;
      white-space: nowrap;
      margin: 0 4px;
      margin-top: -1px;
    }

    .cpt-press-btn-v2 .press-btn {
      height: 100%;
      margin-right: 2px;
      display: inline-block;
      background: linear-gradient(transparent 0%, transparent 50%, rgba(255, 255, 255, 0.50) 50%, rgba(255, 255, 255, 0.50) 190%);
      border-radius: 4px;
    }

    .cpt-press-btn-v2 .press-btn:last-of-type {
      margin-right: 0;
    }

    .cpt-press-btn-v2 .press-btn.isPress .btn-content {
      margin-top: 3px;
    }

    .cpt-press-btn-v2 .press-btn .btn-content {
      width: 100%;
      height: 18px;
      padding: 0 4px;
      background-color: #FFF;
      color: #000;
      text-align: center;
      font-family: Roboto;
      line-height: 20px;
      font-size: 14px;
      font-weight: 500;
      letter-spacing: 0.12px;
      border-radius: 4px;
    }

    .notice-content {
      width: 100%;
      height: 100%;
      padding: 16px;
      animation: notice-show 150ms 500ms cubic-bezier(0, 0, 0.58, 1) forwards;
      opacity: 0;
    }

    .notice-container {
      width: 100%;
      height: 100%;
      animation: notice-in 500ms cubic-bezier(0, 0, 0.58, 1) forwards;
      background: linear-gradient(270deg, #87A96D 0%, #409B72 57.72%, #286E81 100%);
      border-radius: 8px;
      position: relative;
    }

    .notice-container.out {
      animation: notice-out 300ms cubic-bezier(0, 0, 0.58, 1) forwards;
    }

    .notice-container::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      border: 1px solid rgba(255, 255, 255, 0.08);
    }

    .top-block {
      height: 40px;
      display: flex;
      align-items: center;
    }

    .top-block p {
      color: #FFF;
      font-size: 14px;
      line-height: 20px;
      letter-spacing: 0.14px;
    }

    .mini-pro-img {
      width: 40px;
      height: 40px;
      margin-right: 12px;
      border-radius: 21px;
      background: rgba(0, 0, 0, 0.28);
      position: relative;
    }

    .mini-pro-img .mini-icon {
      width: 40px;
      height: 40px;
    }

    .mini-pro-img .chat-icon {
      width: 16px;
      height: 16px;
      border-radius: 4px;
      position: absolute;
      bottom: -2px;
      right: -4px;
    }

    .split {
      margin: 12px 0;
      height: 1px;
      background: rgba(255, 255, 255, 0.08);
    }

    .bottom-block {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .bottom-block .km-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .bottom-block .km-row > span {
      width: 0;
      flex: 1;
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      letter-spacing: 0.14px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    .bottom-block .text {
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      letter-spacing: 0.14px;
    }

    .green {
      width: 280px;
      height: 100%;
      background: var(--brand-text, #7DD95E);
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
      animation: green-slider 500ms cubic-bezier(0, 0, 0.58, 1) forwards;
      border-radius: 8px;
    }

    @keyframes green-slider {
      0% {
        transform: translateX(300px);
      }

      100% {
        transform: translateX(-302px);
      }
    }

    @keyframes notice-in {
      0% {
        transform: translateX(300px);
      }

      100% {
        transform: translateX(0);
      }
    }

    @keyframes notice-show {
      0% {
        opacity: 0;
      }

      100% {
        opacity: 1;
      }
    }

    @keyframes notice-out {
      0% {
        transform: translateX(0);
      }

      100% {
        transform: translateX(300px);
      }
    }
  </style>
</head>

<body>
  <div id="app">
    <template v-if="show">
      <div class="notice-container" :class="{
        out: hide
      }">
        <div class="notice-content">
          <div class="top-block">
            <div class="mini-pro-img" v-if="avatar">
              <img class="mini-icon" :src="avatar" />
              <img class="chat-icon" 
                v-if="icon"
                :src="icon === 'chat-icon' 
                  ? 'data:image/png;base64,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'
                  : icon"
              >
            </div>
            <p v-if="desc">{{desc}}</p>
          </div>
          <div class="split" v-if="shortcut_keys.length > 0 || text"></div>
          <div class="bottom-block" v-if="shortcut_keys.length > 0 || text">
            <div class="km-row" v-for="(item, index) in shortcut_keys" :key="index">
              <span>{{item.label}}</span>
              <div class="cpt-press-btn-v2">
                <template v-for="(v, i) in item.value || []">
                  <span v-if="i > 0">+</span>
                  <div class="press-btn">
                    <div class="btn-content">{{ getKeyCodeName([v]) }}</div>
                  </div>
                </template>
              </div>
            </div>
            <div class="text" v-html="text" v-if="text"></div>
          </div>
        </div>
      </div>
      <div class="green"></div>
    </template>

  </div>

  <script src="../assets/js/vue.min.js"></script>

  <script>
    const mouse_key_map = {
      0: 'Mouse 0',
      1: 'Mouse 1',
      2: 'Mouse 2',
      3: 'Mouse 3',
      4: 'Mouse 4',
      8: 'BackSpace',
      9: 'Tab',
      12: 'Clear',
      13: 'Enter',
      16: 'Shift',
      17: 'Ctrl',
      18: 'Alt',
      19: 'Pause',
      20: 'CapsLock',
      27: 'Esc',
      32: 'Space',
      33: 'Page Up',
      34: 'Page Down',
      35: 'End',
      36: 'Home',
      37: 'Left',
      38: 'Up',
      39: 'Right',
      40: 'Down',
      41: 'Select',
      42: 'Print',
      43: 'Execute',
      44: 'Print Screen',
      45: 'Insert',
      46: 'Delete',
      47: 'Help',
      48: '0',
      49: '1',
      50: '2',
      51: '3',
      52: '4',
      53: '5',
      54: '6',
      55: '7',
      56: '8',
      57: '9',
      65: 'A',
      66: 'B',
      67: 'C',
      68: 'D',
      69: 'E',
      70: 'F',
      71: 'G',
      72: 'H',
      73: 'I',
      74: 'J',
      75: 'K',
      76: 'L',
      77: 'M',
      78: 'N',
      79: 'O',
      80: 'P',
      81: 'Q',
      82: 'R',
      83: 'S',
      84: 'T',
      85: 'U',
      86: 'V',
      87: 'W',
      88: 'X',
      89: 'Y',
      90: 'Z',
      91: 'Left Windows',
      92: 'Right Windows',
      93: 'Applications',
      96: 'Numpad 0',
      97: 'Numpad 1',
      98: 'Numpad 2',
      99: 'Numpad 3',
      100: 'Numpad 4',
      101: 'Numpad 5',
      102: 'Numpad 6',
      103: 'Numpad 7',
      104: 'Numpad 8',
      105: 'Numpad 9',
      106: 'Multiply',
      107: 'Add',
      108: 'Separator',
      109: 'Subtract',
      110: 'Decimal',
      111: 'Divide',
      112: 'F1',
      113: 'F2',
      114: 'F3',
      115: 'F4',
      116: 'F5',
      117: 'F6',
      118: 'F7',
      119: 'F8',
      120: 'F9',
      121: 'F10',
      122: 'F11',
      123: 'F12',
      124: 'F13',
      125: 'F14',
      126: 'F15',
      127: 'F16',
      128: 'F17',
      129: 'F18',
      130: 'F19',
      131: 'F20',
      132: 'F21',
      133: 'F22',
      134: 'F23',
      135: 'F24',
      136: 'Num_Lock',
      137: 'Scroll_Lock',
      144: 'Num Lock',
      145: 'Scroll Lock',
      160: 'Left Shift',
      161: 'Right Shift',
      162: 'Left Ctrl',
      163: 'Right Ctrl',
      164: 'Left Alt',
      165: 'Right Alt',
      166: 'Browser Back',
      167: 'Browser Forward',
      168: 'Browser Refresh',
      169: 'Browser Stop',
      170: 'Browser Search',
      171: 'Browser Favorites',
      172: 'Browser Home',
      173: 'Volume Mute',
      174: 'Volume Down',
      175: 'Volume Up',
      176: 'Media Next Track',
      177: 'Media Previous Track',
      178: 'Media Stop',
      179: 'Media Play/Pause',
      180: 'Launch Mail',
      181: 'Launch Media Select',
      182: 'Launch Application 1',
      183: 'Launch Application 2',
      186: 'OEM 1',
      187: '=',
      188: ',',
      189: '-',
      190: '.',
      191: '/',
      192: '`',
      219: '[',
      220: '\\',
      221: ']',
      222: '\'',
      223: 'OEM 8',
      226: 'OEM 102',
      229: 'PROCESSKEY',
      231: 'PACKET',
      246: 'Attn',
      247: 'CRSEL',
      248: 'EXSEL',
      249: 'EREOF',
      250: 'Play',
      251: 'Zoom',
      252: 'Noname',
      253: 'PA1',
      254: 'Clear',
    }

    const app = new Vue({
      el: '#app',
      data() {
        return {
          shortcut_keys: [],
          desc: '',
          avatar: '',
          icon: '',
          text: '',


          hide: false,
          show: false,
          getKeyCodeName,
        }
      },
      created() {
        this.init()
      },
      methods: {
        init() {
          let showFunc = () => {
              if (!this.show) {
                this.show = true
                this.start()
              }
            }
          window.electronAPI.onShow(showFunc)
          setTimeout(showFunc, 5000)

          let { shortcut_keys, desc, avatar, icon, text } = getParam()
          try {
            this.shortcut_keys = JSON.parse(shortcut_keys)
          } catch (err) {}
          
          this.desc = desc
          this.avatar = avatar
          this.icon = icon
          this.text = text
        },
        start() {
          setTimeout(() => {
            this.hide = true
            setTimeout(() => {
              window.electronAPI.hide()
            }, 300)
          }, 5660)
        }
      }
    })
    function getParam() {
      var url = window.location.href;
      if (url.indexOf('?') < 0) {
        return {}
      }
      return url.match(/([^?=&]+)(=([^&]*))/g).reduce(function (a, v) {
        return (a[v.slice(0, v.indexOf("="))] = decodeURIComponent(v.slice(v.indexOf("=") + 1))), a;
      }, {});
    }

    function getKeyCodeName(v, type = "string", join = "+", errorCb) {
      if (!v) {
        return "";
      }
      try {
        let additionKey = [16, 17, 18];
        let l = JSON.parse(JSON.stringify(v));
        l.sort((a, b) => {
          if (additionKey.includes(a)) {
            if (a === 17) {
              a -= 100;
            }
            a -= 18;
          }
          if (additionKey.includes(b)) {
            if (b === 17) {
              b -= 100;
            }
            b -= 18;
          }
          return a - b;
        });
        l = l.map((v) => {
          let key_name = mouse_key_map[v];
          return key_name || `Key Code ${v}`;
        });
        if (type === "list") {
          return l;
        } else {
          return l.join(join);
        }
      } catch (e) {
        console.error(e)
      }
    }
  </script>
</body>

</html>