<template>
  <div class="cpt-hb-level" :style="steamLevelBg">
    <div class="level-value">
      {{ level }}
    </div>
  </div>
</template>

<script setup name="HbLevel">
import { computed, defineProps } from 'vue';

const props = defineProps({
  level: {
    type: Number,
    required: true,
  },
  levelIcon: {
    type: String,
    required: true,
  },
});

const steamLevelBg = computed(() => {
  let offset = Math.min(9, parseInt((props.level / 10) % 10));
  console.log(props.levelIcon)
  return {
    'background-image': props.levelIcon ? `url(${props.levelIcon})` : `url('https://cdn.max-c.com/heybox/steam/profile/level/100.png')`,
    'background-position': `0 ${offset * 30}px`,
  };
});

// 雪碧图 计算position
const levelDecorationPosition = computed(() => {
  return {};
});
</script>

<style lang="scss">
.cpt-hb-level {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background-size: 30px auto;
  .level-value {
    color: var(--white-gamerecord-color-white-100a, rgba(255, 255, 255, 1));
    text-align: center;
    font-size: 12px;
    font-weight: 700;
    line-height: normal;
  }
  .level-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    img {
      width: 100%;
      height: 100%;
    }
  }
}
</style>
