<template>
  <div
    class="cpt-achievement-item"
    :class="{ grey: !item.achieved }"
    v-tooltip="tooltipConfig"
  >
    <div class="img-cover"></div>
    <img
      :class="item.finishLoad ? 'fade' : 'none'"
      :src="item.icon"
    />
    <div
      class="gold-ring"
      v-if="item.achieved"
    ></div>
  </div>
</template>

<script setup name="AchievementItem">
import { defineProps, computed } from 'vue';
import finishedIcon from '../../../../../assets/images/finished.png';

const props = defineProps({
  item: {
    type: Object,
  },
});

const tooltipConfig = computed(() => {
  if (!props.item) return { disable: true };

  const { name, desc, achieved_percent, achieved } = props.item;

  const achievedIcon = achieved ? `<img src="${finishedIcon}" alt="已完成" style="width: 64px; height: 18px; margin-right: 16px;" />` : '';

  const tooltipContent = `
    <div style="display: flex; width: 322px; padding: 10px 8px 10px 8px; flex-direction: column; align-items: flex-start;">
      <div style="display: flex; align-items: center; width: 100%;">
        <img src="${props.item.icon}" alt="成就图标" style="width: 50px; height: 50px; margin-right: 10px; border-radius: 2px; flex-shrink: 0;" />
        <div style="display: flex; flex-direction: column; flex: 1; min-width: 0;">
          <div style="color: #111111; font-family: 'PingFang SC'; font-size: 14px; font-weight: 500; line-height: normal; margin-bottom: 4px;">${name}</div>
          <div style="display: flex; align-items: center; justify-content: space-between; width: 100%;">
            <span style="color: #64696E; font-family: 'PingFang SC'; font-size: 12px; font-weight: 400; line-height: 18px;">解锁率: ${achieved_percent}%</span>
            ${achievedIcon}
          </div>
        </div>
      </div>
      <div style="font-size: 12px; color: #8c9196; line-height: 16px; word-wrap: break-word; white-space: normal; margin-top: 10px;">${desc}</div>
    </div>
  `;

  return {
    placement: 'top',
    popperClass: 'achievement-tooltip',
    text: tooltipContent,
    pointer: 'center',
    maxWidth: 322,
    offset: { x: 0, y: 3 }
  };
});
</script>

<style lang="scss">
.cpt-achievement-item {
  position: relative;
  width: 34px;
  height: 34px;
  padding: 2px;
  border-radius: 2px;
  &.achieved {
    .img-cover {
      background: var(---general-color-primary-0, #fff);
    }
  }
  img {
    position: relative;
    z-index: 4;
    width: 30px;
    height: 30px;
    object-fit: cover;
    border-radius: 2px;
  }
  .img-cover {
    position: absolute;
    z-index: 3;
    top: 2px;
    left: 2px;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    border-radius: 3px;
    background: gray;
  }
  &:last-child {
    margin-right: 0;
  }
}
</style>
