<template>
  <div class="cpt-title-bar">
    <i
      class="iconfont icon-pc-clockwise-line"
      @click="refreshWeb"
    ></i>
    <i
      class="iconfont icon-common-help-line"
      @click="openFeedback"
    ></i>
    <i
      class="iconfont icon-pc-minimize-line"
      @click="handleWindowOperate('min')"
    ></i>
    <i
      class="iconfont icon-pc-maximization-line"
      :class="{ 'icon-common-copy-line': isMax }"
      @click="handleWindowOperate('max')"
    ></i>
    <i
      class="iconfont icon-common-close-line"
      @click="handleWindowOperate('close')"
    ></i>
  </div>
</template>

<script setup>
import { ipcService } from '@heybox-app-web-shared/ipc'
import { ref } from 'vue'
import { storeGetter } from '@heybox-app-web-shared/utils'

const [never_show_close_dialog] = storeGetter(['never_show_close_dialog'], 'main_config')

// 窗口最大化状态
const isMax = ref(false)
windowAPI.getIsMainWindowMaximized().then((res) => {
  isMax.value = res
})
windowAPI.onMainWindowState((_event, value) => {
  if (value === 'maximize') {
    isMax.value = true
  } else if (value === 'unmaximize') {
    isMax.value = false
  }
})

// 刷新网页
const refreshWeb = () => {
  electronAPI.refreshWeb()
}

// 打开反馈
const openFeedback = () => {
  window.popupManagerAPI.show({
    type: 'Dialog',
    cptName: 'UserSettingDialog',
    props: { 
      type: 'feedback'
    }
  })
}

// 窗口操作处理
const handleWindowOperate = (type) => {
  if(type === 'close' && !never_show_close_dialog.value) {
    window.popupManagerAPI.show({
      type: 'Dialog',
      cptName: 'CloseClientDialog',
    })
    return
  }
  windowAPI.mainWindowControl(type)
  if (type === 'max') {
    isMax.value = !isMax.value
  }
}
</script>

<style lang="scss">
.cpt-title-bar {
  height: 36px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  -webkit-app-region: drag;

  .iconfont {
    -webkit-app-region: none;
    cursor: pointer;
    width: 36px;
    height: 36px;
    color: $general-color-text-3;
    font-size: 14px;
    display: flex;
    justify-content: center;
    align-items: center;

    &:hover {
      opacity: 0.8;
    }
  }
}
</style>
