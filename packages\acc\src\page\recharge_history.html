<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>小黑盒加速器插件</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/theme.css">
    <link rel="stylesheet" href="../assets/font/iconfont.css">
    <link rel="stylesheet" href="../assets/font/font.css">
  </head>
  <body>
    <div class="recharge-history-cpt">
      <div class="shading"></div>
      <div class="title-wrapper">
        <div class="title">
          充值记录
        </div>
        <svg id="close-button" class="close pointer" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="Dismiss">
            <path id="Shape" d="M4.08859 4.21569L4.14645 4.14645C4.32001 3.97288 4.58944 3.9536 4.78431 4.08859L4.85355 4.14645L10 9.293L15.1464 4.14645C15.32 3.97288 15.5894 3.9536 15.7843 4.08859L15.8536 4.14645C16.0271 4.32001 16.0464 4.58944 15.9114 4.78431L15.8536 4.85355L10.707 10L15.8536 15.1464C16.0271 15.32 16.0464 15.5894 15.9114 15.7843L15.8536 15.8536C15.68 16.0271 15.4106 16.0464 15.2157 15.9114L15.1464 15.8536L10 10.707L4.85355 15.8536C4.67999 16.0271 4.41056 16.0464 4.21569 15.9114L4.14645 15.8536C3.97288 15.68 3.9536 15.4106 4.08859 15.2157L4.14645 15.1464L9.293 10L4.14645 4.85355C3.97288 4.67999 3.9536 4.41056 4.08859 4.21569L4.14645 4.14645L4.08859 4.21569Z" fill="#424242"/>
          </g>
        </svg>
      </div>
      <div class="recharge-history-wrapper">
        <div class="none-wrapper">
          <p>
            空空如也
          </p>
        </div>
        <div class="table-wrapper">
          <div class="table-header">
            <div class="operate">操作</div>
            <div class="price">支付金额</div>
            <div class="status">状态</div>
          </div>
          <div class="recharge-history-body">
          </div>
        </div>
      </div>
      <div class="button-wrapper">
        <button id="recharge-button" class="primary-button">
          充值中心
        </button>
      </div>
    </div>
  </body>
  <script src="./common/initTheme.js"></script>
  <script type="module">
    import { getUserChargeHistory } from './api/index.js'
    import { getUrlParam } from './common/utils.js'
    let heybox_id = getUrlParam('heybox_id')
    
    function init() {
      let rechargeHistoryList = document.querySelector('.recharge-history-body')
      let closeButton = document.getElementById('close-button')
      let rechargeButton = document.getElementById('recharge-button')
      closeButton.addEventListener('click', close)
      rechargeButton.addEventListener('click', () => {
        window.electronAPI.openPage('recharge', {heybox_id})
      })
      getUserChargeHistory().then(res => {
        console.log(res)
        if (res.status == 'ok') {
          let chargeRecordHistory = res.result.charge_list || []
          if(chargeRecordHistory.length > 0) {
            document.querySelector('.table-wrapper').style.display = 'block'
            document.querySelector('.none-wrapper').style.display = 'none'
          }
          chargeRecordHistory.forEach(item => {
            let row = document.createElement('div')
            row.classList.add('row')
            row.innerHTML = `
              <div class="operate">
                <p class="desc">${item.desc}</p>
                <p class="time-desc">${item.time_desc}</p>
              </div>
              <div class="price"><span class="unit">￥</span>${item.price}</div>
              <div class="status">成功 +${item.days}</div>
            `
            rechargeHistoryList.appendChild(row)
          })
        } else{
          console.log(res.msg)
        }
      })
    }
    function close() {
      window.electronAPI.close('recharge_history')
    }
    init()
  </script>
  <style>
    .recharge-history-cpt {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 24px 24px;
      background-color: var(--nb1r);
      border-radius: 16px;
      overflow: hidden;
      .title-wrapper {
        margin-bottom: 8px;
      }
      .recharge-history-wrapper {
        height: calc(100% - 100px);
        margin-bottom: 4px;
        .none-wrapper {
          width: 100%;
          height: 100%;
          position: relative;
          text-align: center;
          p {
            position: relative;
            font-size: 16px;
            color: var(--nf3r);
            line-height: 18px;
            top: 50%;
            transform: translateY(-50%);
          }
        }
        .table-wrapper {
          display: none;
          height: 100%;
        }
        .table-header {
          height: 44px;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          border-bottom: 1px solid var(--ns3r);
          div {
            font-size: 14px;
            color: var(--nf3r);
            line-height: 44px;
          }
          .operate {
            flex: 1
          }
          .price {
            width: 142px;
          }
          .status {
            width: 180px;
          }
        }
        .recharge-history-body {
          width: calc(100% + 8px);
          height: calc(100% - 44px);
          margin-right: -8px;
          overflow-y: scroll;
          .row {
            height: 60px;
            border-bottom: 1px solid var(--ns3r);
            display: flex;
            align-items: center;
            justify-content: flex-start;
          }
          .row:last-child {
            border-bottom: none;
          }
          .operate {
            flex: 1;
            .desc {
              font-size: 14px;
              color: var(--nf1r);
              font-weight: 600;
              line-height: 20px;
              margin-bottom: 4px;
            }
            .time-desc {
              font-size: 12px;
              color: var(--nf3r);
              line-height: 16px;
            }
          }
          .price {
            width: 142px;
            font-size: 16px;
            font-weight: 700;
            color: var(--nf1r);
            font-family: 'Roboto';
            line-height: 60px;
            .unit {
              font-size: 14px;
            }
          }
          .status {
            width: 180px;
            font-size: 14px;
            color: var(--success);
            font-family: 'Roboto';
            line-height: 60px;
          }
          .status.fail {
            color: var(--danger);
          }
        }
      }
    }
  </style>
</html>
