/**
 * IPC 通信封装
 */

class IpcService {
  constructor() {
    if (!window.electronAPI) {
      console.error('electronAPI 未初始化，请确保在 preload 脚本中正确配置');
    }
  }

  /**
   * 发送消息到主进程
   * @param {string} channel - 通信通道
   * @param {...any} args - 要发送的参数
   */
  send(channel, ...args) {
    window.electronAPI.send(channel, ...args);
  }

  /**
   * 监听来自主进程的消息
   * @param {string} channel - 通信通道
   * @param {Function} callback - 回调函数
   */
  on(channel, callback) {
    window.electronAPI.receive(channel, callback);
  }

  /**
   * 调用主进程方法并等待结果
   * @param {string} channel - 通信通道
   * @param {...any} args - 要发送的参数
   * @returns {Promise<any>} 主进程返回的结果
   */
  async invoke(channel, ...args) {
    return await window.electronAPI.invoke(channel, ...args);
  }

  /**
   * 向主窗口发送消息
   * @param {string} event - 事件名称
   * @param {...any} args - 要发送的参数
   */
  sendToMain(event, ...args) {
    this.send('webView:postMessage', 'main', event, ...args);
  }

  /**
   * 向所有窗口发送消息
   * @param {string} event - 事件名称
   * @param {...any} args - 要发送的参数
   */
  sendToAll(event, ...args) {
    this.send('webView:postMessage', 'all', event, ...args);
  }

  /**
   * 向指定窗口发送消息
   * @param {string} targetKey - 目标窗口的标识符
   * @param {string} event - 事件名称
   * @param {...any} args - 要发送的参数
   */
  sendToTarget(targetKey, event, ...args) {
    this.send('webView:postMessage', targetKey, event, ...args);
  }

  /**
   * 监听来自其他窗口的消息
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数，接收除事件名外的其他参数
   */
  onMessage(event, callback) {
    this.on('webView:receiveMessage', (receivedEvent, ...args) => {
      if (receivedEvent === event) {
        callback(...args);
      }
    });
  }
}

// 创建单例实例
const ipcService = new IpcService();

export { ipcService };
