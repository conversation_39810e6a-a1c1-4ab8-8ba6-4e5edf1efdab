const { contextBridge, ipc<PERSON>enderer } = require('electron');
const store = require('@heybox/electron-utils/store')
const { version } = require('../../package.json');
const { NODE_BIT } = require('../../env');
// 监听 'load' 事件，确保 webView 销毁
window.addEventListener('load', () => {
  const navigationEntry = performance.getEntriesByType('navigation')[0];
  // 检查导航类型是否为 'reload'
  if (navigationEntry && navigationEntry.type === 'reload') {
    console.log('page reload');
    ipcRenderer.send('mainWindow:pageReloaded');
  }
});

contextBridge.exposeInMainWorld('electronAPI', {
  refreshWeb: () => ipcRenderer.send('mainWindow:refresh'),
  sentryReport: (message, stack) => ipcRenderer.send('sentryReport', message, stack),
  setCacheData: (k, v) => {
    ipcRenderer.send('levelstore:set', k, JSON.parse(v))
  },
  delCacheData: (k) => ipcRenderer.invoke('levelstore:del', k),
  getCacheData: (k) => ipcRenderer.invoke('levelstore:get', k),
  refreshData: (cb) => {
    ipcRenderer.on('localstorage:refresh', cb)
  },
  onSetLocalStoreError: (error) => {
    ipcRenderer.on('localstorage:error', error)
  },
  getStoreData: (k, base = 'main_config') => {
    return store.get(`${base}${k ? `.${k}` : ''}`)
  },
  setStoreData: (k, v, base = 'main_config') => {
    store.set(`${base}${k ? `.${k}` : ''}`, v)
  },
  delStoreData: (k, base = 'main_config') => {
    try {
      if(!k) throw new Error('k is required')
      return store.del(`${base}.${k}`)
    } catch (error) {
      console.error('delStoreData error', error)
    }
  },
  setCookie: (v) => ipcRenderer.invoke('set-cookie', v),
  getStoreCookies: () => ipcRenderer.invoke('getStoreCookies'),
  clearStoreCookies: () => ipcRenderer.invoke('clearStoreCookies'),
  onStoreChange: (k, cb, base = 'main_config') => {
    return store.original_store.onDidChange(`${base}${k ? `.${k}` : ''}`, (v) => {
      cb(v)
    })
  },
  openMiniProgram: (moduleName, url) => ipcRenderer.invoke('openMiniProgram', moduleName, url),

  // 发送消息到主进程
  send: (channel, ...args) => {
    ipcRenderer.send(channel, ...args);
  },

  // 从主进程接收消息
  receive: (channel, callback) => {
    // 删除任何现有监听器
    // ipcRenderer.removeAllListeners(channel);

    // 添加新的监听器
    ipcRenderer.on(channel, (event, ...args) => callback(...args));
  },

  // 调用主进程方法并等待结果
  invoke: async (channel, ...args) => {
    return await ipcRenderer.invoke(channel, ...args);
  },
  getDeviceId: () => ipcRenderer.invoke('getDeviceId'),
  checkSteamInstallation: () => ipcRenderer.invoke('steam:checkInstallation'),
  base: 'main_config' // 默认的store存储位置
});

contextBridge.exposeInMainWorld('windowAPI', {
  mainWindowControl: (type) => {
    if (type === 'min') {
      ipcRenderer.send('mainWindow:min')
    } else if (type === 'max') {
      ipcRenderer.send('mainWindow:max')
    } else if (type === 'close') {
      ipcRenderer.send('mainWindow:close')
    } else if (type === 'hide') {
      ipcRenderer.send('mainWindow:hide')
    }
  },
  onMainWindowState: (cb) => {
    ipcRenderer.on('mainWindow:update-window-state', cb)
  },
  getIsMainWindowMaximized: () => ipcRenderer.invoke('mainWindow:get-is-maximized'),
});

contextBridge.exposeInMainWorld('webViewAPI', {
  load: (viewId) => ipcRenderer.send('webView:load', viewId),
  hide: (viewId) => ipcRenderer.send('webView:hide', viewId),
  destroy: (viewId) => ipcRenderer.send('webView:destroy', viewId),
  destroyAll: () => ipcRenderer.send('webView:destroyAll'),
  postMessage: (...args) => ipcRenderer.send('webView:postMessage', ...args),
  onMessage: (event, callback) => {
    ipcRenderer.on('webView:receiveMessage', (receivedEvent, ...args) => {
      if (event === receivedEvent) {
        callback(...args);
      }
    });
  },
  resizeSidebar: (rect) => ipcRenderer.send('webView:resizeSidebar', rect),
  loadDefaultWebView: () => ipcRenderer.send('webView:load', 'default'),
  getDefaultWebView: () => ipcRenderer.invoke('webView:getDefaultWebView'),
});

contextBridge.exposeInMainWorld('popupManagerAPI', {
  /**
   * 展示弹窗
   * @param {*} options
   * @param {'Dialog' | 'Popup'} options.type - 弹窗类型
   * @param {string} options.cptName - 组件名
   * @param {Object} options.props - 组件的props
   * @returns
   */
  show: (options) => ipcRenderer.send('popup:show', options),
  /**
   * 隐藏弹窗
   * @param {Object} options - 配置
   * @param {'Dialog' | 'Popup'} options.type - 弹窗类型
   * @param {string} options.cptName - 组件名
   * @returns
   */
  hide: (options) => ipcRenderer.send('popup:hide', options),
  ensureInitialized: () => ipcRenderer.invoke('popup:ensure-initialized'),
  onMessage: (event, callback) => {
    ipcRenderer.on('popup:receiveMessage', (receivedEvent, ...args) => {
      if (event === receivedEvent) {
        callback(...args);
      }
    });
  },
});

contextBridge.exposeInMainWorld('versionAPI', {
  version,
  exe_bit: NODE_BIT,
  // 获取系统版本
  getWindowsVersion: () => ipcRenderer.invoke('get-windows-version'),
  // 设置下一次启动的系统版本
  setAsarVersion: (version, relaunch) =>
    ipcRenderer.send('set-asar-version', version, relaunch),
  // 获取客户端版本
  getClientVersion: () => ipcRenderer.invoke('get-client-version'),
  onUpdateExeProgress: (callback) => {
    ipcRenderer.on('update-exe-progress', callback)
  },
  onUpdateExeResult: (callback) => {
    ipcRenderer.on('update-exe-result', callback)
  },
  // 下载版本资源文件
  updateClientVersion: (data) => ipcRenderer.send('update-client-version', data),
  // 更新ASAR版本
  updateAsarResource: (version, download_url, callback) => {
    let callbackHandler = (event, action, param) => {
      callback(action, param)
      if (['success', 'error'].includes(action)) {
        ipcRenderer.off('updateAsarResource:callback', callbackHandler)
      }
    }
    ipcRenderer.on('updateAsarResource:callback', callbackHandler)
    ipcRenderer.send('update-asar-resource', version, download_url)
  },
});

contextBridge.exposeInMainWorld('accAPI', {
  initAcc: (smDeviceId) => ipcRenderer.send('acc-sdk:init-acc', smDeviceId),
  logout: () => ipcRenderer.invoke('acc-sdk:logout'),
  startGame: (launch_schema) => ipcRenderer.invoke('acc-sdk:start-game', launch_schema),
  onEventListener: (events) => {
    Object.keys(events).forEach(key => {
      ipcRenderer.on(key, (e, ...args) => {
        events[key](...args)
      })
    })
  },

  // sdk
  startAcc: (params) => ipcRenderer.invoke('heybox:sdk-start-accelerate', params),
  stopAcc: (params) => ipcRenderer.invoke('heybox:sdk-stop-accelerate', params),
  sendRequest: (url, method, params, form) => ipcRenderer.invoke('heybox:send-request', url, method, params, form),
  loginAcc: (params) => ipcRenderer.invoke('heybox:login-by-qr', params),
})

contextBridge.exposeInMainWorld('deviceAPI', {
  getDeviceId: () => ipcRenderer.invoke('getDeviceId'),
})