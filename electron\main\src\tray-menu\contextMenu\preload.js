const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')
contextBridge.exposeInMainWorld('trayContextMenuAPI', {
  onReceiveData: (callback) => ipcRenderer.on('receive-tray-menu-data', (event, data) => callback(data)),
  handleMenuClick: (v) => ipcRenderer.send('handle-menu-click', v),
  handleShowSubMenu: (v) => ipcRenderer.send('handle-show-sub-menu', v),
  getHoverStatus: (cb) => {
    ipcRenderer.on('get-hover-status', cb)
  },
  setHoverStatus: (data) => {
    ipcRenderer.send('set-hover-status', data)
  },
})