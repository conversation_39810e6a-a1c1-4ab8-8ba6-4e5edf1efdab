const utils = require('@heybox/electron-utils')

// 枚举MUSIC_REARRANGE_STRINGS可能的正则表达式
function generateRegexList(str) {
  const list = [new RegExp(`${str}`)]
  const origin = [[str]]
  for (let i = 1; i < str.length - 1; i++) {
    const regTail = str.substring(0, i)
    const regFront = str.substring(i)
    list.push(new RegExp(`^${regFront}.*?${regTail}$`))
    origin.push([regTail, regFront])
  }
  return { list, origin }
}

// 检查是否需要重新排列
function checkIsNeedToRearrange(name, rearranges) {
  for (let r of rearranges) {
    const { list, origin } = r
    const idx = list.findIndex(reg => reg.test(name))
    if (idx !== -1) {
      const ogStr = origin[idx]
      if (ogStr.length === 1) {
        return utils.rearrangeString(name, ogStr[0])
      } else {
        return removeFrontAndTail(name, ogStr)
      }
    }
  }
  return name
}

/**
 * 去除字符串前后的特定字符
 * @param {string} str - 需要处理的字符串
 * @param {Array<string>} list - 包含头部和尾部字符的数组
 * @returns {string} 返回去除头部和尾部后的字符串
 */
function removeFrontAndTail(str, list) {
  const [tail, front] = list
  return str.slice(front.length, -tail.length)
}

module.exports = {
  generateRegexList,
  checkIsNeedToRearrange,
  removeFrontAndTail,
}