export function getVideoBlobMetadata(fileUrl) {
  let videoElement = document.createElement("video");
  videoElement.src = fileUrl;
  videoElement.autoplay = true;
  videoElement.muted = true;
  return new Promise((resolve) => {
    videoElement.addEventListener("canplay", function () {
      let canvas = document.createElement("canvas");
      canvas.width = videoElement.videoWidth; //画板宽
      canvas.height = videoElement.videoHeight; //画板的高
      //通过画板的drawImage属性对图片进行处理
      canvas
        .getContext("2d")
        .drawImage(videoElement, 0, 0, canvas.width, canvas.height);
      //处理完毕通过toDataURL属性转为图片格式拿去base64图片路径
      canvas.toBlob((blob) => {
        resolve(
          {
            posterBase64: canvas.toDataURL(),
            posterFile: new File([blob], "poster"),
            width: videoElement.videoWidth,
            height: videoElement.videoHeight,
            duration: videoElement.duration.toFixed(2),
          },
          "image/png"
        );
      });
    });
  });
}