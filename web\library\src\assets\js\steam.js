export const getSteamInstalledGameInfo = async () => {
  try {
    let steamFolder = await window.electronAPI.getRegeditKey(['HKCU', 'SOFTWARE\\Valve\\Steam'], 'SteamPath')
    if(steamFolder) {
      let installGameList = []
      let vdfPath = `${steamFolder}\\config\\libraryfolders.vdf`
      let vdfData = await window.electronAPI.getFileData(vdfPath)
      console.log(vdfData)
      if(vdfData.libraryfolders) {
        let folderList = vdfData.libraryfolders
        Object.values(folderList).forEach(folder => {
          if(folder.apps) {
            let folderPath = folder.path.replace(/\\\\/g, '\\')
            Object.keys(folder.apps).forEach(game_id => {
              if(folder.apps[game_id] != '0') {
                let gamePath = `${folderPath}\\steamapps\\common`
                let game = {
                  appid: Number(game_id),
                  folder_path: gamePath,
                }
                installGameList.push(game)
              }
            })
          }
        })
      }
      return installGameList
    }
  } catch(err) {
    console.log(err)
    this.$toast.error('读取本地安装游戏发生错误')
  }
}

export const getSteamAccountsInfo = async () => {
  try {
    let steamFolder = await window.electronAPI.getRegeditKey(['HKCU', 'SOFTWARE\\Valve\\Steam'], 'SteamPath')
    if(steamFolder) {
      let accountList = []
      let currentAccount = ''
      let vdfPath = `${steamFolder}\\config\\loginusers.vdf`
      let vdfData = await window.electronAPI.getFileData(vdfPath)
      console.log(vdfData)
      if(vdfData.users) {
        let userList = vdfData.users
        Object.keys(userList).forEach(user => {
          let userData = userList[user]
          accountList.push({
            steam_id: user,
            account_name: userData.AccountName,
            nick_name: userData.PersonaName,
            timestamp: userData.Timestamp,
          })
          if(userData.MostRecent === '1') {
            currentAccount = user
          }
        })
        accountList.sort((a, b) => {
          return b.timestamp - a.timestamp
        })
        if(!currentAccount) {
          currentAccount = accountList[0].steam_id
        }
      }
      return {
        accountList,
        currentAccount,
      }
    }
  } catch(err) {
    console.log(err)
    this.$toast.error('读取本地安装游戏发生错误')
  }
}

export const setOfflineMode = async (steam_id) => {
  try {
    let steamFolder = await window.electronAPI.getRegeditKey(['HKCU', 'SOFTWARE\\Valve\\Steam'], 'SteamPath')
    if(steamFolder) {
      let vdfPath = `${steamFolder}\\config\\loginusers.vdf`
      let vdfData = await window.electronAPI.getFileData(vdfPath)
      console.log(vdfData)
      if(vdfData.users) {
        vdfData.users[steam_id].WantsOfflineMode = '1'
      }
      let res = await window.electronAPI.writeFileData(vdfPath, vdfData)
      // read
      let vdfData2 = await window.electronAPI.getFileData(vdfPath)
      console.log(vdfData2)
      return res
    }
  } catch(err) {
    console.error('设置离线模式发生错误', err)
  }
}

export const setOnlineMode = async (steam_id) => {
  try {
    let steamFolder = await window.electronAPI.getRegeditKey(['HKCU', 'SOFTWARE\\Valve\\Steam'], 'SteamPath')
    if(steamFolder) {
      let vdfPath = `${steamFolder}\\config\\loginusers.vdf`
      let vdfData = await window.electronAPI.getFileData(vdfPath)
      console.log(vdfData)
      if(vdfData.users) {
        vdfData.users[steam_id].WantsOfflineMode = '0'
      }
      let res = await window.electronAPI.writeFileData(vdfPath, vdfData)
      // read
      let vdfData2 = await window.electronAPI.getFileData(vdfPath)
      console.log(vdfData2)
      return res
    }
  } catch(err) {
    console.error('设置在线模式发生错误', err)
  }
}

export const deleteSteamAccount = async (steam_id) => {
  try {
    let steamFolder = await window.electronAPI.getRegeditKey(['HKCU', 'SOFTWARE\\Valve\\Steam'], 'SteamPath')
    if(steamFolder) {
      let vdfPath = `${steamFolder}\\config\\loginusers.vdf`
      let vdfData = await window.electronAPI.getFileData(vdfPath)
      console.log(vdfData)
      if(vdfData.users) {
        delete vdfData.users[steam_id]
      }
      let res = await window.electronAPI.writeFileData(vdfPath, vdfData)
      // read
      let vdfData2 = await window.electronAPI.getFileData(vdfPath)
      console.log(vdfData2)
      return res
    }
  } catch(err) {
    console.error('删除账号发生错误', err)
  }
}