import { ref } from 'vue'

let refMap = new Map() // 存储ref的map
let listenerMap = new Map() // 监听器map

const MAX_LISTENER_COUNT = 20 // 每个base的最大监听器数量

/**
 * storeGetter 获取electron-store的响应式数据
 * 通过vue3的ref来响应electron-store中的数据变化，以监听主程序中的test1数据变化为例：
 * const [test1] = storeGetter(['test1'], STORE_CONFIG.GLOBAL_BASE) // STORE_CONFIG在各程序的constant配置中定义
 * @param {Array} keys 需要获取的key列表
 * @param {String} base 需要获取哪个程序的store，默认为当前程序
 * @returns {Array} 返回的ref列表
 */
export const storeGetter = (keys, base = null) => { 
  let refList = []
  base = base || window.electronAPI.base
  keys.forEach(key => {
    if(refMap.has(`${base}.${key}`)) {
      // 如果ref已存在，直接返回
      refList.push(refMap.get(`${base}.${key}`))
      return
    }
    const refItem = ref(window.electronAPI.getStoreData(key, base))
    refMap.set(`${base}.${key}`, refItem)
    if(!listenerMap.has(base)) {
      listenerMap.set(base, [])
    }
    if(Array.isArray(listenerMap.get(base))) {
      if(listenerMap.get(base).length < MAX_LISTENER_COUNT) {
        // 监听器数量未达到上限，直接添加监听器
        let unsubscribe = window.electronAPI.onStoreChange(key, (v) => {
          refItem.value = v
        }, base)
        listenerMap.get(base).push(unsubscribe)
      } else {
        // 监听器数量达到上限，移除之前的监听器，改为直接监听设定程序的整个store变化
        listenerMap.get(base).forEach(unsubscribe => unsubscribe())
        let unsubscribe = window.electronAPI.onStoreChange('', (v) => {
          refMap.forEach((refItem, path) => {
            if(path.startsWith(base)) {
              refItem.value = findValueByPath(v, path)
            }
          })
        }, base)
        listenerMap.set(base, unsubscribe)
      }
    } else {
      let unsubscribe = listenerMap.get(base)
      unsubscribe()
      unsubscribe = window.electronAPI.onStoreChange('', (v) => {
        refMap.forEach((refItem, path) => {
          console.log('key', refItem, path)
          if(path.startsWith(base)) {
            refItem.value = findValueByPath(v, path)
          }
        })
      }, base)
      listenerMap.set(base, unsubscribe)
    }
    
    refList.push(refItem)
  })
  return refList
}

/**
 * storeSetter 修改electron-store的数据
 * @param {String} key 需要修改的key
 * @param {Any} value 需要修改的value
 * @param {String} base 需要修改哪个程序的store，默认为当前程序
 */
export const storeSetter = (key, value, base = null) => {
  window.electronAPI.setStoreData(key, value, base || window.electronAPI.base)
}

const findValueByPath = (v, path) => {
  let keys = path.split('.')
  for(let i = 1; i < keys.length; i++) {
    // 从1开始，去掉第0个base
    if (!v) return undefined // 防止访问undefined的属性
    v = v[keys[i]]
  }
  return v
}
