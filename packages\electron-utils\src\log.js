// .ElectronLog.js
const log = require('electron-log')
const { app, ipcMain } = require('electron')
const path = require('path')

// const pkgPath = app.isPackaged ? path.join(process.resourcesPath, 'app.asar/package.json') : path.join(process.cwd(), 'package.json')
let asar_v = global.CLIENT_VERSION

class Log {
  constructor () {}
  initVersion (version) {
    // ====重新定义日志输入的文件位置以及文件名====end
    log.transports.file.level = 'debug'
    log.transports.file.maxSize = 1002430 // 10M
    log.transports.file.format = '[{y}-{m}-{d} {h}:{i}:{s}.{ms}] [{level}]{scope} {text}'
    // ====重新定义日志输入的文件位置以及文件名====start
    const currentDate = new Date().getFullYear() + '-' + (new Date().getMonth() + 1) + '-' + new Date().getDate()
    const fileName = `${currentDate}@${asar_v}.log`
    const basePath = path.join(app.getPath('userData'), 'logs', fileName)
    log.transports.file.resolvePath = () => basePath

    ipcMain.on('electorn-log', (_, ...args) => {
      log_func.info(...args)
    })
  }
  getFile () {
    return log.transports.file.getFile()
  }
  info () {
    log.info(...arguments)
  }
  warn () {
    log.warn(...arguments)
  }
  error () {
    log.error(...arguments)
  }
  debug () {
    log.debug(...arguments)
  }
  verbose () {
    log.verbose(...arguments)
  }
  silly () {
    log.silly(...arguments)
  }
}

module.exports = new Log()