<template>
  <div
    class="cpt-chat-form-button"
    :class="[type, {loading, disabled, success}]"
    @click="handleClick"
  >
    <i 
      v-if="icon"
      class="iconfont"
      :class="icon"
      :style="{'margin-right': text ? '3px' : ''}"
    ></i>
    <i
      class="iconfont icon-correct-bold"
      v-if="success"
      :style="{'margin-right': text ? '3px' : ''}"
    ></i>
    <div
      v-if="loading"
      class="loading-spinner"
    ></div>
    {{ text }}
    <i 
      v-if="suffixIcon"
      class="iconfont suffix"
      :class="suffixIcon"
    ></i>
  </div>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue'

// 定义props
const props = defineProps({
  icon: {
    default: "",
    type: String,
  },
  suffixIcon: {
    default: "",
    type: String,
  },
  text: {
    default: "",
    type: String,
  },
  type: {
    default: "",
    type: String,
  },
  loading: {
    default: false,
    type: Boolean,
  },
  success: {
    default: false,
    type: Boolean,
  },
  disabled: {
    default: false,
    type: <PERSON>olean
  }
})

// 定义emits
const emit = defineEmits(['click'])

// 事件处理函数
const handleClick = (e) => {
  if (props.disabled) return;
  emit('click', e);
}
</script>

<style lang="scss" scoped>
.cpt-chat-form-button {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: $color-gradual-black;
  border-radius: 3px;
  font-size: 14px;
  color: $general-color-plain;
  line-height: 18px;
  cursor: pointer;

  i {
    width: 12px;
    height: 12px;
    font-size: 12px;
  }

  .loading-spinner {
    width: 16px;
    height: 16px;
    position: relative;
    border-radius: 50%;
    margin-right: 6px;
    border: 2px solid var(--d-op-3);

    &::after {
      content: '';
      border: 2px solid transparent;
      border-left: 2px solid var(--d-bd-tx);
      border-radius: 50%;
      position: absolute;
      top: -2px;
      left: -2px;
      bottom: -2px;
      right: -2px;
      animation: rotate 1s infinite linear;
    }
  }

  .icon-correct-bold {
    margin-right: 3px;
  }

  .suffix {
    margin-left: 3px;
  }

  // 不同类型的按钮样式
  &.cancel {
    background: $general-color-bg-6;
    color: $general-color-text-1;
  }

  &.disabled {
    cursor: not-allowed;
    opacity: 0.4;
  }

  &.alert {
    background: $general-color-danger;
  }

  &.transparent {
    background: rgba(255, 255, 255, 0.2);
    color: $general-color-text-1;
  }

  &.gradient {
    color: var(--d-tx-1);
    background: var(--d-bg-ln-3);
  }

  &.loading {
    opacity: 0.4;
    cursor: not-allowed;
  }

  &.primary { 
    color: $general-color-text-6;
    background: $gradient-6;
  }

  &.agreed {
    background: var(--d-fl-sl);
    color: var(--d-bd-tx);
  }
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
