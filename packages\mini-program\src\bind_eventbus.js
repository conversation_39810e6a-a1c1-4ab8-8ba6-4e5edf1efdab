const { 
  getMiniProgramSetting,
  getConfigInDisplayConfig,
} = require('./mini_sdk')
const { registerShowOverlayKmEvent } = require('./register_km_events')
const { eventBus: EventBus } = require('@heybox/electron-utils')

function bindEventBus() {
  EventBus.on('mini-program:bind-process-exist-change', onMiniBindProcessExistChange.bind(this))
  EventBus.on('mini-program:miniprogram_setting-change', onMiniProgramEnableChange.bind(this))
}

function getMiniproEnableShortcutConfig(mini_pro_id) {
  const mini_data = mini_map[mini_pro_id]
  if (mini_data) {
    let enable_shortcut_config = getConfigInDisplayConfig(mini_pro_id, config => config.is_show_shortcut)?.[0]
    return enable_shortcut_config
  }
}

// 监听小程序绑定的游戏状态发生改变
async function onMiniBindProcessExistChange({mini_pro_id, process_exit}) {
  let mini_map_data = global.mini_map[mini_pro_id]
  let mini_program_enable = getMiniProgramSetting(mini_pro_id)?.enable
  if (!mini_program_enable) return
  
  if (process_exit && mini_map_data.listen_start) {
    await global.MiniProgram.processStart(mini_pro_id) 
  } else if (!process_exit && mini_map_data.listen_exit) {
    global.MiniProgram.processExit(mini_pro_id) 
  } else if (!process_exit && mini_map_data.isOverlay) {
    const ins = global.MiniProgram.getInstance(mini_pro_id)
    ins && ins.window && ins.window.close()
  }
}

async function onMiniProgramEnableChange(newV, oldV) {
  console.log("onMiniProgramEnableChange", newV, oldV)
  let allKeySet = new Set(), newObj = {}, oldObj = {}, rigisterKmSet = new Set(), unRigisterKmSet = new Set(), needProgram
  
  for (let v of newV) {
    let mini_pro_id = v.mini_pro_id
    newObj[mini_pro_id] = v
    allKeySet.add(mini_pro_id)
    if (v.enable) {
      rigisterKmSet.add(mini_pro_id)
    } else {
      unRigisterKmSet.add(mini_pro_id)
    }
  }
  if (oldV) {
    for (let v of oldV) {
      let mini_pro_id = v.mini_pro_id
      oldObj[mini_pro_id] = v
      allKeySet.add(mini_pro_id)
      if (v.enable) {
        if (rigisterKmSet.has(mini_pro_id)) {
          rigisterKmSet.delete(mini_pro_id)
        } else {
          unRigisterKmSet.add(mini_pro_id)
        }
      } else {
        if (unRigisterKmSet.has(mini_pro_id)) {
          unRigisterKmSet.delete(mini_pro_id)
        }
      }
    }
  }

  console.log("onMiniProgramEnableChange", 'unRigisterKmSet', unRigisterKmSet)
  for (let mini_pro_id of unRigisterKmSet) {
    let enable_shortcut_config = getMiniproEnableShortcutConfig(mini_pro_id)
    if (enable_shortcut_config) {
      global.kmEvent.removeShortcut(enable_shortcut_config.key)
    }
  }

  console.log("onMiniProgramEnableChange", 'rigisterKmSet', rigisterKmSet)
  for (let mini_pro_id of rigisterKmSet) {
    let enable_shortcut_config = getMiniproEnableShortcutConfig(mini_pro_id)
    if (enable_shortcut_config) {
      registerShowOverlayKmEvent(mini_pro_id, enable_shortcut_config)
    }
  }



  for (let mini_pro_id of allKeySet) {
    if (newObj[mini_pro_id]?.enable === oldObj[mini_pro_id]?.enable || !newObj[mini_pro_id]?.enable) continue

    let mini_map_data = global.mini_map[mini_pro_id]
    if (!mini_map_data) continue

    let {listen_start} = mini_map_data
    let process_exit = !!mini_program_bind_process_state[mini_pro_id]
    if (listen_start && process_exit) {
      await global.MiniProgram.processStart(mini_pro_id) 
    }
  }
}

module.exports = { bindEventBus, onMiniProgramEnableChange };
