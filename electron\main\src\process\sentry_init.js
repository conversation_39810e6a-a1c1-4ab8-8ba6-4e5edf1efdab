const { app, crashReporter, ipcMain, net } = require("electron")
const Sentry = require('@sentry/electron/main');
const fs = require('fs')
const path = require('path')
// const store = require('../store/index')
const addonCrashDir = path.join(process.env.LOCALAPPDATA, 'Qingfeng', 'HeyboxApp', 'CrashFiles');

const sentryInit = (isReport) => {
  // 开启crash捕获
  crashReporter.start({
    productName: 'heybox_app_electron',
    companyName: 'heybox',
    submitURL: '',  // 上传到服务器的地址
    uploadToServer: false, // 不上传服务器
    ignoreSystemCrashHandler: false, // 不忽略系统自带的奔溃处理，为 true 时表示忽略，奔溃时不会生成奔溃堆栈文件
    rateLimit: false,
    compress: false,
  });
  try {
    console.log('[sentry init]', isReport)
    if (isReport) {
      const { RewriteFrames: RewriteFramesIntegration } = require("@sentry/integrations");
      const release = fs.readFileSync(path.join(__dirname, '../../webapp/sentry-release')).toString()
      // const release = '1.00.0-test'
      // const heybox_id = store.get('heybox_id')
      Sentry.init({
        dsn: "https://<EMAIL>/23",
        ignoreErrors: [
          'Network Error',
          'Failed to fetch',
          'timeout of 30000ms exceeded',
          'Request failed with status code 404',
          'ERR_NETWORK_IO_SUSPENDED',
          'ERR_NETWORK_CHANGED',
          'ERR_INTERNET_DISCONNECTED',
        ],
        release,
        integrations: [
          new RewriteFramesIntegration({
            root: '',
            prefix: '',
            iteratee: (frame) => {
              let matches = frame.filename.match(/\/versions\/\d+\.\d+\.\d+?\/app\/source\//)
              if (matches) {
                let name = `app:///src_/${frame.filename.slice(matches.index + matches[0].length)}`
                frame.filename = name.replace('.jsc', '.js')
              } else if (frame.filename.startsWith('app:///source/')) {
                frame.filename = frame.filename.replace('app:///source/', 'app:///src_/').replace('.jsc', '.js')
              } else if (frame.filename.startsWith('src_')) {
                frame.filename = `app:///${frame.filename}`
              }
              if (frame.filename.includes('webapp/js')) {
                frame.filename = 'app:///src_/webapp' + frame.filename.slice(frame.filename.indexOf('webapp/js') + 'webapp/js'.length)
              }
              return frame
            }
          })
        ],
        // initialScope: {
        //   tags: {
        //     'heybox.heybox_id': heybox_id || -1
        //   }
        // },
        debug: true,
      });
      app.on('render-process-gone', (event, webContents, details) => {
        if (details.reason !== 'killed') {
          Sentry.captureMessage(`render-process-gone: ${JSON.stringify(details)}`, {
            level: 'fatal',
            details: details
          })
        }
      });
      app.on('child-process-gone', (event, details) => {
        if (details.reason !== 'killed') {
          Sentry.captureMessage(`child-process-gone: ${JSON.stringify(details)}`, {
            level: 'fatal',
            details: details
          })
        }
      });

      // web页面内报错上报
      ipcMain.on('sentryReport', (e, message, stack) => {
        let error = new Error(message)
        error.stack = stack
        Sentry.captureException(error)
      })
    }
  } catch (e) {
    console.error(e)
  }
}

function getRecentDmpFile(dir) {
  // 获取当前时间戳和 1 分钟前的时间戳
  const now = Date.now();
  const oneMinuteAgo = now - 60 * 1000;

  const files = fs.readdirSync(dir);

  // 筛选最近 1 分钟内生成的 .dmp 文件
  const recentDmpFiles = files
    .filter((file) => {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      return file.endsWith('.dmp') && stats.mtimeMs >= oneMinuteAgo;
    })
    .map((file) => {
      const filePath = path.join(dir, file);
      const stats = fs.statSync(filePath);
      return { file: filePath, time: stats.mtimeMs };
    });

  // 如果存在最近的文件，返回最新的一个
  if (recentDmpFiles.length > 0) {
    // 按时间排序，返回最新的一个文件
    recentDmpFiles.sort((a, b) => b.time - a.time);
    return recentDmpFiles[0].file;
  }

  // 没有符合条件的文件时返回 null
  return null;
}

// 上传最近 1 分钟内生成的 .dmp 文件
function uploadRecentAddonDmpFile() {
  const recentDmpFile = getRecentDmpFile(addonCrashDir);

  if (recentDmpFile) {
    uploadDumpFile(recentDmpFile);
  } else {
    console.log('No recent dump files found.');
  }
}

// 上传 dump 文件
function uploadDumpFile(dumpFilePath) {
  const request = net.request({
    method: 'POST',
    url: 'https://monitor.xiaoheihe.cn/api/23/minidump/?sentry_key=e04a4ac1e9b2410bc8cceaa711db13dc'
  });

  // 设置 multipart/form-data 请求头
  const boundary = '----WebKitFormBoundary' + Math.random().toString(16).substr(2);
  request.setHeader('Content-Type', `multipart/form-data; boundary=${boundary}`);

  // 读取 dump 文件
  const dumpFile = fs.readFileSync(dumpFilePath);

  // 构建 multipart 表单数据
  let postData = `--${boundary}\r\n`;
  postData += `Content-Disposition: form-data; name="upload_file_minidump"; filename="${path.basename(dumpFilePath)}"\r\n`;
  postData += `Content-Type: application/octet-stream\r\n\r\n`;
  postData += dumpFile + '\r\n';
  postData += `--${boundary}\r\n`;
  postData += `Content-Disposition: form-data; name="sentry[release]"\r\n\r\n`;
  postData += `heybox-chat-overlay@3.2.5\r\n`;
  postData += `--${boundary}--\r\n`;
    // 监听响应
    request.on('response', (response) => {
      console.log(`STATUS: ${response.statusCode}`);
      console.log(`HEADERS: ${JSON.stringify(response.headers)}`);
      response.on('data', (chunk) => {
        console.log(`BODY: ${chunk}`);
      });
      response.on('end', () => {
        console.log('No more data in response.');
      });
    }); `--${boundary}--\r\n`;

  // 发送请求
  request.write(postData);
  request.end();
}


module.exports = {
  sentryInit,
  uploadRecentAddonDmpFile,
}