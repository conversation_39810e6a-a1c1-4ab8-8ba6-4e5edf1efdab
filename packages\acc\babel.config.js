module.exports = {
    "presets": [
        [
            "@babel/preset-env",
            {
                "debug": true,
                "useBuiltIns": "usage",
                "modules": false,
                "loose": true,
                "corejs": { version: 2, proposals: true },  // 明确 corejs 配置    
                "targets": {
                    "browsers": ["android>4.0"]
                },
            }
        ]
    ],
    "plugins": [
        [
            "@babel/plugin-transform-runtime",
            {
                "corejs": 2,
                "helpers": true,
                "regenerator": true,
                "absoluteRuntime": true, // 使用绝对路径
            }
        ],
        [
            "@babel/plugin-proposal-class-properties",
            {
                "loose": true
            }
        ],
        [
            "@babel/plugin-transform-private-methods",
            {
                "loose": true
            }
        ],
        [
            "@babel/plugin-transform-private-property-in-object",
            {
                "loose": true
            }
        ]
    ],
}