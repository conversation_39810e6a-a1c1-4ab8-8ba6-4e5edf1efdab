<template>
  <div class="skeleton-game-list">
    <div class="skeleton-game-item" v-for="i in 20" :key="i">
      <div class="skeleton-game-icon"></div>
      <div class="skeleton-game-name"></div>
    </div>
  </div>
</template>

<script setup name="SkeletonGameList">
</script>

<style lang="scss" scoped>
.skeleton-game-list {
  width: 100%;
  height: calc(100% - 135px);
  overflow-y: scroll;
  padding: 0 0 8px 8px;

  .skeleton-game-item {
    display: flex;
    align-items: center;
    padding: 8px;
    margin-bottom: 2px;
    border-radius: 4px;
    cursor: pointer;

    .skeleton-game-icon {
      width: 20px;
      height: 20px;
      aspect-ratio: 1/1;
      background: var(---general-color-bg-0, #F1F2F3);
      border-radius: 4px;
      margin-right: 12px;
    }

    .skeleton-game-name {
      width: 132px;
      height: 12px;
      border-radius: 6px;
      background: var(---general-color-bg-0, #F1F2F3);
    }
  }
}
</style>
