"use strict";(()=>{(()=>{if(console.log("ready:",App.GetServicesInitialized()),window.rpc&&window.rpc.readyState===WebSocket.OPEN)if($REPLACE){if(window.rpcReady===!1){console.log("Replacing before init is not allowed");return}console.log("Closing open socket"),window.rpc.close()}else{console.log("Ignoring duplicate send");return}window.rpcReady=!1;let s=new WebSocket("ws://$HOSTNAME");window.terminate=()=>{window.rpc?.send("Terminate"),window.rpc=void 0};function p(e,t){if(!t)return e;let a={};for(let r of t)a[r]=e[r];return a}let i=new Map;async function d(e){return new Promise((t,a)=>{let r=!1;for(let n of window.appStore.m_mapApps.data_.keys())if(n===e){r=!0;break}if(!r){a(`App with ID ${e} not found in library`);return}if(i.has(e)&&i.get(e)!==void 0){t(i.get(e));return}SteamClient.Apps.RegisterForAppDetails(e,n=>{i.set(e,n),t(n)})})}let o=!1,l;async function u(){return new Promise((e,t)=>{if(o){e(l);return}o=!0,SteamClient.Downloads.RegisterForDownloadOverview(a=>{l=a,e(a)})})}let c={AddShortcut:async e=>{if(!e.args.exe)return{success:!1,error:'Missing argument "exe"'};let t=await SteamClient.Apps.AddShortcut(e.args.name??"",e.args.exe,(e.args.launchOptions??[]).join(" "),e.args.exe);return e.args.name&&SteamClient.Apps.SetShortcutName(t,e.args.name),e.args.icon&&SteamClient.Apps.SetShortcutIcon(t,e.args.icon),e.args.startDir&&SteamClient.Apps.SetShortcutStartDir(t,e.args.startDir),{success:!0,appId:t}},RemoveShortcut:async e=>e.args.appId?(SteamClient.Apps.RemoveShortcut(e.args.appId),{success:!0}):{success:!1,error:'Missing argument "appId"'},InstallApp:async e=>e.args.appId?(SteamClient.Installs.OpenInstallWizard([e.args.appId]),e.args.folderIdx&&(await SteamClient.Installs.SetInstallFolder(e.args.folderIdx),SteamClient.Installs.ContinueInstall()),{success:!0}):{success:!1,error:'Missing argument "appId"'},InstallApps:async e=>e.args.appIds?(SteamClient.Installs.OpenInstallWizard(e.args.appIds),e.args.folderIdx&&(await SteamClient.Installs.SetInstallFolder(e.args.folderIdx),SteamClient.Installs.ContinueInstall()),{success:!0}):{success:!1,error:'Missing argument "appId"'},UninstallApp:async e=>e.args.appId?(SteamClient.Installs.OpenUninstallWizard([e.args.appId],e.args.autoConfirm??!1),{success:!0}):{success:!1,error:'Missing argument "appId"'},UninstallApps:async e=>e.args.appIds?(SteamClient.Installs.OpenUninstallWizard(e.args.appIds,e.args.autoConfirm??!1),{success:!0}):{success:!1,error:'Missing argument "appId"'},RunApp:async e=>{if(!e.args.appId)return{success:!1,error:'Missing argument "appId"'};let a=appStore.m_mapApps.data_.get(e.args.appId);if(!a||!a.value_.installed)return{success:!1,error:`App with ID ${e.args.appId} not installed`};let r=a.value_;return SteamClient.Apps.RunGame(r.gameid,"",-1,500),{success:!0}},TerminateApp:async e=>{if(!e.args.appId)return{success:!1,error:'Missing argument "appId"'};let a=appStore.m_mapApps.data_.get(e.args.appId);if(!a)return{success:!1,error:`App with ID ${e.args.appId} not installed`};let r=a.value_;return SteamClient.Apps.TerminateApp(r.gameid,!1),{success:!0}},GetApps:async e=>{if(e.args?.typeFilter){let t=new Set(e.args.typeFilter);return{success:!0,appIds:appStore.allApps.filter(a=>t.has(a.app_type)&&(!(e.args.installedOnly??!1)||a.installed)).map(a=>a.appid)}}return e.args?.installedOnly?{success:!0,appIds:appStore.allApps.filter(t=>t.installed).map(t=>t.appid)}:{success:!0,appIds:appStore.allApps.map(t=>t.appid)}},CreateDesktopShortcutForApp:async e=>e.args.appId?(SteamClient.Apps.CreateDesktopShortcutForApp(e.args.appId),{success:!0}):{success:!1,error:'Missing argument "appId"'},SetUIMode:async e=>e.args.mode?(SteamClient.UI.SetUIMode(e.args.mode),{success:!0}):{success:!1,error:'Missing argument "mode"'},GetUIMode:async()=>({success:!0,mode:await SteamClient.UI.GetUIMode()}),GetAppInfo:async e=>{if(!e.args.appId)return{success:!1,error:'Missing argument "appId"'};let t=appStore.m_mapApps.data_.get(e.args.appId);if(!t)return{success:!1,error:`App with ID ${e.args.appId} not found in library`};let a=t.value_,r=await d(e.args.appId),n;return a.app_type===1073741824?n={success:!0,id:e.args.appId,type:a.app_type,installed:a.installed??!1,displayName:a.display_name,storeTags:a.store_tag,launchOptions:r.strLaunchOptions,shortcutExe:r.strShortcutExe,startDir:r.strShortcutStartDir,status:r.eDisplayStatus}:n={success:!0,id:e.args.appId,type:a.app_type,installed:a.installed??!1,displayName:a.display_name,storeTags:a.store_tag,launchOptions:r.strLaunchOptions,developerName:r.strDeveloperName,installFolder:r.iInstallFolder,status:r.eDisplayStatus},n},GetDownloadOverview:async()=>{let e=await u(),t;return t={success:!0,...e},t},ResumeAppUpdate:async e=>e.args.appId?(SteamClient.Downloads.ResumeAppUpdate(e.args.appId),{success:!0}):{success:!1,error:'Missing argument "appId"'},EnableAllDownloads:async e=>e.args.enable===void 0?{success:!1,error:'Missing argument "enable"'}:(SteamClient.Downloads.EnableAllDownloads(e.args.enable),{success:!0}),GetTagName:async e=>e.args.tagId?{success:!0,name:appStore.GetLocalizationForStoreTag(e.args.tagId)}:{success:!1,error:'Missing argument "tagId"'},GetTagNames:async e=>({success:!0,names:e.args.tagIds?.map(t=>appStore.GetLocalizationForStoreTag(t))??[]}),GetInstallFolders:async e=>({success:!0,folders:(await SteamClient.InstallFolder.GetInstallFolders()).map(r=>p({index:r.nFolderIndex,isDefault:r.bIsDefaultFolder,isRemovable:!r.bIsFixed,isMounted:r.bIsMounted,freeSpace:r.nFreeSpace,spaceUsedBySteam:r.nUsedSize,totalCapacity:r.nCapacity,driveName:r.strDriveName,folderPath:r.strFolderPath,userLabel:r.strUserLabel},e.args.fields))})};async function m(e){if(e.command){if(!App.GetServicesInitialized()){s.send(JSON.stringify({messageId:e.messageId,success:!1,error:"Steam is not ready, try again later"}));return}let t=c[e.command];if(t)try{let a=await t(e).catch(r=>{s.send(JSON.stringify({messageId:e.messageId,success:!1,error:`Command failed with ${r}`}))});s.send(JSON.stringify({messageId:e.messageId,...a}))}catch(a){s.send(JSON.stringify({messageId:e.messageId,success:!1,error:a}))}else s.send(JSON.stringify({messageId:e.messageId,success:!1,error:`Invalid command: ${e.command}`}))}else s.send(JSON.stringify({success:!1,error:"Missing command"}))}s.addEventListener("message",async e=>{try{if(e.data==="Ready")window.rpcReady=!0;else{let t=JSON.parse(e.data);t.secret&&t.secret==="$SECRET"?await m(t):console.error("Refused unauthorized RPC command")}}catch(t){console.log("RPC command Failed:",t.message)}}),window.rpcSecret="$SECRET",s.addEventListener("open",()=>{s.send("init:$SECRET")}),s.addEventListener("close",()=>{window.rpcSecret=void 0,window.rpc?.close(),window.rpc=void 0}),window.rpc=s})();})();
