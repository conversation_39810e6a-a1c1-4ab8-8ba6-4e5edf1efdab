@font-face {
  font-family: "iconfont"; /* Project id 4950435 */
  src: url('iconfont.woff2?t=1752548535556') format('woff2'),
       url('iconfont.woff?t=1752548535556') format('woff'),
       url('iconfont.ttf?t=1752548535556') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-pc-link-filled:before {
  content: "\e605";
}

.icon-common-arrow-down-line:before {
  content: "\e7a9";
}

.icon-pc-download-filled:before {
  content: "\e7a8";
}

.icon-common-help-line:before {
  content: "\e7a6";
}

.icon-pc-maximization-line:before {
  content: "\e7a7";
}

.icon-pc-minimize-line:before {
  content: "\e7a4";
}

.icon-common-copy-line:before {
  content: "\e7a5";
}

.icon-pc-clockwise-line:before {
  content: "\e7a0";
}

.icon-common-list2-filled:before {
  content: "\e7a1";
}

.icon-common-email-filled:before {
  content: "\e7a2";
}

.icon-bbs-game-filled:before {
  content: "\e7a3";
}

.icon-common-check-line:before {
  content: "\e79f";
}

.icon-common-filter-filled:before {
  content: "\e753";
}

.icon-bbs-comment-filled:before {
  content: "\e762";
}

.icon-bbs-thumbs-up-filled:before {
  content: "\e75f";
}

.icon-game-steam-platform-filled:before {
  content: "\e75c";
}

.icon-common-arrow-up-filled:before {
  content: "\e75a";
}

.icon-common-arrow-right-filled:before {
  content: "\e75e";
}

.icon-common-filter2-filled:before {
  content: "\e75d";
}

.icon-common-arrow-down-filled:before {
  content: "\e75b";
}

.icon-common-arrow-left-line:before {
  content: "\e772";
}

.icon-common-add-line:before {
  content: "\e76e";
}

.icon-common-close-line:before {
  content: "\e76f";
}

.icon-common-more-line:before {
  content: "\e770";
}

.icon-game-heybox-platform-filled_v:before {
  content: "\e760";
}

