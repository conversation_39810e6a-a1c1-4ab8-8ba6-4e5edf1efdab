<template>
  <div class="cpt-news-item-bottom">
    <div class="left-side">
      <GameTag :game="game" />
      <div class="relative-time">
        {{ getRelativeTime(newsData.time) ?? '4分钟前' }}
      </div>
    </div>
    <div class="right-side">
      <div class="right-side-item comment-item">
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M1.52949 2C1.05301 2 0.666748 2.39329 0.666748 2.87843V11.2549C0.666748 11.74 1.05301 12.1333 1.52949 12.1333H3.2858V15.3333L8.00008 12.1333H14.4707C14.9472 12.1333 15.3334 11.74 15.3334 11.2549V2.87843C15.3334 2.39329 14.9472 2 14.4707 2H1.52949Z"
            fill="#C8CDD2"
          />
        </svg>

        <div class="item-count">{{ newsData.comment_num }}</div>
      </div>
      <div class="right-side-item award-item">
        <svg
          width="16"
          height="16"
          viewBox="0 0 16 16"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M9.64576 5.04843L12.6775 5.15517C13.1904 5.17322 13.6913 5.31464 14.1378 5.56742L14.5749 5.81488C15.1902 6.16321 15.4772 6.89466 15.2626 7.568C15.1932 7.78602 15.1344 7.99841 15.0862 8.20518C15.0645 8.29839 15.0536 8.39374 15.0536 8.4894L15.0388 9.26212C15.0303 9.71238 14.9422 10.1576 14.7786 10.5773L14.4666 11.378C14.4326 11.4623 14.4078 11.5501 14.3927 11.6397L14.2267 12.6267C14.1611 13.0165 14.044 13.3959 13.8784 13.7549L13.793 13.94C13.5889 14.3826 13.1458 14.666 12.7083 14.666H7.45826C6.5036 14.666 5.83598 14.489 5.27076 14.2289V5.04843C5.76999 4.66637 6.25701 4.25404 6.43848 3.9182C7.13192 2.6349 6.43848 1.38931 7.71479 0.757082C8.30066 0.466872 10.2839 0.622861 9.64576 5.04843ZM4.39575 5.04102V14.2285L1.83262 14.5947C1.59343 14.6288 1.37182 14.4626 1.33765 14.2234C1.33472 14.203 1.33325 14.1823 1.33325 14.1616V5.47852C1.33325 5.23689 1.52913 5.04102 1.77075 5.04102H4.39575Z"
            fill="#C8CDD2"
          />
        </svg>

        <div class="item-count">{{ newsData.link_award_num }}</div>
      </div>
    </div>
  </div>
</template>

<script setup name="NewsItemBottom">
import { ref, defineProps } from 'vue';
import GameTag from '@/components/func/GameTag.vue';

const props = defineProps({
  newsData: {
    type: Object,
    default: () => {},
  },
});

const game = ref({
  icon: 'https://imgheybox.max-c.com/dev/bbs/2025/06/04/********************************.png',
  name: '无限暖暖',
});

const getRelativeTime = (time) => {
  if (!time) return undefined;
  const now = new Date();
  const diff = now - time;
  const diffMinutes = Math.floor(diff / 60000);
  return diffMinutes;
};
</script>

<style lang="scss">
.cpt-news-item-bottom {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;

  .left-side {
    display: flex;
    align-items: center;
    gap: 4px;
    .relative-time {
      color: var(---general-color-text-3, #8c9196);
      font-size: 11px;
    }
  }

  .right-side {
    display: flex;
    align-items: center;
    gap: 14px;
    .right-side-item {
      display: flex;
      align-items: center;
      gap: 4px;
      .item-count {
        color: var(---general-color-text-4, #c8cdd2);
        font-family: Helvetica;
        font-size: 13px;
      }
    }
  }
}
</style>
