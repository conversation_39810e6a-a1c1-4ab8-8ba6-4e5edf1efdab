<template>
  <Dialog
    class="cpt-steam-install-dialog"
    v-model="show"
    :config="dialogConfig"
    :disableClose="true"
    :disableWrapperClose="true"
    @confirm="confirmHandler"
    @cancel="cancelHandler"
    @close="close"
  >
    <div class="custom-bottom-wrapper">
      <button class="custom-button cancel-button" @click="cancelHandler">
        取消
      </button>
      <button class="custom-button primary-button" @click="confirmHandler">
        <svg xmlns="http://www.w3.org/2000/svg" width="17" height="16" viewBox="0 0 17 16" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M15.9194 7.9998C15.9194 11.9763 12.6958 15.1998 8.71935 15.1998C5.40136 15.1998 2.60757 12.9554 1.7733 9.90203L4.57389 11.1267C4.7687 12.0159 5.53769 12.6798 6.45667 12.6798C7.51756 12.6798 8.37857 11.795 8.38705 10.7003L10.8967 8.82562L10.9167 8.8257C12.3872 8.8257 13.5794 7.59317 13.5794 6.07276C13.5794 4.55235 12.3872 3.31982 10.9167 3.31982C9.44611 3.31982 8.25398 4.55235 8.25398 6.07276C8.25398 6.10426 8.25449 6.13564 8.25551 6.16689L6.5807 8.69211C6.53969 8.68942 6.49833 8.68805 6.45667 8.68805C6.08517 8.68805 5.73818 8.79655 5.44372 8.98456L1.5498 7.33316C1.88618 3.66923 4.96767 0.799805 8.71935 0.799805C12.6958 0.799805 15.9194 4.02335 15.9194 7.9998ZM12.6474 6.07272C12.6474 7.06098 11.8725 7.86213 10.9166 7.86213C9.96075 7.86213 9.18586 7.06098 9.18586 6.07272C9.18586 5.08445 9.96075 4.2833 10.9166 4.2833C11.8725 4.2833 12.6474 5.08445 12.6474 6.07272ZM6.45669 12.2669C7.30226 12.2669 7.98774 11.5582 7.98774 10.684C7.98774 9.80972 7.30226 9.10102 6.45669 9.10102C6.27461 9.10102 6.09995 9.13388 5.93796 9.19418L6.89648 9.60069C7.30234 9.78207 7.58833 10.1985 7.58833 10.684C7.58833 11.3301 7.08168 11.854 6.45669 11.854C6.35277 11.854 6.25212 11.8395 6.15652 11.8124L6.10574 11.7966C6.04463 11.776 5.98581 11.7502 5.92977 11.7197L5.06323 11.3407C5.30456 11.8872 5.83773 12.2669 6.45669 12.2669ZM9.58537 6.07276C9.58537 6.83296 10.1814 7.44923 10.9167 7.44923C11.652 7.44923 12.2481 6.83296 12.2481 6.07276C12.2481 5.31256 11.652 4.69629 10.9167 4.69629C10.1814 4.69629 9.58537 5.31256 9.58537 6.07276Z" fill="white"/>
        </svg>
        前往官网
      </button>
    </div>
  </Dialog>
</template>

<script setup name="SteamInstallDialog">
import { ref } from 'vue';
import {
  useSubComponent,
  getDefaultSubProps,
} from '../composables/useSubComponent';

const props = defineProps(getDefaultSubProps());

const { show, handleClose, Dialog } = useSubComponent({
  initDialog: ({ config: configData }) => {
    if (configData) {
      Object.assign(dialogConfig.value, configData);
    }
    show.value = true;
  },
});

const dialogConfig = ref({
  desc: '使用该功能请先安装Steam'
});

const close = () => {
  handleClose();
};

const confirmHandler = async () => {
  // 打开 Steam 官网
  try {
    await window.electronAPI.invoke('shell:openExternal', 'https://store.steampowered.com/about/');
  } catch (error) {
    console.error('Failed to open Steam website:', error);
  }
  close();
};

const cancelHandler = () => {
  close();
};
</script>

<style lang="scss">
.cpt-steam-install-dialog {
  .hb-dialog-container {
    width: 315px;
    height: 143px;
    padding: 28px 20px 20px 20px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: #f5f5f5;
    border-radius: 8px;
  }

  .dialog-header-group {
    text-align: center;
    margin-top: -8px;

    .desc {
      color: #111111;
      text-align: center;
      font-feature-settings: 'liga' off, 'clig' off;
      font-family: "PingFang SC";
      font-size: 18px;
      font-style: normal;
      font-weight: 500;
      line-height: normal;
      margin: 0;
    }
  }

  .custom-bottom-wrapper {
    display: flex;
    justify-content: center;
    gap: 12px;

    .custom-button {
      width: 132px;
      height: 42px;
      border-radius: 5px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.2s;
      font-family: "PingFang SC";
      font-weight: 500;
      line-height: normal;
      border: none;
      outline: none;

      &.cancel-button {
        background: var(---general-color-bg-6, rgba(20, 25, 30, 0.10));
        color: #111111;
        text-align: center;

        &:hover {
          opacity: 0.8;
        }
      }

      &.primary-button {
        background: var(--Steam, linear-gradient(90deg, #253F78 0%, #1B73A5 100%));
        color: var(---general-color-text-6, #FFF);
        text-align: center;

        &:hover {
          opacity: 0.9;
        }

        // Steam 图标
        svg {
          width: 17px;
          height: 16px;
          margin-right: 4px;
        }
      }
    }
  }
}
</style>
