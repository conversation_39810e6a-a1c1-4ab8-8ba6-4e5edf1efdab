export const ERROR_IMG = {
  dark: 'https://imgheybox.max-c.com/oa/2024/04/23/********************************.png',
  light: 'https://imgheybox.max-c.com/oa/2024/04/23/********************************.png'
}

export function setImgSrc (target, src) {
  if (!src) {
    src = target.getAttribute('data-original')
  }
  if (!src) return

  if (target.src !== src) {
    target.addEventListener('load', imgLoadHandler)
    target.addEventListener('error', imgErrorHandler)
    target.src = src
    target.removeAttribute('data-original')
    target.onload = () => {
      target.classList.remove('opacity')
    }
    target.onerror = () => {
      target.classList.remove('opacity')
    }
  }
}
function imgLoadHandler ({ target }) {
  target.removeAttribute('data-original')
  target.removeEventListener('load', imgLoadHandler)
  target.removeEventListener('error', imgErrorHandler)
}
function imgErrorHandler ({ target }) {
  target.removeAttribute('data-original')
  if (target.getAttribute('data-allow-error')) {
    target.src = ERROR_IMG[document.body.getAttribute('theme') || 'light']
  }
  target.removeEventListener('load', imgLoadHandler)
  target.removeEventListener('error', imgErrorHandler)
}

export function imgObserverRegistry () {
  window.hcImg = {}
  
  window.hcImg.observer = new IntersectionObserver((entries, observer) => {
    entries.forEach((entry) => {
      if (entry.target && entry.boundingClientRect && !(entry.boundingClientRect.width + entry.boundingClientRect.height === 0) && entry.intersectionRect && !(entry.intersectionRect.width + entry.intersectionRect.height === 0)) {
        observer.unobserve(entry.target);
        setImgSrc(entry.target)
      }
    })
  }, {
    root: null,
    threshold: 0
  })
}

export function formatWebplImg(url, q = 40) {
  if (!url) return;
  let isSvg = /\.svg$/.test(url);
  if (isSvg) return url;

  let cdnReg = /^(https|http):\/\/[a-zA-Z0-9]+\.(max-c|maxjia)\.com/;
  if (cdnReg.test(url)) {
    if (window.SUPPORT_WEBP) {
      let u = url.split("?")[0];
      url = `${u}?imageMogr2/format/webp/quality/${q}/ignore-error/1`;
    } else {
      let u = url.split("?")[0];
      url = `${u}?imageMogr2/quality/${q}/ignore-error/1`;
    }
  }
  return url;
}
export function formatThumbnailImg(url, thumbnail) {
  if (!url) return;
  let isSvg = /\.svg$/.test(url);
  if (isSvg) return url;

  let cdnReg = /^(https|http):\/\/[a-zA-Z0-9]+\.(max-c|maxjia)\.com/;
  if (cdnReg.test(url)) {
    if (url.includes("?")) {
      let reg = /\/thumbnail\/.*?\//;
      if (reg.test(url)) {
        url = url.replace(reg, `/thumbnail/${thumbnail}/`);
      } else {
        url += `/thumbnail/${thumbnail}/`;
      }
    } else {
      url += `?imageMogr2/thumbnail/${thumbnail}/`;
    }
  }
  return url;
}
export function formatGifFrame(url, format = "png") {
  let cdnReg = /^(https|http):\/\/[a-zA-Z0-9]+\.(max-c|maxjia)\.com/;
  if (cdnReg.test(url)) {
    if (url.includes("?")) {
      let reg = /\/format\/.*?\//;
      if (reg.test(url)) {
        url = url.replace(reg, `/format/${format}/`);
      } else {
        url += `/format/${format}/`;
      }
    } else {
      url += `?imageMogr2/format/${format}/`;
    }
  }
  return url;
}