import { axiosGetHandler, axiosPostHandler } from './config.js'

/**
 * 获取充值类型
 * @method get
 * @param none
 */
export function getDepositType (args) {
  return window.electronAPI.sendRequest(`https://accoriapi.xiaoheihe.cn/proxy/get_deposit_type/`, 'post', args)
}

/**
 * 获取优惠券列表
 * @method get
 * @param none
 */
export function getCouponList (args) {
	return window.electronAPI.sendRequest(`https://accoriapi.xiaoheihe.cn/proxy/get_coupon_list/`, 'post', args)
}

/**
 * 获取卡券/推广码列表
 * @method get
 * @param none
 */
export function getKeyList (args) {
	return window.electronAPI.sendRequest(`https://accoriapi.xiaoheihe.cn/proxy/vip_key_list/`, 'post', args)
}

/**
 * 获取充值价格
 * @method get
 * @param none
 */
export function getChargePrice (args) {
	return window.electronAPI.sendRequest(`https://accoriapi.xiaoheihe.cn/proxy/get_charge_price/`, 'post', args)
}

/**
 * 获取用户充值记录
 * @method get
 * @param none
 */
export function getUserChargeHistory (args) {
	return window.electronAPI.sendRequest(`https://accoriapi.xiaoheihe.cn/proxy/get_user_charge_history/`, 'post', args)
}

/**
 * 获取会员卡列表
 * @method get
 * @param none
 */
export function getVipKeyList (args) {
	return window.electronAPI.sendRequest(`https://accoriapi.xiaoheihe.cn/proxy/vip_key_list/`, 'post', args)
}

/**
 * 激活会员卡
 * @method get
 * @param none
 */
export function getActivateVipKey (args) {
	return window.electronAPI.sendRequest(`https://accoriapi.xiaoheihe.cn/proxy/activate_vip_key/`, 'post', args)
}


/**
 * 获取支付宝支付结果
 * @method get
 * @param none
 */
export function getChargeResultQuery (args) {
	return window.electronAPI.sendRequest(`https://accoriapi.xiaoheihe.cn/pay/ali_proxy_charge_result_query/`, 'post', args)
}


/**
 * 获取微信支付二维码
 * @method get
 */
export function getOrderWxPayQrCode (option) {
  return window.electronAPI.sendRequest(`http://accapi.xiaoheihe.cn/pay/wx_proxy_buy`, 'post', option)
}

/**
 * 获取新版支付token
 * @method get
 */
export function getPayToken (option) {
  return axiosGetHandler(`https://api.xiaoheihe.cn/pay/booster_unified_pay/`, option)
}

/**
 * 获取连续包月token
 * @method get
 */
//https://accapi.xiaoheihe.cn/pay/charge_proxy_vip/?heybox_id=13553166&amount_paid=15&cycle_paid=18&product_name=proxy_vip_1_cycle&download_source=xiaoheihe
export function getPayMonthToken (option) {
	return axiosGetHandler(`https://api.xiaoheihe.cn/pay/charge_proxy_vip/`, option)
}

/**
 * 新版支付状态查询
 * @method get
 */
export function queryOrderPayStateV2 (option) {
	return axiosGetHandler(`https://api.xiaoheihe.cn/pay/booster_unified_pay_result_query/`, option)
}

/**
 * 查询是否充值成功
 * @method get
 * @param heybox_id
 */
export function checkoutChargeState (option) {
	return window.electronAPI.sendRequest(`http://accapi.xiaoheihe.cn/pay/ali_proxy_charge_result_query/`, 'get', option)
	// return axiosGetHandler(`https://api.xiaoheihe.cn/pay/ali_proxy_charge_result_query/`, option)
}

// /**
//  * 支付状态查询
//  * @method get
//  */
// export function queryOrderPayState (option) {
// 	return axiosGetHandler(`http://api.xiaoheihe.cn/api/mall/acc_pay`, option)
// }