const vm = require("vm");
const HeaderOffsetMap = {
    'magic': 0,
    'version_hash': 4,
    'source_hash': 8,
    'flag_hash': 12
};
exports.headerUtils = {
    set(targetBuffer, type, sourceBuffer) {
        sourceBuffer.copy(targetBuffer, HeaderOffsetMap[type]);
    },
    get(buffer, type) {
        const offset = HeaderOffsetMap[type];
        return buffer.slice(offset, offset + 4);
    },
    buf2num(buf) {
        let ret = 0;
        ret |= buf[3] << 24;
        ret |= buf[2] << 16;
        ret |= buf[1] << 8;
        ret |= buf[0];
        return ret;
    }
};
let _flag_buf;
function getReferenceFlagHash() {
    if (!_flag_buf) {
        const script = new vm.Script('');
        _flag_buf = exports.headerUtils.get(script.createCachedData(), 'flag_hash');
    }
    return _flag_buf;
}
exports.getReferenceFlagHash = getReferenceFlagHash;
function validateString(value, name) {
    if (typeof value !== 'string') {
        throw new Error(`${name} is not string`);
    }
}
function makeRequireFunction(mod) {
    const Module = mod.constructor;
    const require = function require(path) {
        return mod.require(path);
    };
    require.resolve = function resolve(request, options) {
        validateString(request, 'request');
        return Module._resolveFilename(request, mod, false, options);
    };
    require.resolve.paths = function paths(request) {
        validateString(request, 'request');
        return Module._resolveLookupPaths(request, mod);
    };
    require.main = process.mainModule;
    require.extensions = Module._extensions;
    require.cache = Module._cache;
    return require;
}
exports.makeRequireFunction = makeRequireFunction;
