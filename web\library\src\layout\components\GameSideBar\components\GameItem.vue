<template>
  <div class="cpt-game-item" 
    :class="{ 
      'current-game': isCurrentGame,
      'installed': game.installed
    }">
    <div class="game-img">
      <img
        :src="game.icon"
        :alt="game.name"
      />
    </div>
    <div class="game-name" v-html="formattedGameName"></div>
  </div>
</template>

<script setup name="GameItem">
import { defineProps, computed } from 'vue';
const props = defineProps({
  game: {
    type: Object,
    required: true,
  },
  isCurrentGame: {
    type: Boolean,
    default: false,
  },
});

const formattedGameName = computed(() => {
  if (!props.game || !props.game.name) {
    return '';
  }
  const regex = /([a-zA-Z0-9\s:.'™®&!?-]+)/g;
  return props.game.name.replace(regex, '<span class="eng-font">$1</span>');
});
</script>

<style lang="scss">
.cpt-game-item {
  display: flex;
  padding: 7px 8px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  border-radius: 2px;
  width: 100%;
  overflow: hidden;
  cursor: pointer;
  &.installed {
    .game-name {
      color: $general-color-text-1;
    }
  }
  &.current-game {
    background-color: $general-color-bg-0;
    .game-name {
      color: $general-color-text-1;
      font-weight: 600;
    }
    .eng-font {
      font-weight: 400;
      -webkit-text-stroke-width: .5px;
    }
  }
  &:hover {
    background-color: $general-color-bg-0;
  }
  .game-img {
    display: flex;
    width: 20px;
    height: 20px;
    justify-content: center;
    align-items: center;
    aspect-ratio: 1/1;
    border-radius: 5px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .game-name {
    height: 20px;
    flex: 1 0 0;
    overflow: hidden;
    color: var(---general-color-text-3, #8c9196);
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 14px;
    line-height: normal;
  }
}
</style>

