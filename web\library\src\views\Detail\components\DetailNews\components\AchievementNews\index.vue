<template>
  <div
    class="cpt-achievement-news"
    :class="[getCptClass]"
  >
    <NewsItemHeader :user="newsData.user">
      <template #default>
        <div class="extension-content">
          <span class="text">已达成</span>
          <span class="time">{{ '4分钟前' }}</span>
        </div>
      </template>
    </NewsItemHeader>
    <div class="achievement-news-content">
      <div
        class="achievement-news-item"
        v-for="achievement in newsData.achievements"
      >
        <div class="achievement-img">
          <img
            :src="achievement.img"
            alt=""
          />
        </div>
        <div class="achievement-info">
          <div class="achievement-name">{{ achievement.name }}</div>
          <div class="achievement-desc">{{ achievement.description }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="AchievementNews">
import NewsItemHeader from '../NewsItemHeader/index.vue';
import { defineProps, computed } from 'vue';

const props = defineProps({
  newsData: {
    type: Object,
    default: () => {},
  },
});

const getCptClass = computed(() => {
  if (props.newsData.achievements.length > 1) {
    return 'double-col';
  }
});
</script>

<style lang="scss">
.cpt-achievement-news {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12px;

  &.double-col {
    .achievement-news-content {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      gap: 0;

      .achievement-news-item {
        width: 50%;
        .achievement-info {
          .achievement-desc {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
          }
        }
      }
    }
  }

  .achievement-news-content {
    position: relative;
    background: var(---general-color-bg-3, #fafbfc);
    border-radius: 8px;
    border: 0.5px solid var(---general-color-stroke-2, #F3F4F5);
    position: relative;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      border: 1px solid var(---general-color-stroke-2, #f3f4f5);
      border-radius: 8px;
      transform: scale(0.5);
      transform-origin: 0 0;
      width: 200%;
      height: 200%;
      pointer-events: none;
    }

    .achievement-news-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 6px 10px;

      width: 100%;
      height: 72px;
      box-sizing: border-box;
      border-radius: 8px;

      cursor: pointer;
      .achievement-img {
        width: 56px;
        height: 56px;
        min-width: 56px;
        border-radius: 8px;
        overflow: hidden;
        border: 2px solid var(---general-color-stroke-2, rgb(232, 234, 237));

        img {
          width: 100%;
          height: 100%;
          border-radius: inherit;
          object-fit: cover;
        }
      }

      .achievement-info {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .achievement-name {
          color: 1111111;
          font-size: 13px;
          font-weight: 500;
        }

        .achievement-desc {
          color: var(---general-color-text-3, #8c9196);
          font-size: 12px;
        }
      }
    }
  }
}
</style>
