import { xx } from './variable'
import md5 from 'js-md5'

function gencode (path, time, nonce) {
  path = `/${path.split('/').filter(_=>_).join('/')}/`
  let code = ''
  let char = 'JKMNPQRTX1234OABCDFG56789H'
  const nc_md5 = md5((nonce + char).split('').filter(_=>/[0-9]/.test(_)).join(''))
  const kkk = md5(time + path + nc_md5)
  let num = kkk.split('').filter(_=>/[0-9]/.test(_)).slice(0, 9).join('')
  for (let i=0; i<(9 - num.length); i++) {
    num += '0'
  }
  let codeint = Number(num)
  for (let r=0; r<5; r++) {
    let div = ~~(codeint / char.length)
    let mod = codeint % char.length
    codeint = div
    code += char[mod]
  }
  let sign = `${sum(xx(code.slice(-4).split('').map(_ => _.charCodeAt()))) % 100}`
  if (sign.length < 2) {
    sign = 0 + sign
  }
  sign = code + sign
  return sign
}

function sum (arr) {
  return arr.reduce(function(p, c, arr){
    return p + c
  })
}

export { gencode }