const { app, ipcMain } = require('electron');
const { exec } = require('child_process');
const path = require('path');
const fs = require('original-fs');
const os = require('os');
const { LOW_SPEED_CHECK_TIME } = require('../assets/constant/constant');
const { log } = require('@heybox/electron-utils');
const cdn = require('@heybox/electron-utils/cdn')

/**
 * 负责小黑盒PC的版本管理和自动更新
 */
class ClientVersionManager {
  constructor() {
    this.fileName = '';
    this.authResult = null;
    this.isUpdateAsar = false;
    this.downloadPath = os.tmpdir();

    // 初始化IPC通讯
    this.initIpcMain();
    log.info('[ClientVersionManager] Client Version Manager Ready');
  }

  // 初始化Ipc通讯
  initIpcMain() {
    // 获取clientVersion
    ipcMain.handle('get-client-version', this.getClientVersion.bind(this));

    // 更新客户端版本
    ipcMain.on('update-exe-version', this.downloadFiles.bind(this));

    // 修改asar version
    ipcMain.on('set-asar-version', this.handleSetAsarVersion.bind(this));

    // 更新ASAR版本
    ipcMain.on(
      'update-asar-resource',
      this.handleUpdateAsarResource.bind(this)
    );

    // 监听主窗口下载
    mainWindow.webContents.session.on(
      'will-download',
      this.handleWatchDownload.bind(this)
    );

    // 移除初始化时的调用，改为懒加载
    // this.getClientVersion();
    ipcMain.handle('get-windows-version', this.getWindowsVersion.bind(this));
  }

  /**
   * 获取客户端版本信息
   * @typedef {Object} ClientVersion
   * @property {string} client_version - C++的安装版本
   * @property {string} asar_version - electron asar包版本
   * @property {boolean} edit_authority - 是否有文件编辑权限，没有的话跳过asar更新
   * @returns {Promise<ClientVersion>} 返回包含版本信息的对象
   */
  async getClientVersion() {
    const { getClientVersion, authorityCheck } = require('@heybox/electron-utils');

    let v = global.EXE_VERSION || (await getClientVersion());
    // 获取根目录的package.json
    const pkgPath = app.isPackaged ? path.join(process.resourcesPath, 'app.asar/package.json') : path.join(process.cwd(), 'package.json')
    let asar_v = require(pkgPath).version
    const file_name = 'authorityCheck.txt';
    let checkFile = path.join(global.appPath, './resources/', file_name);
    this.authResult = await authorityCheck(checkFile);
    const { report } = require('@heybox/electron-utils');
    report('user-authority', this.authResult);
    let obj = {
      client_version: v, // C++的安装版本
      asar_version: asar_v, // electron asar包版本
      edit_authority: !!(this.authResult.add && this.authResult.delete), // 是否有文件编辑权限，没有的话跳过asar更新
    };
    log.info('[authResult]', this.authResult);
    log.info('[ClientVersionManager] ', obj);
    return obj;
  }

  /**
   * 更新ASAR包
   * @param {*} event
   * @param {string} version 新版本号
   * @param {string} download_url 资源文件url
   * @returns
   */
  async handleUpdateAsarResource(event, version, download_url) {
    const {
      getAsarVersion,
      setNextLaunchVersion,
    } = require('@heybox/electron-utils');

    // 如果 authResult 还没有获取，先获取一次
    if (!this.authResult) {
      await this.getClientVersion();
    }

    if (!this.authResult) {
      global.mainWindow.webContents.send(
        'updateAsarResource:callback',
        'error',
        '无文件夹读写权限'
      );
      log.info('[asar download no auth]', this.authResult);
      return;
    }
    if (this.isUpdateAsar) {
      return;
    }
    log.info('[asar download start]');
    this.isUpdateAsar = true;
    let versionsPath;
    if (app.isPackaged) {
      versionsPath = path.join(__dirname, '../../../../');
    } else {
      // 本地开发调试用，把return删掉 注释打开
      versionsPath = path.join(__dirname, '../../out');
      // fs.mkdirSync(versionsPath, { recursive: true })
    }
    log.info(`[versionPath]: ${versionsPath}`);

    const { downloadFile } = cdn;
    // 确保没有此目录
    getAsarVersion().then((v) => {
      setNextLaunchVersion(v);
      fs.rmSync(path.join(versionsPath, version), {
        recursive: true,
        force: true,
      });
    });

    let zipPath = path.join(versionsPath, `${version}.zip`);
    log.info('[asar download start]');

    let lowSpeedDuration = 0;
    downloadFile(
      download_url,
      zipPath,
      undefined,
      version,
      async (action, param) => {
        if (action === 'success') {
          try {
            event.sender?.send('updateAsarResource:callback', 'extract');
            const { unzipInChildProcess } = require('@heybox/electron-utils');
            await unzipInChildProcess(zipPath, versionsPath);

            event.sender?.send('updateAsarResource:callback', 'success');
            log.info('[asar download success]');
          } catch (error) {
            event.sender?.send(
              'updateAsarResource:callback',
              'error',
              error.message
            );
            log.info('[asar extract error]', error.message);
          } finally {
            this.isUpdateAsar = false;
          }
        } else if (action === 'error') {
          event.sender?.send('updateAsarResource:callback', 'error', param);
          this.isUpdateAsar = false;
          log.info('[asar download error]', param);
        } else if (action === 'progress') {
          event.sender?.send('updateAsarResource:callback', 'progress', param);
          if (param.rate / 1024 < 100) {
            lowSpeedDuration += 0.5;
          } else {
            lowSpeedDuration = 0;
          }
          // TODO 修改DNS提醒
          // if (lowSpeedDuration >= LOW_SPEED_CHECK_TIME) {
          //   mainWindow?.webContents.send('dns-change-warn', 'download');
          // }
          log.info('[asar download progress]', param);
        }
      }
    );
  }

  /**
   * 设置下一次启动的版本号
   * @param {*} e
   * @param {string} version 下一次启动版本号
   * @param {boolean} relaunchApp 是否重启
   */
  handleSetAsarVersion(e, version, relaunchApp) {
    const { setNextLaunchVersion } = require('@heybox/electron-utils');
    setNextLaunchVersion(version).then(() => {
      if (relaunchApp) {
        app.relaunch({ args: process.argv.slice(1).concat(['--relaunch']) });
        app.exit(0);
      }
    });
  }

  /**
   * 下载文件
   * @param {*} _ 
   * @param {*} data 下载文件的数据
   */
  downloadFiles(_, data) {
    this.fileName = 'chat_installer.exe';
    mainWindow && mainWindow.webContents.downloadURL(data.url);
  }

  getWindowsVersion() {
    const { requireAddon } = require('@heybox/electron-utils')
    return requireAddon('get_endpoint_info').getSystemVersion()
  }

  /**
   * 观测下载进度
   * @param {*} _ 
   * @param {*} item 
   * @returns 
   */
  handleWatchDownload(_, item) {
    if (!this.downloadPath || !this.fileName) {
      return;
    }
    let filePath = path.join(this.downloadPath, this.fileName);
    log.info('[exe download start]', filePath);
    // 无需对话框提示， 直接将文件保存到路径
    item.setSavePath(filePath);

    let receivedBytes = 0;
    let lastReceivedBytes = 0;
    let speedCheckInterval = null;
    let lowSpeedDuration = 0;
    let intervalTime = 500;
    let isStartSpeedCheck = false;

    function startSpeedCheck() {
      isStartSpeedCheck = true;
      // TODO 修改local_config
      let local_config = {}
      if (local_config?.never_show_dnc_warn) return;

      speedCheckInterval = setInterval(() => {
        const speed =
          (receivedBytes - lastReceivedBytes) / (intervalTime / 1000); // in bytes per second
        lastReceivedBytes = receivedBytes;

        console.log(`Download speed: ${(speed / 1024).toFixed(2)} KB/s`);

        // if (speed < 100 * 1024) {
        if (speed < local_config.low_download_speed_threshold * 1024) {
          lowSpeedDuration += 0.5;
        } else {
          lowSpeedDuration = 0;
        }

        if (lowSpeedDuration >= LOW_SPEED_CHECK_TIME) {
          mainWindow?.webContents.send('dns-change-warn', 'download');
          clearInterval(speedCheckInterval);
        }
      }, intervalTime);
    }

    item.on('updated', (event, state) => {
      if (state === 'progressing') {
        let rB = item.getReceivedBytes(),
          tB = item.getTotalBytes();
        if (rB && tB) {
          let value = parseInt(
            100 * (item.getReceivedBytes() / item.getTotalBytes())
          );

          // 把百分比发给渲染进程进行展示
          const mainWindow = global.mainWindow
          mainWindow?.webContents.send('update-exe-progress', value);
          // mac 程序坞、windows 任务栏显示进度
          mainWindow && mainWindow.setProgressBar(value);

          receivedBytes = rB;
          log.info('[exe download progressing]', rB, tB);
        }
      }
      if (!isStartSpeedCheck) {
        startSpeedCheck();
      }
    });
    item.once('done', (event, state) => {
      log.info('[exe download done]', state);
      clearInterval(speedCheckInterval);
      speedCheckInterval = null;
      console.log(state);
      mainWindow && mainWindow.setProgressBar(0);
      if (state === 'completed') {
        let workerProcess = exec(`${fileName} update`, {
          cwd: downloadPath,
        });
        // 打印正常的后台可执行程序输出
        workerProcess.stdout.on('data', function (data) {
          console.log('stdout: ' + data);
        });
        // 打印错误的后台可执行程序输出
        workerProcess.stderr.on('data', function (data) {
          console.log('stderr: ' + data);
        });
        // 退出之后的输出
        workerProcess.on('close', function (code) {
          log.info('out code：' + code);
        });
      } else {
        mainWindow && mainWindow.webContents.send('update-exe-result', false);
      }
    });
  }
}

module.exports = new ClientVersionManager();
