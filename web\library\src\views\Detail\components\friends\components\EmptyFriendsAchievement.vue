<template>
  <div class="empty-friends-achievement">
    <div class="empty-content">
      <div class="empty-icon">
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 32 32" fill="none">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M6.00016 4C4.5274 4 3.3335 5.19391 3.3335 6.66667V10.6667C3.3335 12.1394 4.5274 13.3333 6.00016 13.3333H11.3335C12.8063 13.3333 14.0002 12.1394 14.0002 10.6667V6.66667C14.0002 5.19391 12.8063 4 11.3335 4H6.00016ZM6.00016 18.6667C4.5274 18.6667 3.3335 19.8606 3.3335 21.3333V25.3333C3.3335 26.8061 4.5274 28 6.00016 28H11.3335C12.8063 28 14.0002 26.8061 14.0002 25.3333V21.3333C14.0002 19.8606 12.8063 18.6667 11.3335 18.6667H6.00016ZM17.0002 6C17.0002 5.26362 17.5971 4.66667 18.3335 4.66667H29.0002C29.7365 4.66667 30.3335 5.26362 30.3335 6C30.3335 6.73638 29.7365 7.33333 29.0002 7.33333H18.3335C17.5971 7.33333 17.0002 6.73638 17.0002 6ZM18.3335 19.3333C17.5971 19.3333 17.0002 19.9303 17.0002 20.6667C17.0002 21.403 17.5971 22 18.3335 22H29.0002C29.7365 22 30.3335 21.403 30.3335 20.6667C30.3335 19.9303 29.7365 19.3333 29.0002 19.3333H18.3335ZM17.0002 11.3333C17.0002 10.597 17.5971 10 18.3335 10H29.0002C29.7365 10 30.3335 10.597 30.3335 11.3333C30.3335 12.0697 29.7365 12.6667 29.0002 12.6667H18.3335C17.5971 12.6667 17.0002 12.0697 17.0002 11.3333ZM18.3335 24.6667C17.5971 24.6667 17.0002 25.2636 17.0002 26C17.0002 26.7364 17.5971 27.3333 18.3335 27.3333H29.0002C29.7365 27.3333 30.3335 26.7364 30.3335 26C30.3335 25.2636 29.7365 24.6667 29.0002 24.6667H18.3335Z" fill="#C8CDD2"/>
        </svg>
      </div>
      <div class="empty-title">暂无和此游戏相关的好友和成就</div>
    </div>
  </div>
</template>

<script setup>
</script>

<style lang="scss" scoped>
.empty-friends-achievement {
  display: flex;
  width: 334px;
  padding: 60px 0px;
  flex-direction: column;
  align-items: center;
  border-radius: var(--general-size-radius-8, 8px);
  background: var(---general-color-primary-0, #FFF);
}

.empty-content {
  display: flex;
  padding: 50px 36px;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  align-self: stretch;
}

.empty-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  
  svg {
    width: 32px;
    height: 32px;
  }
}

.empty-title {
  align-self: stretch;
  color: $general-color-text-3;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 13px;
  font-style: normal;
  font-weight: 400;
  line-height: 17px;
}
</style>
