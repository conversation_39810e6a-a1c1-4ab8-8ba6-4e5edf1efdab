class Button {
  constructor(props) {
    this.props = props
  }
  render() {
    return `
      <button id="${this.props.id}" class="${this.props.className}">
        ${this.props.text}
      </button>
    `
  }
  initClickEvent() {
    let button = document.getElementById(this.props.id)
    button.addEventListener('click', this.props.onClick)
  }
  destroy() {
    let button = document.getElementById(this.props.id)
    button.removeEventListener('click', this.props.onClick)
  }
}


