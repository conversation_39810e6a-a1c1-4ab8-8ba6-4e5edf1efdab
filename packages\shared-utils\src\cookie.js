/**
 * 设置cookie
 * @param {String} name cookie名称
 * @param {String} value cookie值
 * @param {Number} days 过期天数
 * @param {String} path cookie路径
 * @param {String} domain cookie域
 */
export function setCookie(name, value, days = 30, path = '/', domain = '') {
  const date = new Date();
  date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
  const expires = `expires=${date.toUTCString()}`;
  const domainStr = domain ? `; domain=${domain}` : '';
  document.cookie = `${name}=${encodeURIComponent(value)}; ${expires}; path=${path}${domainStr}`;
}

/**
 * 获取cookie
 * @param {String} name cookie名称
 * @returns {String} cookie值
 */
export function getCookie(name) {
  const nameEQ = `${name}=`;
  const ca = document.cookie.split(';');
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i];
    while (c.charAt(0) === ' ') c = c.substring(1, c.length);
    if (c.indexOf(nameEQ) === 0) return decodeURIComponent(c.substring(nameEQ.length, c.length));
  }
  return '';
}

/**
 * 删除cookie
 * @param {String} name cookie名称
 * @param {String} path cookie路径
 * @param {String} domain cookie域
 */
export function removeCookie(name, path = '/', domain = '') {
  const domainStr = domain ? `; domain=${domain}` : '';
  document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 GMT; path=${path}${domainStr}`;
}

/**
 * 检查cookie是否存在
 * @param {String} name cookie名称
 * @returns {Boolean} 是否存在
 */
export function hasCookie(name) {
  return getCookie(name) !== '';
} 