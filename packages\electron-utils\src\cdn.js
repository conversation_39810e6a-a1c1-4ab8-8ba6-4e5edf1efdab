const fs = require('fs')
const request = require('./request')
const { downloadFile: utilsDownloadFile } = require('./index')

let COS = null
let tokenInfo = null
let cos = null
let currentTaskId = null

const scopes = [
  { bucket: "imgheybox-1251007209", cdnHost: "imgheybox.max-c.com" },
  { bucket: "chat-1251007209", cdnHost: "chat.max-c.com" },
  { bucket: "chat-file-1251007209", cdnHost: "hf.max-c.com" },
  { bucket: "heybox-beijing-1251007209", cdnHost: "heyboxbj.max-c.com" },
  { bucket: "wowxiaoheihe-1251007209", cdnHost: "wow.max-c.com" },
  { bucket: "wow-backup-1251007209", cdnHost: "wowbackup.max-c.com" }
];


function getCustomCDNTokenInfo (type, source, upload_infos) {
  COSAbilityTest()
  // 每次的token不能复用，因此需要重新创建cos
  cos = new COS({
    getAuthorization: (options, callback) => {
      callback({
        TmpSecretId: tokenInfo.credentials.tmpSecretId,
        TmpSecretKey: tokenInfo.credentials.tmpSecretKey,
        XCosSecurityToken: tokenInfo.credentials.sessionToken,
        StartTime: tokenInfo.startTime,
        ExpiredTime: tokenInfo.expiredTime,
      })
    }
  })

  return request.$post(`/chatroom/v2/common/cos/upload/token`, {}, {
    type,
    source,
    upload_infos
  })
}

function getDownloadCDNTokenInfo (url, file_type) {
  // 1.35修改 cdn下载需要使用自定义域名，不能使用cos域名
  let domain = getCdnHost(url)

  cos = new COS({
    Domain: domain, // 自定义加速域名
    ForceSignHost: false, // 防止自定义domain导致签名鉴权错误
    getAuthorization: (options, callback) => {
      callback({
        TmpSecretId: tokenInfo.credentials.tmpSecretId,
        TmpSecretKey: tokenInfo.credentials.tmpSecretKey,
        XCosSecurityToken: tokenInfo.credentials.sessionToken,
        StartTime: tokenInfo.startTime,
        ExpiredTime: tokenInfo.expiredTime,
      })
    }
  })

  return request.$post(`/chatroom/v2/common/cos/download/token`, {}, {
    type: file_type,
    cdn_url: url
  })
}



module.exports.uploadFile = async (filePath, type, scope, uuid, callback) => {
  try {
    let file = fs.statSync(filePath)
    let filename = filePath.split('/').pop()
    let ext = filename.split('.').pop()
    let upload_infos =[{
      ext: ext,
      file_size: file.size/1024/1024,
    }]
    getCustomCDNTokenInfo(type, scope, upload_infos).then((res) => {
      if (res.status === 'ok') {
        let r = res.result
        tokenInfo = r.info.token
  
        cos.putObject({
          Bucket: r.info.bucket,
          Region: 'ap-shanghai',
          Key: r.paths[0].key,
          ACL: scope === 'archives' ? 'private' : 'default',
          ContentDisposition: `attachment;filename=${encodeURIComponent(filename)}`,
          Body: fs.createReadStream(filePath),
          onProgress: (progressData) => { /* 非必须 */
            callback(uuid, 'progress', progressData)
          },
          onTaskReady: (id) => {
            currentTaskId = id
          }
        }, (err, data) => {
          if (!err && data.statusCode === 200) {
            var url = `https://${r.info.host + r.paths[0].key}`
            currentTaskId = null
            callback(uuid, 'success', url)
          } else {
            currentTaskId = null
            callback(uuid, 'error', err.statusCode)
          }
        })
        
      } else {
        callback(uuid, 'error', res.msg)
      }
    }).catch((err) => {
      console.log(err)
      callback(uuid, 'error', 'Network Error')
    })
  } catch (error) {
    callback(uuid, 'error', '错误的文件路径或文件损毁')
  }
}

module.exports.downloadFile = (url, filePath, file_type, uuid, callback) => {
  COSAbilityTest()
  
  if (file_type) {
    getDownloadCDNTokenInfo(url, file_type).then((res) => {
      if (res.status === 'ok') {
        let r = res.result
        if (!r.info) {
          callback('error', '暂无权限')
          return
        }
        tokenInfo = r.info.token
  
        cos.getObject({
          Bucket: r.info.bucket,
          Region: 'ap-shanghai',
          Key: r.info.key,
          Output: fs.createWriteStream(filePath),
          TaskId: uuid,
          onProgress: (progressData) => {
            callback('progress', progressData)
          },
          onTaskReady: (id) => {
            currentTaskId = id
          }
        }, (err, data) => {
          if (!err && data.statusCode === 200) {
            currentTaskId = null
            callback('success', data)
          } else {
            currentTaskId = null
            callback('error', err.statusCode)
          }
        })
      } else {
        callback('error', res.msg)
      }
    }).catch((error) => {
      callback('error', error.message)
    })
  } else {
    utilsDownloadFile(url, filePath, callback)
  }

}

module.exports.cancelCosTask = () => {
  if (currentTaskId && cos) {
    cos.emit('inner-kill-task', {
      TaskId: currentTaskId
    })
    currentTaskId = null
  }
}

function COSAbilityTest ()  {
  if (!COS) {
    COS = require('cos-nodejs-sdk-v5')
  }
}

function getCdnHost(url) {
  const domain = extractDomainFromUrl(url)
  for (const scope of scopes) {
    const { bucket, cdnHost } = scope
    if (domain.startsWith(bucket)) {
      return cdnHost
    } else if (domain === cdnHost) {
      return domain
    }
  }
  return undefined
}


function extractDomainFromUrl(urlString) {
  try {
      const urlObj = new URL(urlString);
      return urlObj.hostname;  // 提取并返回域名部分
  } catch (err) {
      console.error("Invalid URL:", err);
      return null;
  }
}
