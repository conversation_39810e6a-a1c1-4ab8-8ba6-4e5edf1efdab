import { toastError, toastSuccess } from "./toast.js";

export function getBrowserType() {
  if (window && window.navigator.userAgent) {
    const ua = window.navigator.userAgent.toLocaleLowerCase();
    let browserType = "";
    if (ua.match(/msie/) != null || ua.match(/trident/) != null) {
      browserType = "IE";
    } else if (ua.match(/edg|edge/) != null) {
      browserType = "Edge";
    } else if (ua.match(/firefox/) != null) {
      browserType = "firefox";
    } else if (ua.match(/ucbrowser/) != null) {
      browserType = "UC";
    } else if (ua.match(/opera/) != null || ua.match(/opr/) != null) {
      browserType = "opera";
    } else if (ua.match(/baidubrowser/) != null) {
      browserType = "baidu";
    } else if (ua.match(/metasr/) != null) {
      browserType = "sougou";
    } else if (
      ua.match(/tencenttraveler/) != null ||
      ua.match(/qqbrowse/) != null
    ) {
      browserType = "QQ";
    } else if (ua.match(/maxthon/) != null) {
      browserType = "maxthon";
    } else if (ua.match(/lbbrowser/) != null) {
      browserType = "liebao";
    } else if (ua.match(/2345explorer/) != null) {
      browserType = "2345";
    } else if (ua.match(/qihu 360ee/) != null) {
      browserType = "360";
    } else if (ua.match(/chrome/) != null) {
      browserType = "Chrome";
    } else if (ua.match(/safari/) != null) {
      browserType = "Safari";
    }
    return browserType;
  }
}

export function getOSType() {
  if (window && window.navigator.userAgent) {
    const ua = window.navigator.userAgent.toLocaleLowerCase();
    if (/(iphone|ipad|ios)/i.test(ua)) {
      return "iOS";
    } else if (/mac os/i.test(ua)) {
      return "Mac";
    } else if (/(android|harmony)/i.test(ua)) {
      return "Android";
    } else {
      return "Windows";
    }
  }
  return "";
}

export function setCookie(key, val) {
  if (val === undefined) return;
  localStorage.setItem(key, JSON.stringify(val));
}
export function getCookie(key) {
  const jsonStr = localStorage.getItem(key);
  if (!jsonStr) return null;
  try {
    let ck = JSON.parse(jsonStr);
    if (ck) {
      return ck;
    } else {
      const arr = document.cookie.match(
        new RegExp("(^| )" + key + "=([^;]*)(;|$)")
      );
      if (arr != null) {
        return unescape(arr[2]);
      } else {
        return null;
      }
    }
  } catch (e) {
    return null;
  }
}
export function delCookie(key) {
  localStorage.removeItem(key);

  let now = new Date();
  now.setTime(now.getTime() - 100000);
  const cval = getCookie(key);
  if (cval != null) {
    document.cookie =
      key +
      "=;expires=" +
      now.toGMTString() +
      ";path=/;domain=" +
      process.env.BASE_DOMAIN;
  }
}

export function formatDate(timestamp, format) {
  const date = new Date(timestamp);

  const map = {
    YYYY: date.getFullYear(),
    MM: String(date.getMonth() + 1).padStart(2, '0'), // 月份从 0 开始，需要加 1
    DD: String(date.getDate()).padStart(2, '0'),
    HH: String(date.getHours()).padStart(2, '0'),
    mm: String(date.getMinutes()).padStart(2, '0'),
    ss: String(date.getSeconds()).padStart(2, '0'),
    SSS: String(date.getMilliseconds()).padStart(3, '0'), // 毫秒
    d: date.getDay(), // 星期 (0-6, 周日是 0)
  };

  // 替换格式中的标识符
  return format.replace(/YYYY|MM|DD|HH|mm|ss|SSS|d/g, (match) => map[match] || match);
}

export function getUrlParam(key) {
  let url = new URL(window.location.href)
  console.log('getUrlParam', url)
  return url.searchParams.get(key)
}

export function throttle(fn, wait) {
  let timer = null
  return function () {
    let _this = this
    let args = arguments
    if (!timer) {
      timer = setTimeout(function () {
        fn.apply(_this, args)
        timer = null
      }, wait)
    }
  }
}

export function copyToClipboard(text) {
  // 现代浏览器推荐方式
  if (navigator.clipboard) {
    navigator.clipboard.writeText(text)
      .then(() => {
        toastSuccess('复制成功')
      })
      .catch(err => {
        console.error('复制失败:', err);
        toastError('复制失败')
      });
    return;
  }
}


