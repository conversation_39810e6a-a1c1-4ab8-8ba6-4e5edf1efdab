{"name": "@heybox-app-web-shared/components", "version": "1.0.0", "description": "Shared Vue components", "main": "src/index.js", "files": ["src"], "dependencies": {"@heybox-app-web-shared/eventbus": "workspace:*", "@heybox-app-web-shared/font": "workspace:*", "@heybox-app-web-shared/ipc": "workspace:*", "@heybox-app-web-shared/utils": "workspace:*", "@heybox-webapp/hb-theme": "^0.0.3"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "sass": "^1.63.6", "vite": "^4.5.14", "vite-plugin-cdn-import": "^1.0.1"}, "peerDependencies": {"vue": "^3.0.0"}}