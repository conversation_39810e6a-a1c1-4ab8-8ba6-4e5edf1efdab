<template>
  <div :class="['cpt-hb-img', {'img-info-block': img_info && img_info.width}]">
    <img 
      class="preload-img" 
      :src="`data:image/svg+xml;charset=UTF-8,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; width=&quot;${img_info.width}&quot; height=&quot;${img_info.height}&quot; viewBox=&quot;0 0 894 536&quot;><rect opacity=&quot;0&quot; width=&quot;894&quot; height=&quot;536&quot;/></svg>`" 
      alt=""
      v-if="img_info && img_info.width"
    >
    <img
      ref="imgDom"
      :class="[
        'img-dom', 
        `img-fit-${fit}`, 
        {
          'preview': canPreview, 
          'lazy': lazy, 
          'animation': animation,
          'opacity': animation
        }
      ]"
      alt="image"
      @click="openImage"
      @error="$emit('error')"
      @load="handleLoad"
      v-show="!hiddenGif"
      :src="(!lazy && !animation) ? realSrc : PRELOAD_IMG"
    >
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, onActivated, onDeactivated, watch } from 'vue'
import { setImgSrc, formatWebplImg, formatGifFrame, formatThumbnailImg } from './img-lazy-load'
import { useEventBus } from '@heybox-app-web-shared/eventbus'

const EventBus = useEventBus()
const PRELOAD_IMG = 'data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw=='

// Props
const props = defineProps({
  src: { // 图片链接
    type: String,
    default: ''
  },
  lazy: { // 是否开启懒加载
    type: Boolean,
    default: true
  },
  animation: { // 是否展示懒加载动画
    type: Boolean,
    default: true,
  },
  webp_quality: { // 转换webp
    type: Number,
    default: 100
  },
  preview_list: { // 预览列表
    type: Array,
    default: () => []
  },
  fit: { // 图片渲染方式，同object-fit
    type: String,
    default: 'cover'
  },
  img_info: { // 图像信息，存高宽
    type: Object,
    default: () => {}
  },
  thumbnail: { // 缩放操作, 参考https://cloud.tencent.com/document/product/460/36540
    type: String,
    default: ''
  },
  backend_gif_pause: {
    type: Boolean,
    default: true
  },
  gif_active_type: { // 激活gif的类型 ['hover', 'all', 'focus']
    type: String,
    default: 'focus'
  },
  hover_dom_handler: {
    default: null,
  },
  error: {
    default: true,
    type: Boolean,
  }
})

// Emits
const emit = defineEmits(['error', 'load'])

// Refs
const imgDom = ref(null)
const ele = ref(null)
const showViewer = ref(false)
const hiddenGif = ref(false)
const windowShowStateWatcher = ref(null) // 用于存储停止观察函数
const isGitActive = ref(false) // 是否激活gif
const listeners = ref([])

// Computed
const initialPreviewIndex = computed(() => {
  let index = props.preview_list.indexOf(props.img_info?.url)
  if (index !== -1) return index
  
  return props.preview_list.indexOf(props.src)
})

const canPreview = computed(() => {
  return props.preview_list.length > 0 && props.src
})

const realSrc = computed(() => {
  let url = props.src
  if (props.webp_quality > 0 && props.webp_quality < 100) {
    url = formatWebplImg(url, props.webp_quality)
  }
  if (isGif.value && props.gif_active_type !== 'all' && !isGitActive.value) {
    url = formatGifFrame(url)
  }
  if (props.thumbnail) {
    url = formatThumbnailImg(url, props.thumbnail)
  }
  return url
})

const isGif = computed(() => {
  return /\.gif$/i.test(props.src)
})

// Methods
const openViewer = () => {
  EventBus.emit('openImgPreviewer', props.preview_list, initialPreviewIndex.value)
}

const handleImg = () => {
  if (!props.lazy && !props.animation) {
    return
  }
  let eleSrc = ele.value.getAttribute('src')
  if (props.lazy && (!eleSrc || eleSrc === PRELOAD_IMG)) {
    window.hcImg.observer.observe(ele.value)
  } else {
    setImgSrc(ele.value)
  }
}

const openImage = () => {
  if (canPreview.value) {
    if (props.preview_list.length == 0) {
      props.preview_list = [props.src]
    }
    openViewer()
  }
}

const initGifListener = () => {
  releaseGifListener()
  if (props.gif_active_type === 'hover') {
    listeners.value = [{
      dom: (props.hover_dom_handler && props.hover_dom_handler()) || imgDom.value,
      event: 'mouseenter',
      func: () => {
        changeGifState(true)
      }
    }, {
      dom: (props.hover_dom_handler && props.hover_dom_handler()) || imgDom.value,
      event: 'mouseleave',
      func: () => {
        changeGifState(false)
      }
    }]
  } else if (props.gif_active_type === 'focus') {
    listeners.value = [{
      dom: window,
      event: 'focus',
      func: () => {
        changeGifState(true)
      }
    }, {
      dom: window,
      event: 'blur',
      func: () => {
        changeGifState(false)
      }
    }]
    if (document.hasFocus()) {
      changeGifState(true)
    }
  }
  listeners.value?.forEach(({ dom, event, func }) => {
    dom.addEventListener(event, func)
  })
}

const releaseGifListener = () => {
  listeners.value?.forEach(({ dom, event, func }) => {
    dom.removeEventListener(event, func)
  })
}

const changeGifState = (bl) => {
  isGitActive.value = bl
  ele.value.setAttribute('data-original', realSrc.value)
  setImgSrc(ele.value)
}

const handleLoad = (event) => {
  emit('load', event)
}

const init = () => {
  ele.value.setAttribute('data-original', realSrc.value)
  ele.value.setAttribute('data-allow-error', props.error ? 1 : 0)
  handleImg()
  if (isGif.value) {
    initGifListener()
  }
}

// Lifecycle hooks
onMounted(() => {
  ele.value = imgDom.value
  init()
})

onBeforeUnmount(() => {
  releaseGifListener()
})

onActivated(() => {
  initGifListener()
})

onDeactivated(() => {
  changeGifState(false)
  releaseGifListener()
})

// Watchers
watch(realSrc, () => {
  init()
})
</script>

<style lang="scss">
.cpt-hb-img {
  height: 100%;
  width: 100%;
  border-radius: inherit;
  &.img-info-block {
    position: relative;
    .img-dom {
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
    }
    .preload-img {
      width: 100%;
      height: 100%;
    }
  }
  .img-dom {
    display: block;
    object-fit: cover;
    border-radius: inherit;
    pointer-events: auto;
    max-width: 100%;
    max-height: 100%;
    width: 100%;
    height: 100%;
    &.opacity {
      opacity: 0;
    }
    &.animation {
      transition: opacity .5s cubic-bezier(0.25, 0.1, 0.25, 1);
    }
    &.preview {
      cursor: zoom-in;
    }
    &.img-fit {
      &-cover {
        object-fit: cover;
      }
      &-contain {
        object-fit: contain;
      }
      &-fill {
        object-fit: fill;
      }
      &-scale-down {
        object-fit: scale-down;
      }
      &-none {
        object-fit: none;
      }
    }
    &[data-error] {
      object-fit: cover;
    }
  }
}
</style>