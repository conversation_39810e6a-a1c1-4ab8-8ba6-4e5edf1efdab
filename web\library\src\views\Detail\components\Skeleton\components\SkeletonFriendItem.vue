<template>
  <div class="skeleton-friend-item">
    <div class="skeleton-friend-avatar"></div>
    <div class="skeleton-friend-name"></div>
  </div>
</template>

<script setup name="SkeletonFriendItem">
// 朋友列表骨架屏项目组件
</script>

<style lang="scss" scoped>
.skeleton-friend-item {
  display: flex;
  align-items: center;
  gap: 12px;

  .skeleton-friend-avatar {
    width: 34px;
    height: 34px;
    background: var(---general-color-bg-0, #F1F2F3);
    border-radius: 50%;
    flex-shrink: 0;
  }

  .skeleton-friend-name {
    width: 152px;
    height: 12px;
    background: var(---general-color-bg-0, #F1F2F3);
    border-radius: 6px;
  }
}
</style>
