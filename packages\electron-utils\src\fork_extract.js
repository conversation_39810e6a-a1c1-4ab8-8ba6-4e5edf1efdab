const AdmZip = require('adm-zip');
const fs = require('fs');

process.on('message', ({ zipPath = '', extractPath = '', config = {} }) => {
  let { removeZipAfterExtract = true, removeZipWhenError = true } = config;
  try {
    let zip = new AdmZip(zipPath);
    zip.extractAllTo(extractPath, true);

    if (removeZipAfterExtract) {
      // 删除zip文件
      fs.rmSync(zipPath, {
        recursive: true,
        force: true,
      });
    }

    // 成功解压
    process.send({ status: 'success' });
    process.exit(0);
  } catch (error) {
    if (removeZipWhenError) {
      // 发生错误
      fs.rmSync(zipPath, {
        recursive: true,
        force: true,
      });
    }
    process.send({ status: 'error', message: error.message });
    process.exit(0);
  }
});
