<template>
  <div class="cpt-bottom-action-bar" :class="layoutMode">
    <div class="bottom-left-side">
      <ActionsButton
        v-if="tools.length > 0"
        :text="leftButtonText"
        :showText="showLeftButtonText"
        :layoutMode="layoutMode"
        @clickButton="handleToolsClick"
        ><template #icon>
          <div class="single-img">
            <img
              src="https://imgheybox.max-c.com/dev/bbs/2025/07/01/********************************.png"
              alt=""
            />
          </div> </template
        ><template #desc v-if="showLeftButtonDesc">
          <span class="button-desc">{{ open_tools_num }} 项已开启</span>
        </template>
      </ActionsButton>
    </div>
    <div class="bottom-right-side">
      <ActionsButton
        v-if="boosterInfo"
        :options="accelerateOptions"
        :text="accelerateButtonText"
        :showText="showAccelerateButtonText"
        :layoutMode="layoutMode"
        class="accelerate-button"
        :class="{ 'accelerating': is_acc_start && !is_acc_other_game }"
        @clickOptions="handleInitAcc"
        @clickButton="handleStartAcc"
      >
        <template #icon>
          <div v-if="(!is_acc_start && !(acc_loading || is_acc_initing)) || is_acc_other_game" class="single-img">
            <img
              src="https://imgheybox.max-c.com/dev/bbs/2025/07/01/********************************.png"
              alt=""
            />
          </div>
          <div v-else-if="!is_acc_start && (acc_loading || is_acc_initing)">
            <div class="circle-loading">
              <div class="loading-item"></div>
            </div>
          </div>
          <div v-else class="stop-icon">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M10.0005 1.53906C14.6734 1.53933 18.4614 5.32796 18.4614 10.001C18.4614 14.674 14.6734 18.4626 10.0005 18.4629C5.32731 18.4629 1.53857 14.6742 1.53857 10.001C1.53857 5.3278 5.32731 1.53906 10.0005 1.53906ZM10.0005 3.84668C6.60181 3.84668 3.84619 6.6023 3.84619 10.001C3.84619 13.3997 6.60181 16.1553 10.0005 16.1553C13.3989 16.155 16.1538 13.3995 16.1538 10.001C16.1538 6.60246 13.3989 3.84694 10.0005 3.84668ZM11.5386 6.92383C12.3882 6.92389 13.0767 7.61326 13.0767 8.46289V11.5391C13.0767 12.3887 12.3882 13.0781 11.5386 13.0781H8.46143C7.61181 13.0781 6.92334 12.3887 6.92334 11.5391V8.46289C6.92334 7.61326 7.61181 6.92389 8.46143 6.92383H11.5386Z" fill="white"/>
            </svg>
          </div>
        </template>
        <template #desc v-if="showAccelerateButtonDesc">
          <span
            class="button-desc accelerate-desc"
            :style="acc_desc_color"
            >{{ boosterInfo.rtt_info || '日本 34ms' }}</span
          >
        </template>
      </ActionsButton>
      <ActionsButton
        :text="launchButtonConfig.text"
        :speed="launchButtonConfig.speed"
        :showText="true"
        :options="launchButtonConfig.showOptions ? ['account-switch'] : null"
        :theme="launchButtonConfig.theme"
        :accounts="accounts"
        layoutMode="wide"
        class="launch-button"
        @selectAccount="handleSelectAccount"
        @addLocalFile="handleAddLocalFile"
        @removeLocalFile="handleRemoveLocalFile"
        @clickButton="handleLaunchGame"
        ><template #icon>
          <div class="multiple-img">
            <AccountCardAvatar
              :width="22"
              :avatar="currentAccountAvatar"
              decoration="https://imgheybox.max-c.com/dev/bbs/2025/06/04/********************************.png"
              :showDecoration="true"
            />
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M15.1696 8.00078C15.1696 11.9772 11.946 15.2008 7.9696 15.2008C4.65161 15.2008 1.85781 12.9564 1.02355 9.90301L3.82413 11.1277C4.01894 12.0169 4.78794 12.6808 5.70691 12.6808C6.7678 12.6808 7.62882 11.796 7.6373 10.7012L10.1469 8.8266L10.1669 8.82668C11.6375 8.82668 12.8296 7.59414 12.8296 6.07374C12.8296 4.55333 11.6375 3.32079 10.1669 3.32079C8.69635 3.32079 7.50423 4.55333 7.50423 6.07374C7.50423 6.10524 7.50474 6.13662 7.50575 6.16786L5.83094 8.69308C5.78993 8.69039 5.74858 8.68903 5.70691 8.68903C5.33541 8.68903 4.98842 8.79752 4.69397 8.98554L0.800049 7.33414C1.13642 3.67021 4.21791 0.800781 7.9696 0.800781C11.946 0.800781 15.1696 4.02433 15.1696 8.00078ZM11.8976 6.07369C11.8976 7.06196 11.1227 7.86311 10.1669 7.86311C9.21099 7.86311 8.43611 7.06196 8.43611 6.07369C8.43611 5.08543 9.21099 4.28428 10.1669 4.28428C11.1227 4.28428 11.8976 5.08543 11.8976 6.07369ZM5.70694 12.2679C6.55251 12.2679 7.23798 11.5592 7.23798 10.6849C7.23798 9.8107 6.55251 9.10199 5.70694 9.10199C5.52485 9.10199 5.35019 9.13486 5.1882 9.19516L6.14673 9.60167C6.55259 9.78304 6.83858 10.1995 6.83858 10.6849C6.83858 11.3311 6.33192 11.8549 5.70694 11.8549C5.60301 11.8549 5.50236 11.8405 5.40677 11.8133L5.35598 11.7976C5.29487 11.777 5.23605 11.7512 5.18001 11.7206L4.31348 11.3417C4.5548 11.8881 5.08798 12.2679 5.70694 12.2679ZM8.83561 6.07374C8.83561 6.83394 9.43167 7.45021 10.167 7.45021C10.9022 7.45021 11.4983 6.83394 11.4983 6.07374C11.4983 5.31353 10.9022 4.69727 10.167 4.69727C9.43167 4.69727 8.83561 5.31353 8.83561 6.07374Z" fill="white"/>
              </svg>
          </div>
        </template>
      </ActionsButton>
    </div>
  </div>
</template>

<script setup name="BottomActionBar">
import ActionsButton from './components/ActionsButton.vue';
import AccountCardAvatar from '@/components/func/Avatar.vue';
import { defineProps, defineEmits, ref, onMounted, onUnmounted, computed, watch } from 'vue';
import { useStore } from 'vuex';
import { useToast } from 'vue-toastification';
import { storeGetter, storeSetter } from '@heybox-app-web-shared/utils';
import SteamManager from '@/assets/js/steam-manager.js';
import { ipcService } from '@heybox-app-web-shared/ipc';
const [
  last_acc_settings,
  is_acc_start,
  acc_loading,
  is_acc_initing,
  acc_game_infos,
  current_acc_app,
  current_acc_id,
] = storeGetter([
  'last_acc_settings',
  'is_acc_start',
  'acc_loading',
  'is_acc_initing',
  'acc_game_infos',
  'current_acc_app',
  'current_acc_id',
], 'acc_config')

const store = useStore();
const toast = useToast();

const emit = defineEmits(['gameStatusChanged']);

const { tools, boosterInfo, gameStatus, accounts } = defineProps({
  tools: {
    type: Array,
    default: () => [],
  },
  boosterInfo: {
    type: Object,
    default: () => null,
  },
  gameStatus: {
    type: Object,
    default: () => ({
      status: 'ready', // ready, installing, updating, running, toSteam
      progress: 0,
      speed: '',
    }),
  },
  accounts: {
    type: Array,
    default: () => [],
  },
});
console.log(tools.value)

const miniprogram_setting = window.electronAPI.getStoreData('', 'miniprogram_setting')
const open_tools_num = ref(0)

window.electronAPI.onStoreChange('', (v) => {
  console.log('miniprogram_setting', v)
  miniprogram_setting = v
  calculateOpenToolsNum()
}, 'miniprogram_setting')

const acc_desc_color = computed(() => {
  if(is_acc_start.value && !is_acc_other_game.value) {
    return { color: 'rgba(255, 255, 255, 0.8)' }
  }
  return { color: 'var(---general-color-success, #32B846)' }
})

const is_acc_other_game = computed(() => {
  return current_acc_app.value != store.state.current_game && is_acc_start.value
})

const containerWidth = ref(0);
const leftSideWidth = ref(280); // 默认左侧边栏宽度
const selectedAccount = ref(null);

const currentAccountAvatar = computed(() => {
  if (selectedAccount.value && selectedAccount.value.avatar) {
    return selectedAccount.value.avatar;
  }
});

watch(() => accounts, (newAccounts) => {
  if (newAccounts && newAccounts.length > 0 && !selectedAccount.value) {
    const currentSteamId = store.state.current_account;
    const currentAccount = newAccounts.find(acc => acc.account_id === currentSteamId);
    selectedAccount.value = currentAccount || newAccounts[0];
  }
}, { immediate: true });

const layoutMode = computed(() => {
  if (containerWidth.value >= 760) return 'wide';
  if (containerWidth.value >= 580) return 'medium';
  return 'narrow';
});

// 左侧按钮配置
const leftButtonText = computed(() => {
  if (layoutMode.value === 'narrow') return '';
  return '关联工具';
});

const showLeftButtonText = computed(() => layoutMode.value !== 'narrow');
const showLeftButtonDesc = computed(() => layoutMode.value !== 'narrow');

// 加速按钮配置
const accelerateButtonText = computed(() => {
  if (layoutMode.value === 'narrow') return '';
  if(is_acc_start.value && !is_acc_other_game.value) {
    return '停止加速'
  } else if(acc_loading.value) {
    return '加速中'
  } else if(is_acc_initing.value) {
    return '正在启动加速器'
  } else {
    return '加速并启动'
  }
});

const showAccelerateButtonText = computed(() => layoutMode.value !== 'narrow');
const showAccelerateButtonDesc = computed(() => layoutMode.value !== 'narrow' && (is_acc_start.value || !acc_loading.value && !is_acc_initing.value));

const accelerateOptions = computed(() => {
  return [];
});

// 启动按钮配置
const launchButtonConfig = computed(() => {
  const { status, progress, speed } = gameStatus || { status: 'ready', progress: 0, speed: '' };

  switch (status) {
    case 'installing':
      return {
        text: `安装中 ${progress}%`,
        speed: speed,
        theme: 'success',
        showOptions: false,
        icon: 'loading'
      };
    case 'updating':
      return {
        text: `更新中 ${progress}%`,
        speed: speed,
        theme: 'success',
        showOptions: false,
        icon: 'loading'
      };
    case 'running':
      return {
        text: '停止运行',
        theme: 'info',
        showOptions: false,
        icon: 'stop'
      };
    case 'toSteam':
      return {
        text: '前往 Steam',
        theme: 'steam',
        showOptions: true,
        icon: 'steam'
      };
    default: // ready
      return {
        text: '启动游戏',
        theme: 'primary',
        showOptions: true,
        icon: 'play'
      };
  }
});

const handleToolsClick = () => {
  if (window.popupManagerAPI) {

    window.popupManagerAPI.show({
      type: 'Dialog',
      cptName: 'ToolsDialog',
      props: {
        tools: JSON.parse(JSON.stringify(tools))
      }
    });
  } else {
    console.error('popupManagerAPI is not available');
  }
};

const calculateOpenToolsNum = () => {
  let num = 0
  console.log(tools)
  tools.forEach(tool => {
    console.log(tool.mini_pro_id, miniprogram_setting)
    let setting = miniprogram_setting.find(item => item.mini_pro_id === tool.mini_pro_id)
    if(setting && setting.enable) {
      num++
    }
  })
  open_tools_num.value = num
}

const handleSelectAccount = async (account) => {
  console.log('Selected account:', account);
  selectedAccount.value = account;

  const steamAccount = store.state.steam_accounts.find(acc => acc.steam_id === account.account_id);

  if (steamAccount) {
    const accountData = {
      steam_id: steamAccount.steam_id,
      account_name: steamAccount.account_name,
    };
    await SteamManager.setCurrentAccount(accountData);
  } else {
    console.warn('Steam account not found in store for account_id:', account.account_id);
  }
};

const handleLaunchGame = async () => {
  const currentGame = store.state.current_game;
  if (!currentGame) {
    console.warn('No current_game available for launch');
    return;
  }

  const { status } = gameStatus || { status: 'ready' };

  try {
    if (status === 'running') {
      console.log('[BottomActionBar] Stopping game:', currentGame);
      const result = await window.steamGames.terminateGame(parseInt(currentGame));

      if (result.success) {
        gameStatus.status = 'ready';
        emit('gameStatusChanged');
      } else {
        console.error('[BottomActionBar] Failed to stop game:', result.error);
      }
    } else {
      console.log('[BottomActionBar] Starting game:', currentGame);

      // 检查是否有本地 exe 文件
      const localExeFile = window.electronAPI.getStoreData(`local_exe_${currentGame}`, 'library_config');

      if (localExeFile && localExeFile.path) {
        // 启动本地 exe 文件
        console.log('[BottomActionBar] Starting local exe:', localExeFile.path);
        try {
          const result = await window.electronAPI.invoke('launch-local-exe', localExeFile.path);
          if (result.success) {
            console.log('[BottomActionBar] Local exe launched successfully');
            setTimeout(() => {
              emit('gameStatusChanged');
            });
          } else {
            console.error('[BottomActionBar] Failed to launch local exe:', result.error);
          }
        } catch (error) {
          console.error('[BottomActionBar] Error launching local exe:', error);
        }
      } else {
        // 使用原有的启动逻辑
        let launchSchema = '';

        if (boosterInfo && boosterInfo.acc_game_infos && boosterInfo.acc_game_infos.length > 0) {
          const firstGameInfo = boosterInfo.acc_game_infos[0];
          launchSchema = firstGameInfo.launch_schema;
        } else {
          launchSchema = `steam://rungameid/${currentGame}`;
        }

        if (launchSchema) {
          window.accAPI.startGame(launchSchema);
          setTimeout(() => {
            emit('gameStatusChanged');
          });
        }
      }
    }
  } catch (error) {
    console.error('[BottomActionBar] Game operation failed:', error);
  }
};

const handleAddLocalFile = (file) => {
  console.log('Selected local file:', file);
};

const handleRemoveLocalFile = () => {
  console.log('Removed local file');
};

const updateContainerWidth = () => {
  const container = document.querySelector('.cpt-bottom-action-bar');
  if (container) {
    containerWidth.value = container.offsetWidth;
  }
};

const updateLeftSideWidth = () => {
  const leftSide = document.querySelector('.layout-main .left-side');
  if (leftSide) {
    leftSideWidth.value = leftSide.offsetWidth;
    updateBottomBarPosition();
  }
};

const updateBottomBarPosition = () => {
  const bottomBar = document.querySelector('.cpt-bottom-action-bar');
  if (bottomBar) {
    const leftOffset = leftSideWidth.value + 2; // 左侧边栏宽度 + 分割线宽度
    bottomBar.style.left = `${leftOffset}px`;
    bottomBar.style.width = `calc(100% - ${leftOffset}px)`;
  }
};

onMounted(() => {
  updateContainerWidth();
  updateLeftSideWidth();
  calculateOpenToolsNum();
  window.addEventListener('resize', updateContainerWidth);

  // 监听左侧边栏宽度变化
  const observer = new MutationObserver(() => {
    updateLeftSideWidth();
  });

  const leftSide = document.querySelector('.layout-main .left-side');
  if (leftSide) {
    observer.observe(leftSide, {
      attributes: true,
      attributeFilter: ['style']
    });
  }

  window._bottomBarObserver = observer;
});

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerWidth);
  if (window._bottomBarObserver) {
    window._bottomBarObserver.disconnect();
    delete window._bottomBarObserver;
  }
});

const handleStartAcc = () => {
  if(last_acc_settings.value) {
    // 如果上次加速过，则根据上次的加速选择直接启动
    let last_acc_setting = last_acc_settings.value[store.state.current_game]
    handleInitAcc(last_acc_setting)
  } else {
    // 如果上次没有加速过，则弹窗选择加速节点
    handleInitAcc()
  }
}

const handleInitAcc = async (last_acc_setting = null) => {
  if(acc_loading.value) return

  if(is_acc_other_game.value) {
    let current_game = store.state.steam_games.find(game => game.appid == current_acc_app.value)
    console.log(store.state.steam_games, store.state.current_game,current_acc_app.value)
    const config = {
      title: `加速游戏${current_game.name}中`,
      desc: '当前已有游戏进行加速，这将断开其连接',
      disableClose: true,
      confirm: {
        text: '确定',
      },
      cancel: {
        text: '取消'
      },
    }
    const dialogId = 'AccOtherGameTip'
    window.popupManagerAPI.show({
      type: 'Dialog',
      cptName: 'QueryDialog',
      props: {
        config,
        id: dialogId,
        target: 'library',
      }
    })
    ipcService.onMessage(`query-dialog-${dialogId}-response`, async ({type, data}) => {
      if(type === 'confirm') {
        await window.accAPI.stopAcc({acc_id: current_acc_id.value})
        window.webViewAPI.postMessage('main', 'initAcc', {
          last_acc_setting: JSON.parse(JSON.stringify(last_acc_setting)),
          appid: String(store.state.current_game),
          acc_game_infos: JSON.parse(JSON.stringify(boosterInfo.acc_game_infos))
        })
      }
    })
  } else {
    window.webViewAPI.postMessage('main', 'initAcc', {
      last_acc_setting: JSON.parse(JSON.stringify(last_acc_setting)),
      appid: String(store.state.current_game),
      acc_game_infos: JSON.parse(JSON.stringify(boosterInfo.acc_game_infos))
    })
  }
}

</script>

<style lang="scss">
.cpt-bottom-action-bar {
  display: flex;
  width: calc(100% - 282px);
  height: 70px;
  padding: 12px 24px;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
  border-top: 1px solid var(---general-color-stroke-1, #f1f2f3);
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 282px;
  right: 0;
  z-index: 100;
  .single-img {
    width: 22px;
    height: 22px;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .stop-icon {
    width: 20px;
    height: 20px;
  }
    .circle-loading {
    width: 20px;
    height: 20px;
    border: 2px solid $general-color-bg-6;
    position: relative;
    border-radius: 50%;

    .loading-item {
      border: 2px solid transparent;
      border-left: 2px solid #7dd95e;
      position: absolute;
      top: -2px;
      left: -2px;
      bottom: -2px;
      right: -2px;
      border-radius: 50%;
      animation: circle-loading-rotate 1s linear infinite;
    }

    @keyframes circle-loading-rotate {
      to {
        transform: rotate(1turn);
      }
    }
  }
  .multiple-img {
    display: flex;
    align-items: center;
    gap: 6px;
    .platform-icon {
      width: 16px;
      height: 16px;
      object-fit: cover;
    }
  }
  .button-desc {
    white-space: nowrap;
    font-size: 12px;
    font-weight: 400;
    line-height: normal;
    color: var(---general-color-text-3, #8c9196);

    &.accelerate-desc {
      font-weight: 500;
    }
  }
  .bottom-right-side {
    display: flex;
    align-items: center;
    gap: 10px;
    .launch-button {
      width: 234px;

      &.success {
        width: 204px;
      }
      &.info {
        width: 200px;
      }
    }
    .accelerate-button {
      .actions-button-arrow {
        svg {
          path {
            fill: #8C9196;
          }
        }
      }
    }
    .accelerating {
      background-color: $general-color-danger;
      .actions-button-text {
        color: #FFF;
      }
      .accelerate-desc {
        color: rgba(255, 255, 255, 0.8);
      }
      .actions-split-line {
        background-color: $general-color-stroke-0;
      }
      .actions-button-arrow {
        svg {
          path {
            fill: #FFF !important;
          }
        }
      }
    }
  }

  &.narrow {
    .bottom-right-side {
      .launch-button {
        width: 264px;
      }
    }
  }
}
.cpt-acc-popover {
  width: 640px;
  height: 560px;
  background-color: #fff;
  border-radius: 12px;
  border: 1px solid $general-color-stroke-0;
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.10);
  overflow: hidden;
}
</style>
