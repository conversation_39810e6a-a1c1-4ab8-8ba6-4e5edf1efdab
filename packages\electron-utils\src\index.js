const fs = require("fs")
const path = require('path')
const requireAddon = require('./require_addon')
const { net, app, webContents } = require("electron")
const log = require('./log')
const eventBus = require('./eventBus')
const store = require('./store')
const request = require('./request')

let requestInstance = null

function getParams (url) {
  var url = url;
  if (url.indexOf('?') < 0) {
    return false
  }
  return url.match(/([^?=&]+)(=([^&]*))/g).reduce(function (a, v) {
    return (a[v.slice(0, v.indexOf("="))] = v.slice(v.indexOf("=") + 1)), a;
  }, {});
}
// 拿到url中的具体某个参数的值
function getUrlParam (key, url) {
  var url = url;
  var params = getParams(url);
  return params[key] ? params[key] : '';
}

//ArrayBuffer转Buffer
function toBuffer(ab) {
  var buf = new Buffer(ab.byteLength);
  var view = new Uint8Array(ab);
  for (var i = 0; i < buf.length; ++i) {
    buf[i] = view[i];
  }
  return buf;
}
async function downloadFile(url, filePath, callback) {
  // 创建写入流用于将文件写入磁盘
  const writer = fs.createWriteStream(filePath);
  callback('start')

  let rate = 0
  let lastData = 0
  let receiveData = 0
  let progressTimer = null
  let isFinish = true
  let intervalTime = 500

  requestInstance = net.request(url);
  requestInstance.on('response', (res) => {
    if (res.statusCode >= 400) {
      isFinish = false
      callback('error', res.statusCode)
      writer.close()
      return
    }
    progressTimer = setInterval(() => {
      rate = (receiveData - lastData) / (intervalTime / 1000)
      lastData = receiveData
      callback('progress', {
        rate: rate,
        progress: receiveData / parseInt(res.headers['content-length']),
        loaded: receiveData,
      })
    }, intervalTime)
    res.on('data', (chunk) => {
      receiveData += chunk.length
      writer.write(chunk)
    })
    res.on('end', () => {
      clearInterval(progressTimer)
      writer.end()
    })
    res.on('error', (error) => {
      callback('error', error)
      clearInterval(progressTimer)
      console.error(`文件写入时出现错误：${error}`);
    })
  });

  requestInstance.on('error', (error) => {
    clearInterval(progressTimer)
    console.error(`error: ${error}`);
    requestInstance = null
    writer.destroy()
    callback('error', error)
  });
  writer.on('finish', () => {
    clearInterval(progressTimer)
    if (isFinish) {
      callback('success', filePath)
    }
    requestInstance = null
  });
  writer.on('error', (error) => {
    clearInterval(progressTimer)
    callback('error', error)
    console.error(`文件写入时出现错误：${error}`);
    requestInstance.abort()
    requestInstance = null
  });

  requestInstance.end()
}
async function checkFileExists(path) {
  try {
    const fs = require('fs').promises;
    await fs.access(path, fs.constants.F_OK);
    return true;
  } catch (err) {
    return false;
  }
}
function compareVersions(version1, version2) {
  const arr1 = version1.split('.').map(Number);
  const arr2 = version2.split('.').map(Number);

  for (let i = 0; i < Math.max(arr1.length, arr2.length); i++) {
    const num1 = arr1[i] || 0;
    const num2 = arr2[i] || 0;

    if (num1 < num2) {
      return -1; // version1 < version2
    } else if (num1 > num2) {
      return 1; // version1 > version2
    }
  }

  return 0; // version1 = version2
}
function electronVersionAfter30() {
  return compareVersions(global.ELECTRON_VERSION, '30.0.0') >= 0
}
// 检查resource文件是否有文件新增/删除权限
function authorityCheck(checkFile) {
  return new Promise((resolve, reject) => {
    fs.writeFile(checkFile, 'authorityCheck', "utf8", (err) => {
      if (err) {
        resolve({
          add: false,
        })
      } else {
        fs.unlink(checkFile, (error) => {
          if (error) {
            resolve({
              add: true,
              delete: false,
            })
          } else {
            resolve({
              add: true,
              delete: true,
            })
          }
        });
      }
    })
  })
}
// 窗口置顶显示
function focusWindow (win) {
  if (!win) return
  win.setAlwaysOnTop(true, 'pop-up-menu')
  win.focus()
  win.show()
  win.setAlwaysOnTop(false)
}
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
function getMemoryUsage () {
  let metrics = app.getAppMetrics()
  let renderProcessInfo = {}
  webContents.getAllWebContents().forEach((webContent) => {
    let url = webContent.getURL()
    let fileMatches = url.match(/([a-zA-Z0-9\s_-]+?)\/[^\/]+\.html/)
    let key
    if (fileMatches && fileMatches[1]) {
      key = fileMatches[1]
    } else {
      key = 'unknown'
    }
    renderProcessInfo[webContent.getOSProcessId()] = key
  })
  let usages = {}
  let privateBytes = metrics.reduce((p, c) => {
    if (['Tab', 'GPU', 'Browser'].includes(c.type)) {
      if (c.type === 'Tab') {
        usages[`${c.type}_${renderProcessInfo[c.pid]}`] = (c.memory.privateBytes / 1024).toFixed(2)
      } else {
        usages[c.type] = (c.memory.privateBytes / 1024).toFixed(2)
      }
    }
    return p + c.memory.privateBytes
  }, 0) / 1024
  let cpuUsage = metrics.reduce((p, c) => {
    return p + c.cpu.percentCPUUsage
  }, 0)
  usages['size'] = privateBytes.toFixed(2)
  usages['cpu'] = cpuUsage.toFixed(1) + '%'

  return usages
}
// 截取跑马灯窗口title的数据
function rearrangeString(str1, str2) {
  let doubleStr1 = str1 + str1;
  let pos = doubleStr1.indexOf(str2);
  if (pos === -1) {
      return str1;
  }
  let startPos = pos + str2.length;
  let rearranged = doubleStr1.substring(startPos, startPos + str1.length - str2.length);
  return rearranged;
}
function base64ToBuffer(base64) {
  const binString = atob(base64);
  return Uint8Array.from(binString, (m) => m.codePointAt(0));
}
function throttle(fn, wait) {
  let timer = null;
  return function () {
    let _this = this;
    let args = arguments;
    if (!timer) {
      timer = setTimeout(function () {
        fn.apply(_this, args);
        timer = null;
      }, wait);
    }
  };
}
function debounce(func, wait) {
  let timer;
  return function () {
    let _this = this;
    let args = arguments;
    clearTimeout(timer);
    timer = setTimeout(function () {
      func.apply(_this, args);
    }, wait);
  };
}
function getRegeditKey(path, key) {
  const regedit = require('./regedit')
  return new Promise((resolve, reject) => {
    try {
      resolve(regedit.getKey(path, key))
    } catch (error) {
      reject(error.message)
    }
  })
}
function getCurFocusWindow() {
  const addon = require('@heybox/node-inject').addon
  const cur_focus_window = addon.getForegroundWindow()
  cur_focus_window.process_name = cur_focus_window.processName?.split('\\').slice(-1)[0]
  return cur_focus_window
}
function getStoreCookies() {
  const { PKEY_ENCRYPT_KEY } = require('./assets/constant')
  const { decrypt } = require('./node_encrypt')
  let user_cookies = store.get('cookies') || []
  return user_cookies.map(cookie => {
    if (isPkey(cookie.name)) { // pkey 解密
      cookie.value = decrypt(cookie.value, PKEY_ENCRYPT_KEY)
    }
    return cookie
  })
}
function clearStoreCookies() {
  store.set('cookies', [])
}
function isPkey(name) {
  return name === 'pkey' || name === 'user_pkey'
}
function isHeyboxId(name) {
  return name === 'heybox_id' || name === 'user_heybox_id'
}
// 新建子进程执行解压，防止同步解压导致卡死
function unzipInChildProcess(zipPath, extractPath, config) {
  return new Promise((resolve, reject) => {
    const { fork } = require('child_process');
    const extractProcess = fork(path.join(__dirname, './fork_extract.js'), [], {
      stdio: 'inherit'
    });

    extractProcess.send({ zipPath, extractPath, config });

    extractProcess.on('message', (message) => {
      log.info('message', message);
      if (message.status === 'success') {
        resolve();  // 解压成功
      } else if (message.status === 'error') {
        reject(new Error(message.message));  // 解压失败
      }
    });

    extractProcess.on('error', (error) => {
      reject(error);  // 进程启动失败
    });
    extractProcess.on('close', (code) => {
      setTimeout(() => { // setTimeout 防止触发时机过早
        reject({message: `process close ${code}`});
      })
    });
    extractProcess.on('exit', (code) => {
      setTimeout(() => { // setTimeout 防止触发时机过早
        reject({message: `process exit ${code}`});
      })
    });
  });
}
function createObservableArray(initialArray, lengthChangeCallback) {
  return new Proxy(initialArray, {
    set(target, property, value, receiver) {
      // 拦截对 'length' 属性的设置
      if (property === 'length') {
        lengthChangeCallback(value);
      }
      // 拦截数组元素的设置，可能会影响长度
      const oldLength = target.length;
      const result = Reflect.set(target, property, value, receiver);
      if (property !== 'length') {
        const newLength = target.length;
        if (newLength !== oldLength) {
          lengthChangeCallback(newLength);
        }
      }
      return result;
    },
    get(target, property, receiver) {
      const original = Reflect.get(target, property, receiver);
      // 如果属性是函数（如 push, pop 等），需要进行包装
      if (typeof original === 'function') {
        return function (...args) {
          const oldLength = target.length;
          const result = original.apply(this, args);
          const newLength = target.length;
          if (newLength !== oldLength) {
            lengthChangeCallback(newLength);
          }
          return result;
        };
      }
      return original;
    }
  });
}

function getGpuCompositingEnable() {
  return app.getGPUFeatureStatus().gpu_compositing === "enabled"
}

function getUseSharedTexture() {
  if (!electronVersionAfter30()) {
    log.info('[getUseSharedTexture] electron version is less than 30.0.0')
    return false
  }
  const gpu_compositing = getGpuCompositingEnable()
  const use_shared_texture = store.get('global_config.switches.use_shared_texture')
  log.info('[getUseSharedTexture]', gpu_compositing, use_shared_texture)
  return gpu_compositing && use_shared_texture
}

function getAudioDuration(filePath) {
  return new Promise((resolve, reject) => {
    try {
      const endPointAddOn = requireAddon('get_endpoint_info')
      endPointAddOn.getAudioDuration(filePath, (info) => {
        resolve(info.duration_second)
      })
    } catch (error) {
      reject(error)
    }
  })
}

// 合并音频
function concatAudio({ audioFiles, outputPath }) {
  return new Promise((resolve, reject) => {
    const { fork } = require('child_process');
    const concatProcess = fork(path.join(__dirname, './audio_concat.js'), [], {
      stdio: 'inherit'
    });

    concatProcess.send({ audioFiles, outputPath });

    concatProcess.on('message', (message) => {
      log.info('[audio concat]', message);
      if (message.status === 'success') {
        resolve(message);
      } else if (message.status === 'error') {
        reject(new Error(message.message));
      }
    });

    concatProcess.on('error', (error) => {
      reject(error);  // 进程启动失败
    });
    concatProcess.on('close', (code) => {
      setTimeout(() => { // setTimeout 防止触发时机过早
        reject({message: `process close ${code}`});
      })
    });
    concatProcess.on('exit', (code) => {
      setTimeout(() => { // setTimeout 防止触发时机过早
        reject({message: `process exit ${code}`});
      })
    });
  });
}

// 异步遍历dirPath文件夹中的所有子文件
async function readDirRecursive(dirPath) {
  return new Promise(async (resolve, reject) => {
    fs.readdir(dirPath, { withFileTypes: true }, async (err, entries) => {
      if(err) {
        reject(err)
      } else {
        let results = [];
    
        for (const entry of entries) {
          const fullPath = path.join(dirPath, entry.name);
          if (entry.isDirectory()) {
            results = results.concat(await readDirRecursive(fullPath));
          } else {
            results.push(fullPath);
          }
        }
        resolve(results);
      }
    });
  })
}

/**
 * 1.38.2版本，为了兼容electron22在window closed事件中获取win.id会报错Object has been destroyed使用
 * 将window.id缓存到_winId属性中,避免在closed事件中直接访问id属性导致报错
 */
function fixWindowId(win) {
  if (!win || win._winId || electronVersionAfter30()) return
  win._winId = win.id
  Object.defineProperty(win, "id", {
    get: function () {
      return win._winId
    },
  });
}

module.exports = {
  getParams,
  // 拿到url中的具体某个参数的值
  getUrlParam,

  //ArrayBuffer转Buffer
  toBuffer,
  downloadFile,
  checkFileExists,
  compareVersions,
  electronVersionAfter30,
  // 检查resource文件是否有文件新增/删除权限
  authorityCheck,
  // 窗口置顶显示
  focusWindow,
  sleep,
  getMemoryUsage,
  // 截取跑马灯窗口title的数据
  rearrangeString,
  base64ToBuffer,

  throttle,
  debounce,
  getRegeditKey,
  getCurFocusWindow,
  getStoreCookies,
  clearStoreCookies,
  isPkey,
  isHeyboxId,
  // 新建子进程执行解压，防止同步解压导致卡死
  unzipInChildProcess,
  createObservableArray,
  getGpuCompositingEnable,
  getUseSharedTexture,

  // 音频文件的处理
  getAudioDuration,
  concatAudio,
  readDirRecursive,

  fixWindowId,
  requireAddon,
  ...require('./system_window_list'),
  ...require('./hardware_report'),
  ...require('./dll_update'),
  ...require('./regedit'),
  ...require('./report'),
  ...require('./version'),
  ...require('./node_encrypt'),
  ...require('./steam_login'),
  ...require('./fileHandler'),
  store,
  log,
  eventBus,
  request,
}