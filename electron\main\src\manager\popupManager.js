const { WebContentsView, ipcMain, app, webContents } = require('electron');
const path = require('path');
const { getWebUrl, updateWebContentsUa } = require('../assets/js/utils');
const log = require('@heybox/electron-utils/log');


/**
 * 弹窗浮层管理器
 * 负责管理所有弹窗的创建、销毁、更新等操作
 */
class PopupManager {
  constructor() {
    log.info('[PopupManager] init');
    this.popupView = null;
    this.popupMap = new Map();
    this.destroyedTimer = null;
    this.isCreatingView = false; // 创建锁
    this.createViewPromise = null;
    this.ipcInitialized = false;
    this.popupQueue = []; // 弹窗执行队列
    this.currentPopup = null;
    this.isProcessingQueue = false;

    this.initIpc();
  }

  /**
   * 确保PopupView已经创建
   */
  async ensurePopupView() {
    if (
      this.popupView &&
      this.popupView.webContents &&
      !this.popupView.webContents.isDestroyed()
    ) {
      return;
    }

    // 正在创建中
    if (this.isCreatingView && this.createViewPromise) {
      return this.createViewPromise;
    }

    this.isCreatingView = true;
    this.createViewPromise = this._createPopupView();

    try {
      await this.createViewPromise;
    } finally {
      this.isCreatingView = false;
      this.createViewPromise = null;
    }
  }

  /**
   * 实际创建PopupView的方法
   */
  async _createPopupView() {
    log.info('[PopupManager] 开始创建PopupView');

    const view = new WebContentsView({
      webPreferences: {
        preload: path.join(__dirname, '../preload/popup.js'),
        nodeIntegration: true,
        contextIsolation: true, // 根据您的安全需求调整
        additionalArguments: [`--window-id=popup`],
        transparent: true,
      },
    });

    updateWebContentsUa(view.webContents);

    const popupUrl = getWebUrl('popup');

    if (global.IS_LOCAL_DEV) {
      await view.webContents.loadURL(popupUrl);
    } else {
      await view.webContents.loadFile(popupUrl);
    }

    // F12 开发者工具
    view.webContents.on('before-input-event', (event, input) => {
      if (
        input.code === 'F12' &&
        (global.main_config.devTool || !app.isPackaged)
      ) {
        view.webContents.openDevTools({ mode: 'detach', activate: true });
      }
    });

    this.popupView = view;
    // 将这个View添加到webContentsManager的webViewMap里面
    global.webContentsManager.webViewMap.set('popup', view);
    global.mainWindow.contentView.addChildView(this.popupView);
    this.popupView.setVisible(false);
    log.info('[PopupManager] PopupView创建完成');
  }

  /**
   * 显示弹窗（加入队列）
   * @param {object} options - 展示的配置
   * @param {'Dialog' | 'Popup'} options.type - 弹窗类型
   * @param {string} options.cptName - 组件名
   * @param {Object} options.props - 组件的props
   */
  show(options) {
    log.info('[PopupManager] 弹窗加入队列', options.cptName);
    this.addToQueue(options);
    this.processQueue();
  }

  /**
   * 将弹窗加入队列
   * @param {object} options - 弹窗配置
   */
  addToQueue(options) {
    const existingIndex = this.popupQueue.findIndex(item => item.cptName === options.cptName);

    if (existingIndex !== -1) {
      this.popupQueue[existingIndex] = options;
    } else {
      this.popupQueue.push(options);
    }
  }

  /**
   * 处理弹窗队列
   */
  async processQueue() {
    if (this.isProcessingQueue || this.popupQueue.length === 0) {
      return;
    }
    if (this.currentPopup) {
      return;
    }

    this.isProcessingQueue = true;

    try {
      const nextPopup = this.popupQueue.shift();
      if (!nextPopup) {
        this.isProcessingQueue = false;
        return;
      }

      log.info('[PopupManager] 开始显示弹窗', nextPopup.cptName, '剩余队列长度:', this.popupQueue.length);
      this.currentPopup = nextPopup;

      await this._showPopup(nextPopup);

    } catch (error) {
      log.error('[PopupManager] 处理弹窗队列时出错', error);
      this.currentPopup = null;
    } finally {
      this.isProcessingQueue = false;
    }
  }

  /**
   * 实际显示弹窗的方法
   * @param {object} options - 弹窗配置
   */
  async _showPopup(options) {
    if (this.destroyedTimer) {
      clearTimeout(this.destroyedTimer);
      this.destroyedTimer = null;
    }

    try {
      await this.ensurePopupView();

      const bounds = global.mainWindow.getBounds();
      this.popupView.setBounds({
        x: 0,
        y: 0,
        width: bounds.width,
        height: bounds.height,
      });

      this.popupView.webContents.send('popup:show', options);

      // dialog真的需要展示的时候，即show的值为true，才会触发这个事件
      ipcMain.once('popup:show-success', () => {
        this.popupView.setVisible(true);
      });

      log.info('[PopupManager] show popup success', options.cptName);

    } catch (err) {
      console.error('Failed to show popup:', err);
      this.currentPopup = null;
      this.processQueue()
    }
  }

  /**
   * 隐藏弹窗
   */
  hide(options) {
    if (this.popupView && this.popupView.webContents && !this.popupView.webContents.isDestroyed()) {
      // 通知 popup view 隐藏
      this.popupView.webContents.send('popup:hide', options);
    }

    if (options && options.cptName) {
      this.removeFromQueue(options.cptName);

      if (this.currentPopup && this.currentPopup.cptName === options.cptName) {
        log.info('[PopupManager] 当前弹窗被隐藏', options.cptName);
        this.currentPopup = null;
        this.processQueue()
      }
    }
  }

  /**
   * 从队列中移除指定类型的弹窗
   * @param {string} cptName - 组件名
   * @param {boolean} autoProcess - 是否自动处理队列，默认为 true
   */
  removeFromQueue(cptName, autoProcess = true) {
    const originalLength = this.popupQueue.length;
    this.popupQueue = this.popupQueue.filter(item => item.cptName !== cptName);

    if (this.popupQueue.length < originalLength) {
      log.info('[PopupManager] 从队列中移除弹窗', cptName, '剩余队列长度:', this.popupQueue.length);

      if (autoProcess && this.currentPopup && this.currentPopup.cptName === cptName) {
        this.currentPopup = null;
        this.processQueue()
      }
    }
  }

  /**
   * 弹窗关闭时的回调
   * @param {string} cptName - 关闭的组件名
   */
  onPopupClosed(cptName) {
    log.info('[PopupManager] 弹窗关闭回调', cptName);

    this.removeFromQueue(cptName, false);

    if (this.currentPopup && this.currentPopup.cptName === cptName) {
      this.currentPopup = null;
      this.processQueue()
    }
  }

  /**
   * 初始化IPC
   */
  initIpc() {
    if (this.ipcInitialized) {
      return;
    }

    ipcMain.on('popup:show', (_, options) => {
      this.show(options);
    });

    ipcMain.on('popup:event', (_, popupEvent) => {
      const { popupId, eventName, payload } = popupEvent;
      const webContentsId = this.popupMap.get(popupId);

      if (webContentsId) {
        const webViewContents = webContents.fromId(webContentsId);
        if (webViewContents && !webViewContents.isDestroyed()) {
          webViewContents.send('popup:response', { eventName, payload });
        }
      }

      // 如果是关闭事件，就从map中移除
      if (eventName === 'close') {
        this.popupMap.delete(popupId);
      }
    });

    ipcMain.on('popup:hide', (_, options) => {
      this.hide(options);
    });

    ipcMain.on('popup:close', (_, cptName) => {
      if (cptName) {
        this.onPopupClosed(cptName);
      } else {
        this.close();
      }
    });

    this.ipcInitialized = true;
    log.info('[PopupManager] IPC监听器初始化完成');
  }

  /**
   * 初始化窗口尺寸调整监听
   */
  initResizeWatch() {
    global.mainWindow.on('resize', () => {
      if (this.popupView && this.popupView.webContents && !this.popupView.webContents.isDestroyed()) {
        const bounds = global.mainWindow.getBounds();
        this.popupView.setBounds({
          x: 0,
          y: 0,
          width: bounds.width,
          height: bounds.height,
        });
      }
    });
  }

  /**
   * 销毁
   */
  destroy() {
    if (this.popupView && this.popupView.webContents && !this.popupView.webContents.isDestroyed()) {
      global.mainWindow.contentView.removeChildView(this.popupView);
      this.popupView.webContents.close();
      this.popupView = null;
    }
    global.webContentsManager.webViewMap.delete('popup');

    this.isCreatingView = false;
    this.createViewPromise = null;
    log.info('[PopupManager] PopupView已销毁');
  }

  close() {
    if (this.popupView && this.popupView.webContents && !this.popupView.webContents.isDestroyed()) {
      this.popupView.setVisible(false);
      log.info('[PopupManager] close popup');
    }

    this.currentPopup = null;
  }
}

module.exports = new PopupManager();
