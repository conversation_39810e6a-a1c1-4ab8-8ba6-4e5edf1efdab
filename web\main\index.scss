/* 
  该文件是由sass-loader additionalData 引入的
  只引入一次, 可以写全局的css样式, 
  但是声明的scss变量不能在index.vue文件以外使用(尽量不要在这里声明scss)
*/

@use '@heybox-webapp/hb-theme/config' with (
  $color-use-dark: false,
);
@use "@heybox-webapp/hb-theme/varible" as *;

/* 禁用 xiaomi 字体网络加载，使用系统字体回退 */
@font-face {
  font-family: "xiaomi";
  font-style: normal;
  font-weight: 400;
  src: local("PingFang SC"), local("Microsoft YaHei"), local("SimHei"), local("Arial");
}

@font-face {
  font-family: "xiaomi";
  font-style: normal;
  font-weight: 500;
  src: local("PingFang SC"), local("Microsoft YaHei"), local("SimHei"), local("Arial");
}

* {
  font-family: "Microsoft YaHei", "Arial", sans-serif;
}