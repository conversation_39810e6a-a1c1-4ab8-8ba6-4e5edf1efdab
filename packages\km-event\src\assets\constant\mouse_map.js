module.exports = {
  MOUSE_CODE_MAP: {
    1: { key_status: 0, key_code: 0, key_type: 'mouse' },
    2: { key_status: 1, key_code: 0, key_type: 'mouse' },
    16: { key_status: 0, key_code: 1, key_type: 'mouse' },
    32: { key_status: 1, key_code: 1, key_type: 'mouse' },
    4: { key_status: 0, key_code: 2, key_type: 'mouse' },
    8: { key_status: 1, key_code: 2, key_type: 'mouse' },
    64: { key_status: 0, key_code: 3, key_type: 'mouse' },
    128: { key_status: 1, key_code: 3, key_type: 'mouse' },
    256: { key_status: 0, key_code: 4, key_type: 'mouse' },
    512: { key_status: 1, key_code: 4, key_type: 'mouse' },
  },
  // VK CODE: https://learn.microsoft.com/en-us/windows/win32/inputdev/virtual-key-codes
  // AreTheKeysDown api 检查已按下按键需要将鼠标code映射为VK CODE检测
  MOUSE_CODE_TO_VK: {
    0: 1,
    1: 4,
    2: 2,
    3: 5,
    4: 6
  },
  VK_TO_MOUSE_CODE: {
    1: 0,
    4: 1,
    2: 2,
    5: 3,
    6: 4
  }
}