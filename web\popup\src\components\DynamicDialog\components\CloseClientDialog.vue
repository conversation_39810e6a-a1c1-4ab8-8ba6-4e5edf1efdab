<template>
  <Dialog
    class="cpt-close-client-dialog"
    v-model="show"
    :config="config"
    :disableClose="true"
    @confirm="confirmHandler"
    @cancel="cancelHandler"
    @textBtn="textBtnHandler"
    @close="close"
  >
    <div class="form-wrapper pointer">
      <div class="row pointer-keyset radio-row" @click="closeEvent = 'hide'">
        <div class="radio" :class="{ active: closeEvent === 'hide' }">
          <i class="iconfont icon-common-check-line"></i>
        </div>
        <span :class="{ active: closeEvent === 'hide' }">隐藏到任务栏托盘</span>
      </div>
      <div class="row pointer-keyset radio-row" @click="closeEvent = 'close'">
        <div class="radio" :class="{ active: closeEvent === 'close' }">
          <i class="iconfont icon-common-check-line"></i>
        </div>
        <span :class="{ active: closeEvent === 'close' }">退出主程序</span>
      </div>
    </div>
    <template #bottom>
      <div class="radio-row" @click="closeTip = !closeTip">
        <div class="radio" :class="{ active: closeTip }">
          <i class="iconfont icon-common-check-line"></i>
        </div>
        <span>不再显示</span>
      </div>
    </template>
  </Dialog>
</template>

<script setup name="CloseClientDialog">
import { ref, defineProps } from 'vue';
import {
  useSubComponent,
  getDefaultSubProps,
} from '../composables/useSubComponent';
import { ipcService } from '@heybox-app-web-shared/ipc';
import { storeGetter, storeSetter } from '@heybox-app-web-shared/utils'

const [setting_data] = storeGetter(['setting_data'], 'main_config')

const props = defineProps(getDefaultSubProps());

const { show, handleClose, Dialog } = useSubComponent({
  initDialog: () => {
    closeEvent.value = setting_data.value.close_event || 'hide'
    show.value = true;
  },
});

const config = ref({
  title: '关闭窗口后',
  confirm: {
    text: '确定',
  },
  cancel: {
    text: '取消',
  },
});

const closeEvent = ref('')
const closeTip = ref(false)

const SET_SETTING_DATA_PARAM = (params) => {
  const newSettingData = {
    ...setting_data.value,
    ...params,
  }
  storeSetter('setting_data', newSettingData, 'main_config')
}

const close = (data) => {
  handleClose();
};

const confirmHandler = async (data) => {
  SET_SETTING_DATA_PARAM({'close_event': closeEvent.value})
  storeSetter('never_show_close_dialog', closeTip.value, 'main_config')
  setTimeout(() => {
    window.windowAPI.mainWindowControl('close')
    close(data);
  }, 100)
};

const cancelHandler = (data) => {
  close(data)
};
</script>

<style lang="scss">
.cpt-close-client-dialog {
  .hb-dialog-container {
    padding: 30px 20px 20px;
    .bottom-slot-wrapper {
      display: flex;
      align-items: center;
      justify-content: center;
      .radio-row {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        cursor: pointer;
        gap: 4px;
        span {
          color: $general-color-text-3;
        }
        .radio {
          width: 14px;
          height: 14px;
          .iconfont::before {
            line-height: 14px;
            text-align: center;
          }
          &.active {
            &::after {
              width: 14px;
              height: 14px;
            }
          }
        }
      }
    }
    .form-wrapper {
      padding: 4px 0;
      background-color: $general-color-bg-0;
      border-radius: 5px;
    }
    .row {
      padding: 15px 12px;
      min-height: 42px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-weight: 400;
      font-size: 14px;
      line-height: 18px;
      text-align: center;
      letter-spacing: 0.01em;
      color: $general-color-text-1;
      &.radio-row {
        justify-content: flex-start;
        gap: 8px;
        span {
          color: $general-color-text-2;
        }
      }
      & span {
        color: $general-color-text-1;
        &.desc {
          margin-top: 3px;
          font-size: 12px;
          line-height: 16px;
          color: $general-color-text-3;
        }
        &.color-tx-3 {
          color: $general-color-text-3;
        }
        &.active {
          color: $general-color-text-1;
          font-weight: 600;
        }
      }
      .text-block {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
      }
    }
    .radio {
      width: 16px;
      height: 16px;
      border: 1px solid $general-color-text-4;
      background-color: $general-color-bg-1;
      border-radius: 50%;
      .iconfont {
        display: none;
        position: relative;
        font-size: 10px;
        color: $general-color-text-6;
        z-index: 1;
        &::before {
          line-height: 17px;
        }
      }

      &.active {
        border: $general-color-text-1;
        position: relative;
        .iconfont {
          display: block;
        }

        &::after {
          content: '';
          position: absolute;
          width: 16px;
          height: 16px;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          background: $general-color-text-1;
          border-radius: 50%;
        }
      }
    }
  }
}
</style>
