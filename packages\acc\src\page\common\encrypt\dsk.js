import AES from 'crypto-js/aes';
import UTF8 from 'crypto-js/enc-utf8';
import { mode } from 'crypto-js';
import md5 from 'js-md5'
import getRSA from './jsencrypt'

function getBaseString () {
  const base = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~"
  let temp = ''
  while (temp.length < 16) {
    temp += base[Math.floor(base.length * Math.random(Math.random()))]
  }
  return temp
}

function getAESKey (data, key) {
  let iv = UTF8.parse('abcdefghijklmnop')
  let temp = AES.encrypt(UTF8.parse(data), UTF8.parse(key),{iv: iv, mode: mode.CBC,}).toString()
  return temp
}

export default function (data) {
  let key = getBaseString(), sid, time_ = ~~(+(new Date().getTime()) / 1000)
  data = getAESKey(JSON.stringify(data), key)
  key = getRSA(key)
  sid = md5(data + time_) + md5(key)
  return {sid, key, data}
}
