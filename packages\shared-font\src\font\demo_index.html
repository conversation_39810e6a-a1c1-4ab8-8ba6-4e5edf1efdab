<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=4950435" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">pc-link-filled</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a9;</span>
                <div class="name">common-arrow-down-line</div>
                <div class="code-name">&amp;#xe7a9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a8;</span>
                <div class="name">pc-download-filled</div>
                <div class="code-name">&amp;#xe7a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a6;</span>
                <div class="name">common-help-line</div>
                <div class="code-name">&amp;#xe7a6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a7;</span>
                <div class="name">pc-maximization-line</div>
                <div class="code-name">&amp;#xe7a7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a4;</span>
                <div class="name">pc-minimize-line</div>
                <div class="code-name">&amp;#xe7a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a5;</span>
                <div class="name">common-copy-line</div>
                <div class="code-name">&amp;#xe7a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a0;</span>
                <div class="name">pc-clockwise-line</div>
                <div class="code-name">&amp;#xe7a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a1;</span>
                <div class="name">common-list2-filled</div>
                <div class="code-name">&amp;#xe7a1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a2;</span>
                <div class="name">common-email-filled</div>
                <div class="code-name">&amp;#xe7a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a3;</span>
                <div class="name">bbs-game-filled</div>
                <div class="code-name">&amp;#xe7a3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79f;</span>
                <div class="name">common-check-line</div>
                <div class="code-name">&amp;#xe79f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe753;</span>
                <div class="name">common-filter-filled</div>
                <div class="code-name">&amp;#xe753;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe762;</span>
                <div class="name">bbs-comment-filled</div>
                <div class="code-name">&amp;#xe762;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75f;</span>
                <div class="name">bbs-thumbs-up-filled</div>
                <div class="code-name">&amp;#xe75f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75c;</span>
                <div class="name">game-steam-platform-filled</div>
                <div class="code-name">&amp;#xe75c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75a;</span>
                <div class="name">common-arrow-up_filled</div>
                <div class="code-name">&amp;#xe75a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75e;</span>
                <div class="name">common-arrow-right_filled</div>
                <div class="code-name">&amp;#xe75e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75d;</span>
                <div class="name">common-filter2-filled</div>
                <div class="code-name">&amp;#xe75d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe75b;</span>
                <div class="name">common-arrow-down_filled</div>
                <div class="code-name">&amp;#xe75b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe772;</span>
                <div class="name">common-arrow_left_line</div>
                <div class="code-name">&amp;#xe772;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76e;</span>
                <div class="name">common-add_line</div>
                <div class="code-name">&amp;#xe76e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe76f;</span>
                <div class="name">common-close_line</div>
                <div class="code-name">&amp;#xe76f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe770;</span>
                <div class="name">common-more_line</div>
                <div class="code-name">&amp;#xe770;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe760;</span>
                <div class="name">game-heybox-platform-filled_v</div>
                <div class="code-name">&amp;#xe760;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1752548535556') format('woff2'),
       url('iconfont.woff?t=1752548535556') format('woff'),
       url('iconfont.ttf?t=1752548535556') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-pc-link-filled"></span>
            <div class="name">
              pc-link-filled
            </div>
            <div class="code-name">.icon-pc-link-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-arrow-down-line"></span>
            <div class="name">
              common-arrow-down-line
            </div>
            <div class="code-name">.icon-common-arrow-down-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pc-download-filled"></span>
            <div class="name">
              pc-download-filled
            </div>
            <div class="code-name">.icon-pc-download-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-help-line"></span>
            <div class="name">
              common-help-line
            </div>
            <div class="code-name">.icon-common-help-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pc-maximization-line"></span>
            <div class="name">
              pc-maximization-line
            </div>
            <div class="code-name">.icon-pc-maximization-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pc-minimize-line"></span>
            <div class="name">
              pc-minimize-line
            </div>
            <div class="code-name">.icon-pc-minimize-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-copy-line"></span>
            <div class="name">
              common-copy-line
            </div>
            <div class="code-name">.icon-common-copy-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-pc-clockwise-line"></span>
            <div class="name">
              pc-clockwise-line
            </div>
            <div class="code-name">.icon-pc-clockwise-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-list2-filled"></span>
            <div class="name">
              common-list2-filled
            </div>
            <div class="code-name">.icon-common-list2-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-email-filled"></span>
            <div class="name">
              common-email-filled
            </div>
            <div class="code-name">.icon-common-email-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bbs-game-filled"></span>
            <div class="name">
              bbs-game-filled
            </div>
            <div class="code-name">.icon-bbs-game-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-check-line"></span>
            <div class="name">
              common-check-line
            </div>
            <div class="code-name">.icon-common-check-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-filter-filled"></span>
            <div class="name">
              common-filter-filled
            </div>
            <div class="code-name">.icon-common-filter-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bbs-comment-filled"></span>
            <div class="name">
              bbs-comment-filled
            </div>
            <div class="code-name">.icon-bbs-comment-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-bbs-thumbs-up-filled"></span>
            <div class="name">
              bbs-thumbs-up-filled
            </div>
            <div class="code-name">.icon-bbs-thumbs-up-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-game-steam-platform-filled"></span>
            <div class="name">
              game-steam-platform-filled
            </div>
            <div class="code-name">.icon-game-steam-platform-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-arrow-up-filled"></span>
            <div class="name">
              common-arrow-up_filled
            </div>
            <div class="code-name">.icon-common-arrow-up-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-arrow-right-filled"></span>
            <div class="name">
              common-arrow-right_filled
            </div>
            <div class="code-name">.icon-common-arrow-right-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-filter2-filled"></span>
            <div class="name">
              common-filter2-filled
            </div>
            <div class="code-name">.icon-common-filter2-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-arrow-down-filled"></span>
            <div class="name">
              common-arrow-down_filled
            </div>
            <div class="code-name">.icon-common-arrow-down-filled
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-arrow-left-line"></span>
            <div class="name">
              common-arrow_left_line
            </div>
            <div class="code-name">.icon-common-arrow-left-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-add-line"></span>
            <div class="name">
              common-add_line
            </div>
            <div class="code-name">.icon-common-add-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-close-line"></span>
            <div class="name">
              common-close_line
            </div>
            <div class="code-name">.icon-common-close-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-common-more-line"></span>
            <div class="name">
              common-more_line
            </div>
            <div class="code-name">.icon-common-more-line
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-game-heybox-platform-filled_v"></span>
            <div class="name">
              game-heybox-platform-filled_v
            </div>
            <div class="code-name">.icon-game-heybox-platform-filled_v
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pc-link-filled"></use>
                </svg>
                <div class="name">pc-link-filled</div>
                <div class="code-name">#icon-pc-link-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-arrow-down-line"></use>
                </svg>
                <div class="name">common-arrow-down-line</div>
                <div class="code-name">#icon-common-arrow-down-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pc-download-filled"></use>
                </svg>
                <div class="name">pc-download-filled</div>
                <div class="code-name">#icon-pc-download-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-help-line"></use>
                </svg>
                <div class="name">common-help-line</div>
                <div class="code-name">#icon-common-help-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pc-maximization-line"></use>
                </svg>
                <div class="name">pc-maximization-line</div>
                <div class="code-name">#icon-pc-maximization-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pc-minimize-line"></use>
                </svg>
                <div class="name">pc-minimize-line</div>
                <div class="code-name">#icon-pc-minimize-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-copy-line"></use>
                </svg>
                <div class="name">common-copy-line</div>
                <div class="code-name">#icon-common-copy-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-pc-clockwise-line"></use>
                </svg>
                <div class="name">pc-clockwise-line</div>
                <div class="code-name">#icon-pc-clockwise-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-list2-filled"></use>
                </svg>
                <div class="name">common-list2-filled</div>
                <div class="code-name">#icon-common-list2-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-email-filled"></use>
                </svg>
                <div class="name">common-email-filled</div>
                <div class="code-name">#icon-common-email-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bbs-game-filled"></use>
                </svg>
                <div class="name">bbs-game-filled</div>
                <div class="code-name">#icon-bbs-game-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-check-line"></use>
                </svg>
                <div class="name">common-check-line</div>
                <div class="code-name">#icon-common-check-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-filter-filled"></use>
                </svg>
                <div class="name">common-filter-filled</div>
                <div class="code-name">#icon-common-filter-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bbs-comment-filled"></use>
                </svg>
                <div class="name">bbs-comment-filled</div>
                <div class="code-name">#icon-bbs-comment-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-bbs-thumbs-up-filled"></use>
                </svg>
                <div class="name">bbs-thumbs-up-filled</div>
                <div class="code-name">#icon-bbs-thumbs-up-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-game-steam-platform-filled"></use>
                </svg>
                <div class="name">game-steam-platform-filled</div>
                <div class="code-name">#icon-game-steam-platform-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-arrow-up-filled"></use>
                </svg>
                <div class="name">common-arrow-up_filled</div>
                <div class="code-name">#icon-common-arrow-up-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-arrow-right-filled"></use>
                </svg>
                <div class="name">common-arrow-right_filled</div>
                <div class="code-name">#icon-common-arrow-right-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-filter2-filled"></use>
                </svg>
                <div class="name">common-filter2-filled</div>
                <div class="code-name">#icon-common-filter2-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-arrow-down-filled"></use>
                </svg>
                <div class="name">common-arrow-down_filled</div>
                <div class="code-name">#icon-common-arrow-down-filled</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-arrow-left-line"></use>
                </svg>
                <div class="name">common-arrow_left_line</div>
                <div class="code-name">#icon-common-arrow-left-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-add-line"></use>
                </svg>
                <div class="name">common-add_line</div>
                <div class="code-name">#icon-common-add-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-close-line"></use>
                </svg>
                <div class="name">common-close_line</div>
                <div class="code-name">#icon-common-close-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-common-more-line"></use>
                </svg>
                <div class="name">common-more_line</div>
                <div class="code-name">#icon-common-more-line</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-game-heybox-platform-filled_v"></use>
                </svg>
                <div class="name">game-heybox-platform-filled_v</div>
                <div class="code-name">#icon-game-heybox-platform-filled_v</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
