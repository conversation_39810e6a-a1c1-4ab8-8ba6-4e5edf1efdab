import copy from 'rollup-plugin-copy';
import { babel } from '@rollup/plugin-babel';
import commonjs from '@rollup/plugin-commonjs';
import { nodeResolve } from '@rollup/plugin-node-resolve';

export default {
  external: [], // 这里配置 直接引入不打包，like vue,react  设置 external: ['vue','react']
  input: 'src/index.js',
  output: [
    {
      format: 'esm',
      file: 'lib/index.esm.js',
      exports: 'auto',
    },
    {
      format: 'cjs',
      file: 'lib/index.cjs.js',
      exports: 'auto',
      interop: 'auto' // 处理 CJS/ESM 互操作
    },
  ],
  plugins: [
    nodeResolve({
      extensions: ['.js', '.jsx', '.mjs']
    }),
    babel({
      babelHelpers: 'runtime', // 明确指定使用 runtime 模式
      skipPreflightCheck: true,  // 避免旧版兼容性检查
      exclude: 'node_modules/core-js/**'  // 排除 core-js
    }), 
    commonjs({
      include: /node_modules/,
      requireReturnsDefault: 'auto'  // 处理 CJS 模块导出
    }),
    copy({
      targets: [
        { src: 'src/page/*', dest: 'lib/page' }, // 复制弹窗页文件夹
        { src: 'src/heybox_js/*', dest: 'lib/heybox_js' }, // 复制heybox_js文件夹
        { src: 'src/assets/*', dest: 'lib/assets' }, // 复制assets文件夹
        { src: 'src/preload.js', dest: 'lib' }, // 复制preload.js
      ],
    })
  ]
}