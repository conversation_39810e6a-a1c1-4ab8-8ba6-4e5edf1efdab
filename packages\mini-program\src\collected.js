const { ipcMain } = require('electron')

ipcMain.on('miniprogram:collected-change', (_, d) => {
  global.MINI_PROGRAM_INSTANCE_GROUP.forEach(ins => {
    if (ins.window && !ins.window.isDestroyed()) {
      ins.window.webContents.send('MiniProgramSDK:onCollectedMiniProgramChange', d)
    }
  })
})

function getIsMiniProgramCollected(mini_pro_id) {
  return new Promise((resolve) => {
    let cb = (_, id, collected) => {
      if (id === mini_pro_id) {
        ipcMain.off('miniprogram:is-collected', cb)
        resolve(collected);
      }
    }
    ipcMain.on('miniprogram:is-collected', cb)
    mainWindow.webContents.send('miniprogram:on-get-collected', mini_pro_id)
  })
}

function addMiniProgramCollected(mini_pro_id) {
  mainWindow.webContents.send('miniprogram:on-add-collected', mini_pro_id)
}

function removeMiniProgramCollected(mini_pro_id) {
  mainWindow.webContents.send('miniprogram:on-remove-collected', mini_pro_id)
}

module.exports = { 
  getIsMiniProgramCollected,
  addMiniProgramCollected,
  removeMiniProgramCollected,
};
