const fs = require('fs');

const args = process.argv.slice(2);
const mode = args.find(arg => arg === 'dev' || arg === 'build');
if (!mode || !['dev', 'build'].includes(mode)) {
  console.error('请指定模式: dev 或 build');
  process.exit(1);
}

// 处理 npm_config_path，移除换行符并转换为数组
const paths = process.env.npm_config_path
  ? process.env.npm_config_path
      .split('\n')
      .filter(Boolean) // 移除空字符串
      .map(path => path.trim()) // 移除首尾空格
  : [];

const electronEnv = process.env.npm_config_env || (paths.length > 0 || mode === 'dev' ? 'local' : '');

const content = `module.exports = {
  ${(electronEnv && electronEnv !== 'prod') ? `ELECTRON_ENV: '${electronEnv}',` : ''}
  NODE_BIT: ${process.arch.includes('64') ? 64 : 32},
  ${paths.length > 0 && electronEnv.includes('local') ? `LOCAL_WEB_PATHS: ${JSON.stringify(paths)}` : ''}
};`;

fs.writeFileSync('./env.js', content);
console.log('env.js 文件已创建并写入内容', electronEnv, process.arch, paths);