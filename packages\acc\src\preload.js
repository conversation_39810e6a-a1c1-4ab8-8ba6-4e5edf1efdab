const { ipc<PERSON><PERSON><PERSON> } = require('electron');

window.electronAPI = {
  requestSMId: (cb) => ipcRenderer.on('request-sm-id', cb),
  sendSMId: (smId) => ipcRenderer.send('send-sm-id', smId),
  initAcc: () => ipcRenderer.invoke('heybox:init-acc'),
  getUserInfo: () => ipcRenderer.invoke('heybox:get-user-info'),
  userCertify: (params) => ipcRenderer.invoke('heybox:user-certify', params),
  getProxyNodeList: (params) => ipcRenderer.invoke('heybox:get-proxy-node-list', params),
  startAcc: (params) => ipcRenderer.invoke('heybox:sdk-start-accelerate', params),
  stopAcc: (params) => ipcRenderer.invoke('heybox:sdk-stop-accelerate', params),
  loginByPassword: (params) => ipc<PERSON>enderer.invoke('heybox:login-by-password', params),
  loginByCode: (params) => ipcRenderer.invoke('heybox:login-by-code', params),
  logout: () => ipcRenderer.invoke('heybox:logout'),
  getCode: (params) => ipcRenderer.invoke('heybox:get-code', params),
  getQrCode: () => ipcRenderer.invoke('heybox:get-qr-code'),
  getQrState: (params) => ipcRenderer.invoke('heybox:get-qr-state', params),
  loginByQr: (params) => ipcRenderer.invoke('heybox:login-by-qr', params),
  getWechatQrCode: () => ipcRenderer.invoke('heybox:get-wechat-qr-code'),
  loginByWechat: (cb) => ipcRenderer.on('heybox:login-by-wechat', cb),
  recharge: (params) => ipcRenderer.invoke('heybox:recharge', params),
  sendRequest: (url, method, params, form) => ipcRenderer.invoke('heybox:send-request', url, method, params, form),
  getVersion: () => ipcRenderer.invoke('heybox:get-version'),
  close: (windowName) => ipcRenderer.send('heybox:close-window', windowName),
  openPage: (page, params) => ipcRenderer.send('heybox:open-page', page, params),
  openInBrowser: (url) => ipcRenderer.send('heybox:open-in-browser', url),
  setOptions: (cb) => ipcRenderer.on('heybox:set-options', cb),
}