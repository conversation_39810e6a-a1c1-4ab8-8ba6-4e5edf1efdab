const { ipcMain } = require('electron')

class GlobalStateManager {
  constructor() {
    this.states = []
    this.initIpc()
  }

  initIpc() {
    ipcMain.on('globalState:add', (_, key, data, config) => {
      this.addState(key, data, config)
    })
    ipcMain.on('globalState:remove', (_, key) => {
      this.removeState(key)
    })
    ipcMain.on('globalState:update', (_, key, data, config) => {
      this.updateState(key, data, config)
    })
    ipcMain.handle('globalState:get', (_, key) => {
      return this.getState(key)
    })
  }

  getState(key) {
    if (key) {
      return this.states.find(state => state.key === key)?.data
    } else {
      return this.states
    }
  }

  notifyStateChange() {
    mainWindow?.webContents.send('globalState:onChange', this.states)
  }

  // 添加或更新状态
  addState(id, data, config = {}) {
    const {add_direction = 'top', force_add = false} = config
    const old_index = this.states.findIndex(state => state.id === id)
    if (old_index === -1) {
      const new_state = {
        id,
        data,
        timestamp: Date.now(),
      }
      if (add_direction === 'top') {
        this.states.unshift(new_state)
      } else {
        this.states.push(new_state)
      }
      this.notifyStateChange()
    } else {
      if (force_add) {
        this.states.splice(old_index, 1)
        const new_state = {
          id,
          data,
          timestamp: Date.now(),
        }
        if (add_direction === 'top') {
          this.states.unshift(new_state)
        } else {
          this.states.push(new_state)
        }
        this.notifyStateChange()
      } else {
        this.updateState(id, data, config)
      }
    }
  }

  // 移除状态
  removeState(id) {
    const old_index = this.states.findIndex(state => state.id === id)
    if (old_index !== -1) {
      this.states.splice(old_index, 1)
      this.notifyStateChange()
    }
  }

  // 更新状态
  updateState(id, data, config) {
    const old_index = this.states.findIndex(state => state.id === id)
    if (old_index !== -1) {
      this.states[old_index] = {id, data, timestamp: Date.now()}
      this.notifyStateChange()
    }
  }
}

// 创建单例实例

module.exports = new GlobalStateManager()
