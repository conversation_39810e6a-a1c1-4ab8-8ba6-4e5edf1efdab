<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset=utf-8>
  <meta http-equiv=X-UA-Compatible content="IE=edge">
  <meta name=viewport content="width=device-width,initial-scale=1">
  <title> </title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    html,
    body {
      background-color: transparent;
    }

    body {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .loading {
      width: 300px;
      height: 300px;
      border: 1px solid rgba(255, 255, 255, 0.04);
      border-radius: 8px;
      background: rgba(250, 251, 252, 1);
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
    }
    svg {
      width: 112px;
      height: 112px;
    }
    .progress {
      width: 180px;
      height: 6px;
      margin: 36px auto 40px;
      border-radius: 3px;
      background-color: rgba(218, 221, 224, 1);
      position: relative;
    }
    .bar {
      width: 0%;
      height: 6px;
      border-radius: 3px;
      background: linear-gradient(46deg, #464B50 -0.9%,  #14191E 100.9%);
      position: absolute;
      top: 0;
      left: 0;
      transition: all 0.15s linear;
    }
  </style>
</head>

<body>
  <div class="loading">
    <svg width="112" height="112" viewBox="0 0 112 112" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M28.0684 24.1642C29.2731 23.4836 30.7754 24.3421 30.7754 25.7046V67.7274C30.7761 68.7783 31.3507 69.7557 32.2839 70.2886L45.2949 77.6805C46.3039 78.2433 47.5551 77.5311 47.5553 76.3908V66.0822C47.5553 65.1053 48.367 64.306 49.36 64.3049H62.5853C63.5793 64.3049 64.3945 65.1046 64.3945 66.0822V105.557C64.3932 106.919 62.887 107.777 61.6829 107.098L47.5553 99.0816L30.7754 89.5568L15.5039 80.8934C14.5715 80.3602 14.0005 79.3828 14 78.3322V33.8485C14.0005 32.7979 14.5715 31.8205 15.5039 31.2873L15.5677 31.2599L28.0684 24.1642ZM47.6146 6.44549C47.6159 5.0841 49.122 4.22479 50.3262 4.90513L64.4401 12.9351L81.2155 22.4598L96.5007 31.1232C97.4344 31.6565 98.0091 32.6373 98.0091 33.689V78.1681C98.009 79.2197 97.4343 80.1962 96.5007 80.7293L96.4414 80.7612L83.9408 87.8569C82.7367 88.5367 81.2306 87.6777 81.2292 86.3166V44.2938C81.2292 43.2421 80.659 42.2613 79.7253 41.728L66.7142 34.3407C65.7056 33.7781 64.4549 34.4862 64.4538 35.6258V45.9344C64.4538 46.912 63.6385 47.7163 62.6445 47.7163H49.4238C48.4298 47.7163 47.6146 46.912 47.6146 45.9344V6.44549Z" fill="#14191E"/>
    </svg>
    <div class="progress">
      <div class="bar"></div>
    </div>
  </div>
  <script>
    let bar = document.querySelector('.bar')
    window.MiniProgramSDK.onProgress((e, data) => {
      bar.style.width = data.progress * 100 + '%'
    })
  </script>
</body>
</html>