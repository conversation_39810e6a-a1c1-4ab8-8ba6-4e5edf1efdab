// Mock 数据用于游戏好友组件测试

const avatarPool = [
  "https://imgheybox.max-c.com/heybox/avatar/steamcommunity/public/images/2023/05/23/94fe9c6d7556430fa16500af4df896b3.jpg",
  "https://imgheybox.max-c.com/heybox/avatar/steamcommunity/public/images/2023/05/23/030f9fbcc04fb587a7efd2dff43edba9.jpg",
  "https://imgheybox.max-c.com/heybox/avatar/steamcommunity/public/images/2025/03/24/2f566c9bb80e8faf34cabfac455dc6fd.jpg"
];

const generateAvatar = (id) => {
  const index = parseInt(id) % avatarPool.length;
  return avatarPool[index];
};

// 正在游玩的好友数据
export const playingFriends = [
  {
    heybox_id: '1',
    nickname: '高桥一碗',
    avatar: generateAvatar('1'),
    description: '大厅',
    status: 'playing'
  },
  {
    heybox_id: '2',
    nickname: '高桥一碗',
    avatar: generateAvatar('2'),
    description: '大厅',
    status: 'playing'
  },
  {
    heybox_id: '3',
    nickname: '高桥一二三四五六七碗',
    avatar: generateAvatar('3'),
    description: '天梯 - 黑鸟5级',
    status: 'playing'
  },
  {
    heybox_id: '4',
    nickname: '高桥一碗',
    avatar: generateAvatar('4'),
    description: '大厅',
    status: 'playing'
  },
  {
    heybox_id: '5',
    nickname: '高桥一碗',
    avatar: generateAvatar('5'),
    description: '大厅',
    status: 'playing'
  },
  {
    heybox_id: '6',
    nickname: '高桥一碗',
    avatar: generateAvatar('6'),
    description: '大厅',
    status: 'playing'
  },
  {
    heybox_id: '7',
    nickname: '高桥一碗',
    avatar: generateAvatar('7'),
    description: '大厅',
    status: 'playing'
  },
  {
    heybox_id: '8',
    nickname: '高桥一碗',
    avatar: generateAvatar('8'),
    description: '大厅',
    status: 'playing'
  }
];

// 最近游玩的好友数据
export const recentFriends = [
  {
    heybox_id: '101',
    nickname: '高桥一碗',
    avatar: generateAvatar('101'),
    playTime: '近两周 22.5h',
  },
  {
    heybox_id: '102',
    nickname: '高桥一碗',
    avatar: generateAvatar('102'),
    playTime: '近两周 22.5h',
    status: 'online'
  },
  {
    heybox_id: '103',
    nickname: '高桥一碗',
    avatar: generateAvatar('103'),
    playTime: '近两周 22.5h',
    status: 'online'
  },
  {
    heybox_id: '104',
    nickname: '高桥一碗',
    avatar: generateAvatar('104'),
    playTime: '近两周 22.5h',
    status: 'online'
  },
  {
    heybox_id: '105',
    nickname: '高桥一碗',
    avatar: generateAvatar('105'),
    playTime: '近两周 22.5h',
    status: 'online'
  },
  {
    heybox_id: '106',
    nickname: '高桥一碗',
    avatar: generateAvatar('106'),
    playTime: '近两周 22.5h',
    status: 'online'
  }
];

// 之前玩过的好友数据
export const previousFriends = [
  {
    heybox_id: '201',
    nickname: '高桥一碗',
    avatar: generateAvatar('201')
  },
  {
    heybox_id: '202',
    nickname: '高桥一碗',
    avatar: generateAvatar('202'),
    status: 'online'
  },
  {
    heybox_id: '203',
    nickname: '高桥一碗',
    avatar: generateAvatar('203')
  },
  {
    heybox_id: '204',
    nickname: '高桥一碗',
    avatar: generateAvatar('204')
  },
  {
    heybox_id: '205',
    nickname: '高桥一碗',
    avatar: generateAvatar('205')
  },
  {
    heybox_id: '206',
    nickname: '高桥一碗',
    avatar: generateAvatar('206')
  },
  {
    heybox_id: '207',
    nickname: '高桥一碗',
    avatar: generateAvatar('207')
  },
  {
    heybox_id: '208',
    nickname: '高桥一碗',
    avatar: generateAvatar('208'),
    status: 'online'
  },
  {
    heybox_id: '209',
    nickname: '高桥一碗',
    avatar: generateAvatar('209')
  },
  {
    heybox_id: '210',
    nickname: '高桥一碗',
    avatar: generateAvatar('210')
  },
  {
    heybox_id: '211',
    nickname: '高桥一碗',
    avatar: generateAvatar('211')
  },
  {
    heybox_id: '212',
    nickname: '高桥一碗',
    avatar: generateAvatar('212')
  },
  {
    heybox_id: '213',
    nickname: '高桥一碗',
    avatar: generateAvatar('213')
  },
  {
    heybox_id: '214',
    nickname: '高桥一碗',
    avatar: generateAvatar('214')
  },
  {
    heybox_id: '215',
    nickname: '高桥一碗',
    avatar: generateAvatar('215'),
    status: 'playing'
  },
  {
    heybox_id: '216',
    nickname: '高桥一碗',
    avatar: generateAvatar('216')
  },
  {
    heybox_id: '217',
    nickname: '高桥一碗',
    avatar: generateAvatar('217')
  },
  {
    heybox_id: '218',
    nickname: '高桥一碗',
    avatar: generateAvatar('218')
  },
  {
    heybox_id: '219',
    nickname: '高桥一碗',
    avatar: generateAvatar('219')
  },
  {
    heybox_id: '220',
    nickname: '高桥一碗',
    avatar: generateAvatar('220')
  }
];

// 愿望单好友数据
export const wishlistFriends = [
  {
    heybox_id: '301',
    nickname: '高桥一碗',
    avatar: generateAvatar('301')
  },
  {
    heybox_id: '302',
    nickname: '高桥一碗',
    avatar: generateAvatar('302'),
    status: 'playing'
  },
  {
    heybox_id: '303',
    nickname: '高桥一碗',
    avatar: generateAvatar('303')
  }
];
