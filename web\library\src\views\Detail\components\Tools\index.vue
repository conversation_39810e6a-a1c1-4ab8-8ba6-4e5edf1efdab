<template>
  <div class="cpt-tools">
    <div class="comp-title tools-title">关联工具</div>
    <div class="tools-container">
      <ToolCard
        v-for="tool in displayedTools"
        :key="tool.id"
        :tool="tool"
      />
    </div>
    <CheckMore
      v-if="tools && tools.length > 2"
      :isExpanded="isExpanded"
      @toggle="handleGetMoreTools"
    />
  </div>
</template>

<script setup name="Tools">
import ToolCard from './components/ToolCard.vue';
import CheckMore from '@/components/func/CheckMore.vue';
import { defineProps, ref, computed } from 'vue';

const props = defineProps({
  tools: {
    type: Array,
    default: () => [],
  },
});

const isExpanded = ref(false);

const displayedTools = computed(() => {
  if (!props.tools) return [];

  if (isExpanded.value) {
    return props.tools;
  } else {
    return props.tools.slice(0, 2);
  }
});

const handleGetMoreTools = () => {
  isExpanded.value = !isExpanded.value;
};
</script>

<style lang="scss">
.cpt-tools {
  padding: 14px 20px 0;
  background: var(---general-color-primary-0, #fff);
  border-radius: 8px;

  .tools-title {
    align-self: stretch;
    color: 1111111;
    font-size: 16px;
    font-weight: 500;

    margin-bottom: 10px;
  }
  .tools-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding-bottom: 24px;
  }
}
</style>
