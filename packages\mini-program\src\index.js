const { BrowserWindow, shell } = require('electron')
const path = require('path')
const { log, store } = require('@heybox/electron-utils')
const request = require('@heybox/electron-utils/request')
const cdn = require('@heybox/electron-utils/cdn')
const { 
  ensureDirExists, 
  pathExists, 
  walkDir,
  clearInstanceCache,
  getConfigJson,
  getInjectGameWindow,
  getMiniProWindowSize,
  setMiniProWindowSize,
  isMiniProWindowSizeNeedStore,
  checkBoundInDisplay,
  getConfigInDisplayConfig,
} = require('./mini_sdk')
const utils = require('@heybox/electron-utils')
const { extractZip, removeFile } = utils
const { downloadFile } = cdn
const {
  getIsMiniProgramCollected,
  addMiniProgramCollected,
  removeMiniProgramCollected,
} = require('./collected')

const { getEventManager } = require('./event')

global.MINI_PROGRAM_INSTANCE_GROUP = []
global.MINI_PROGRAM_INIT_INSTANCE_PROMISE = {}

const ERRORS = {
  DEFAULT: '小程序加载失败',
  INVALID_VERSION: '无效的版本号，请更新到最新版本后重试',
  NETWORK_ERROR: '网络错误，请稍后重试',
  REQUEST_ERROR: '请求失败，请稍后重试',
  DOWNLOAD_FAILED: '下载小程序失败，请稍后重试',
}

const LOADING = require('./loading_window')

const MINI_PROGRAM = {
  // 检测小程序绑定游戏启动钩子
  async processStart(moduleName) {
    log.info('[miniprogram] processStartFunc', moduleName)
    let instance = getInstance(moduleName)
    if (instance) {
      instance.moduleEntry.processStart && instance.moduleEntry.processStart(getEventManager(moduleName))
      return
    }
    await initInstance(moduleName, (instance) => {
      instance.moduleEntry.processStart && instance.moduleEntry.processStart(getEventManager(moduleName))
    }, {showLoading: false})
  },
  beforeMount (moduleName, options = {}) {
    return new Promise(async (resolve, reject) => {
      // 重复打开处理
      let ins = getInstance(moduleName)
      if (ins) {
        if (ins.moduleEntry?.repeatBeforeMount) {
          ins.moduleEntry.repeatBeforeMount(options)
        } else if (options.isOverlay) {
          ins.window && ins.window.close()
        } else {
          ins.window && utils.focusWindow(ins.window)
          resolve(ins)
        }
        return
      }

      await initInstance(moduleName, (instance) => {
        log.info('[miniprogram] beforeMountFunc', moduleName, options)
        instance.moduleEntry.beforeMount && instance.moduleEntry.beforeMount(getEventManager(moduleName))
        instance.options = options
        let config = getConfigJson(instance)
        // 小程序config.json配置manualCreateWindow，可以不自动创建窗口，并且在窗口销毁时不自动卸载小程序
        if (!config.manualCreateWindow) {
          this.createWindow(instance, config, options)
        }
        resolve(instance)
      })
    })
  },
  beforeDestroy (moduleName) {
    let instance = getInstance(moduleName)
    if (!instance) return

    if (instance.moduleEntry && instance.moduleEntry.beforeDestroy) {
      instance.moduleEntry.beforeDestroy(getEventManager(moduleName))
    }
    // 清理require缓存
    clearInstanceCache(instance)
    // 清除MINI_PROGRAM_INSTANCE_GROUP中的instance实例
    clearInstanceGroup(instance)
    // 清除小程序绑定的所有事件
    getEventManager(moduleName).unbindAllEvent()
  },
  // 检测小程序绑定游戏退出钩子
  processExit (moduleName) {
    let instance = getInstance(moduleName)
    if (!instance) return
    instance.moduleEntry.processExit && instance.moduleEntry.processExit(getEventManager(moduleName))
  },

  // 创建窗口，默认beforeMount时自动调用
  async createWindow (instance, config, options = {}) {
    let preloadPath = path.join(instance.moduleDir, `./${instance.latestVersion}/app.asar/preload.js`)
    config.webPreferences = {
      preload: preloadPath,
      sandbox: false,
      nodeIntegration: false,
      webSecurity: false,
      ...config.webPreferences || {}
    }

    config.icon = path.join(__dirname, '../assets/images/favicon.ico')
    
    let use_inject = false, gameWin = null
    if (config.forceInject || checkMiniProgramCanInject(instance.__miniprogram_module_name__)) {
      gameWin = getInjectGameWindow(config.processName || options.processName)
      if (gameWin) {
        use_inject = true
      }
    }
    let node_inject = require('@heybox/node-inject').overlay
    if(!use_inject) {
      let readStoreSize = isMiniProWindowSizeNeedStore(config), rect
      if (readStoreSize) {
        rect = getMiniProWindowSize(instance)
        if (checkBoundInDisplay(rect, 0.5)) {
          config.width = rect.width
          config.height = rect.height
          config.x = rect.x
          config.y = rect.y
        }
      }
      instance.window = new BrowserWindow(config)
      if (readStoreSize) {
        if (rect && rect.max) {
          instance.window.maximize()
        }
        instance.window.on('will-resize', utils.debounce((_, newBounds) => {
          setMiniProWindowSize(instance.__miniprogram_module_name__, newBounds)
        }, 500))
        instance.window.on('will-move', utils.debounce((_, newBounds) => {
          setMiniProWindowSize(instance.__miniprogram_module_name__, newBounds)
        }, 500))
        instance.window.on('unmaximize', () => {
          setMiniProWindowSize(instance.__miniprogram_module_name__, {
            max: false
          })
        })
        instance.window.on('maximize', () => {
          setMiniProWindowSize(instance.__miniprogram_module_name__, {
            max: true
          })
        })
      }
    } else {
      instance.window = node_inject.createOffscreenWindow(config, gameWin)
    }
  
  
    let url = ''
    if (options.url) {
      url = options.url
    } else {
      url = `file://${path.join(instance.moduleDir, `${instance.latestVersion}/app.asar/index.html`)}`
    }
  
    if (options.querys) {
      let querys = '?' + Object.keys(options.querys).map((key) => {
        return `${key}=${options.querys[key]}`
      }).join('&')
      url += querys
    }
    instance.window.loadURL(url).then(() => {
      global.sendUserOpReport && global.sendUserOpReport(['miniprogram', instance.__miniprogram_module_name__])
    }).catch(e => {
      log.error('[miniprogram loadURL]', e)
    })
  
    setDefaultEvent(instance, config)
    
    if(use_inject) {
      try {
        // await new Promise((sub_resolve) => {
        //   instance.window.once('ready-to-show', () => {
        //     sub_resolve()
        //   })
        // })
        await node_inject.injectWindow({
          window: instance.window,
          gameWin,
          listenEvents: {
            "game.connection.close": (payload) => {
              log.info("[overlay event]: game.connection.close", payload);
              instance.window.destroy()
            }
          },
          caption: config.captionRect || {},
          rect: {
            x: config.x,
            y: config.y,
            width: config.width,
            height: config.height,
          },
          ...config.injectConfig || {}
        })
      } catch (e) {
        log.error('error')
        log.info(e)
      }
    } else {
      options.focus && instance.window && utils.focusWindow(instance.window)
    }
    if (options.devtools) {
      setTimeout(() => {
        instance.window?.webContents?.openDevTools()
      }, 1000)
    }
    
    instance.moduleEntry.afterMount && instance.moduleEntry.afterMount(instance.window)
    return instance.window
  },

  patchWindowEvent (eventName, args) {
    MINI_PROGRAM_INSTANCE_GROUP.forEach((ins) => {
      if (ins.window && !ins.window.isDestroyed()) {
        ins.window.webContents?.send(eventName, args)
      }
    })
  },
  getIsOverlayInstance () {
    return MINI_PROGRAM_INSTANCE_GROUP.filter((ins) => {
      return ins.options.isOverlay
    })
  },
  // black_myth_wukong.js 和 浮窗管理关闭开关时 会对指定moduleName的小程序执行关闭
  clearOverlayMiniProgram (moduleName) {
    let instance = getInstance(moduleName)
    if (!instance) return

    if (instance && instance.window && !instance.window.isDestroyed()) {
      instance.window.close()
    }

    // 如果未设置 manualCreateWindow, window触发close事件时会自动触发beforeDestroy
    // 如果设置里manualCreateWindow，需要在销毁小程序时手动触发beforeDestroy
    let config = getConfigJson(instance)
    if (config.manualCreateWindow) {
      this.beforeDestroy(moduleName)
    }
  },
  clearAllOverlayMiniProgram () {
    MINI_PROGRAM_INSTANCE_GROUP.forEach((ins) => {
      if (ins.options.isOverlay) {
        ins.window?.close()
        // 同clearOverlayMiniProgram中的manualCreateWindow判断逻辑
        let config = getConfigJson(ins)
        if (config.manualCreateWindow) {
          this.beforeDestroy(moduleName)
        }
      }
    })
  },
  async batchSetOverlayMiniProgramEnable(list) {
    let params = []
    for (let item of list) {
      let {moduleName, data = {}, config = {}} = item
      this.setOverlayMiniProgramEnable(moduleName, data, {...config, reject_axios: true})
      if (!config.reject_axios) {
        for (let key in data) {
          params.push({
            mini_pro_id: moduleName,
            key,
            value: String(data[key])
          })
        }
      }
    }
    if (params.length > 0) {
      let res = await request.$post('/chatroom/v2/minipro/settings', {}, {settings: params})
      console.log('batchSetOverlayMiniProgramEnable', params, res)
    }
  },
  async setOverlayMiniProgramEnable(moduleName, data = {}, config = {}) {
    log.info("[setOverlayMiniProgramEnable]", moduleName, data, config)
    setMiniProgramSetting(moduleName, data)

    let {need_close, reject_axios} = config
    if (!reject_axios) {
      for (let key in data) {
        if (key !== 'mini_pro_id') {
          let params = {
            mini_pro_id: moduleName,
            key,
            value: String(data[key])
          }
          try {
            let res = await request.$post('/chatroom/v2/minipro/setting', {}, params)
            console.log(params, res)
          } catch(e) {
            console.error(e)
          }
        }
      }
    }
    if (need_close) {
      MINI_PROGRAM.clearOverlayMiniProgram(moduleName)
    }
  },
  getInstance,
  getConfigJson,
  getInjectGameWindow,
  initInstance,
  getEventManager,
  setDefaultEvent,

  getIsMiniProgramCollected,
  addMiniProgramCollected,
  removeMiniProgramCollected,
}

// 执行初始化函数
init()


function init() {
  // 绑定km事件
  const {registerKmEvent} = require('./register_km_events')
  registerKmEvent()

  // 绑定eventBus事件
  const {bindEventBus} = require('./bind_eventbus')
  bindEventBus()

  const { startCheckTopWindowNeedNotice } = require('@heybox/ingame-notice')
  startCheckTopWindowNeedNotice()
}

function addErrorLog (error, sentryType = '', text, instance) {
  const errorText = `[miniprogram error]: ${instance.__miniprogram_module_name__}, ${text}${error ? ` , ${error.message}` : ''}`
  log.error(errorText)
  // if (sentryType === 'exception') {
  //   error.message = errorText
  //   Sentry.captureException(error)
  // } else if (sentryType === 'capture') {
  //   Sentry.captureMessage(errorText, { level: 'fatal' })
  // }
  LOADING.stop(instance.__miniprogram_module_name__)
}

function setDefaultEvent (instance, {manualCreateWindow}) {
  const EVENT_MANAGER = getEventManager(instance.__miniprogram_module_name__)
  const $ipc = EVENT_MANAGER.$ipc
  //窗口最小化 
  $ipc.on('MiniProgramSDK:minimizeWindow', (event) => {
    BrowserWindow.fromWebContents(event.sender).minimize()
  })
  //窗口最大化
  $ipc.on('MiniProgramSDK:maximizeWindow', (event) => {
    let window = BrowserWindow.fromWebContents(event.sender)
    if (window.isMaximized()) {
      window.unmaximize();
    } else {
      window.maximize();
    }
  })
  //窗口关闭
  $ipc.on('MiniProgramSDK:closeWindow', (event) => {
    BrowserWindow.fromWebContents(event.sender).close()
  })
  // 恢复主窗口焦点
  $ipc.on('MiniProgramSDK:focusMainWindow', () => {
    utils.focusWindow(global.mainWindow)
  })

  // 恢复主窗口
  $ipc.on('MiniProgramSDK:focusMainWindow', () => {
    utils.focusWindow(global.mainWindow)
  })

  // 小程序窗口置顶
  $ipc.on('MiniProgramSDK:setWindowTop', (e, v) => {
    let window = BrowserWindow.fromWebContents(e.sender)
    window.setAlwaysOnTop(v, 'screen-saver')
  })

  // 小程序收藏相关ipc
  $ipc.handle('MiniProgramSDK:getIsMiniProgramCollected', (e, mini_pro_id) => {
    return getIsMiniProgramCollected(mini_pro_id)
  })
  $ipc.on('MiniProgramSDK:addMiniProgramCollected', (e, mini_pro_id) => {
    addMiniProgramCollected(mini_pro_id)
  })
  $ipc.on('MiniProgramSDK:removeMiniProgramCollected', (e, mini_pro_id) => {
    removeMiniProgramCollected(mini_pro_id)
  })

  // store相关ipc
  $ipc.handle('MiniProgramSDK:getValueByKeys', (e, v) => {
    return store.getValueByKeys(v)
  })

  $ipc.handle('MiniProgramSDK:sendRequest', (e, { url, method, params, body, base, headers }) => {
    if (!base) {
      let urlObj = new URL(url)
      if (urlObj.hostname.endsWith('.debugmode.cn') || urlObj.hostname.endsWith('.xiaoheihe.cn')) {
        if (urlObj.hostname.startsWith('chat')) {
          base = 'heychat'
        } else {
          base = 'heybox'
        }
      } else {
        base = 'external'
      }
    }
    return request.$http(url, method, params, body, base, headers)
  })

  if (!manualCreateWindow) {
    instance.window.on('closed', () => {
      MINI_PROGRAM.beforeDestroy(instance.__miniprogram_module_name__)
      instance = null
    })
  }

  instance.window.on('maximize', () => {
    instance.window.webContents.send('MiniProgramSDK:update-window-size', true)
  })
  instance.window.on('unmaximize', () => {
    instance.window.webContents.send('MiniProgramSDK:update-window-size', false)
  })

  instance.window.webContents.setWindowOpenHandler(({url}) => {
    shell.openExternal(url)
  })
}

// 加载小程序instance
function initInstance(moduleName, successCallback, config = {}) {
  let {showLoading = true, addInstanceGroup = true} = config
  if (!MINI_PROGRAM_INIT_INSTANCE_PROMISE[moduleName]) {
    MINI_PROGRAM_INIT_INSTANCE_PROMISE[moduleName] = new Promise(async (resolve, reject) => {
      let ins = getInstance(moduleName)
      if (ins) {
        resolve(ins)
        return
      }
  
      let instance = {
        __miniprogram_module_name__: moduleName,
        moduleDir: path.join(global.miniprogram_base_dir, `miniprogram/${moduleName}`),
        options: {},
      }
      addInstanceGroup && MINI_PROGRAM_INSTANCE_GROUP.push(instance)
  
      let rejectFunc = (error, sentryType, text, errorText) => {
        addErrorLog(error, sentryType, text, instance)
        addInstanceGroup && clearInstanceGroup(instance)
        reject(errorText)
      }
  
      await ensureDirExists(instance.moduleDir).catch(({ error, errorText }) => {
        rejectFunc(error, 'exception', `ensureDirExists error, ${errorText}`, errorText)
      })
  
      let res = null
      try {
        res = await request.$post('/heyboxpc/app/mini_program_file', {}, {
          mini_program_id: instance.__miniprogram_module_name__,
          bit: global.EXE_BIT,
          electron_version: global.ELECTRON_VERSION,
        }, 'heybox', {
          'content-type': 'application/json;charset=utf-8',
        })
        console.log(res)
      } catch (error) {
        rejectFunc(error, 'exception', `network error`, ERRORS.NETWORK_ERROR)
        return
      }
  
      if (res.status === 'ok') {
        // 若存在本地版本
        if (await pathExists(path.join(instance.moduleDir, `${res.result.version}/app.asar`))) {
          try {
            instance.latestVersion = res.result.version
            instance.moduleEntry = require(path.join(instance.moduleDir, `./${instance.latestVersion}/app.asar/main.js`))
            successCallback(instance)
            resolve(instance)
          } catch (error) {
            rejectFunc(error, 'exception', `load error`, ERRORS.DEFAULT)
          }
        } else {
          console.log(res.result)
          // 不存在本地版本
          if (res.result.file && res.result.version) {
            res.result.version = String(res.result.version)
            if (!res.result.quiet_update && showLoading) {
              LOADING.start(moduleName)
            }
            let zipPath = `${instance.moduleDir}\\${res.result.version}.zip`
            let lowSpeedDuration = 0
            const { LOW_SPEED_CHECK_TIME } = require('./assets/constant')
            downloadFile(
              res.result.file,
              zipPath,
              undefined,
              res.result.version,
              async (action, param) => {
                if (action === 'success') {
                  try {
                    instance.latestVersion = res.result.version
                    let versionPath = path.join(`${instance.moduleDir}`, instance.latestVersion)
                    await ensureDirExists(versionPath).catch(({ error, errorText }) => {
                      rejectFunc(error, 'exception', `ensureDirExists error, ${errorText}`, errorText)
                    })
                    await extractZip(zipPath, versionPath).catch(({ error, errorText }) => {
                      rejectFunc(error, 'exception', `extractZip error, ${errorText}`, errorText)
                    })
                    await removeFile(zipPath)
                    instance.moduleEntry = require(path.join(instance.moduleDir, `./${instance.latestVersion}/app.asar/main.js`))
  
                    successCallback(instance)
  
                    showLoading && LOADING.stop(moduleName, true)
                    
                    // 删除多余版本
                    let { children: versions } = await walkDir(instance.moduleDir);
                    versions.forEach(async (v) => {
                      if (v.name != instance.latestVersion) {
                        try {
                          await removeFile(path.join(instance.moduleDir, v.name))
                        } catch (error) {
                          log.error(`[miniprogram]: rm ${instance.__miniprogram_module_name__} error, ${error.message}`)
                        }
                      }
                    })
                    resolve(instance)
                  } catch (error) {
                    rejectFunc(error, 'exception', `load remote error`, ERRORS.DEFAULT)
                  }
                } else if (action === 'error') {
                  await removeFile(zipPath)
                  rejectFunc(undefined, 'exception', `download error, ${param}`, ERRORS.DOWNLOAD_FAILED)
                } else if (action === 'progress') {
                  if (LOADING.windows[moduleName] && showLoading) {
                    LOADING.windows[moduleName].webContents.send('MiniProgramSDK:loadingProgress', param)
                    
                    let rate = param.rate
                    if (!main_config.never_show_dnc_warn && lowSpeedDuration >= 0) {
                      if (rate < main_config.low_download_speed_threshold * 1024) {
                        lowSpeedDuration += 0.5;
                      } else {
                        lowSpeedDuration = 0;
                      }
                      if (lowSpeedDuration >= LOW_SPEED_CHECK_TIME) {
                        mainWindow?.webContents.send('dns-change-warn', 'download');
                        lowSpeedDuration = -1;
                      }
                    }
                  }
                }
              }
            )
          } else {
            rejectFunc(undefined, 'capture', `version error, ${res.result.version}`, ERRORS.INVALID_VERSION)
  
          }
        }
      } else {
        rejectFunc(undefined, 'capture', `request error, ${res.msg}`, res.msg || ERRORS.REQUEST_ERROR)
      }
    }).finally(() => {
      delete MINI_PROGRAM_INIT_INSTANCE_PROMISE[moduleName]
    })
  } else {
    return MINI_PROGRAM_INIT_INSTANCE_PROMISE[moduleName].then((instance) => {
      if (!getInstance(moduleName) && addInstanceGroup) {
        MINI_PROGRAM_INSTANCE_GROUP.push(instance)
      }
      successCallback(instance)
      return instance
    })
  }
}

function getInstance(moduleName) {
  return MINI_PROGRAM_INSTANCE_GROUP.find((i) => i.__miniprogram_module_name__ === moduleName)
}

function clearInstanceGroup (instance) {
  if (!instance) return
  let index = MINI_PROGRAM_INSTANCE_GROUP.findIndex((_) => _.__miniprogram_module_name__ === instance.__miniprogram_module_name__)
  if (index >= 0) {
    MINI_PROGRAM_INSTANCE_GROUP.splice(index, 1)
  }
}

function setMiniProgramSetting(moduleName, data) {
  let miniprogram_setting = store.get('miniprogram_setting') || []
  let index = miniprogram_setting.findIndex(({mini_pro_id}) => mini_pro_id === moduleName)
  if (index !== -1) {
    miniprogram_setting[index] = {
      ...miniprogram_setting[index],
      ...data
    }
  } else {
    miniprogram_setting.push({
      "mini_pro_id": moduleName,
      ...data
    })
  }
  store.set('miniprogram_setting', miniprogram_setting, true)
}

// 检测小程序是否允许注入
function checkMiniProgramCanInject(mini_pro_id) {
  let inject_config = getConfigInDisplayConfig(mini_pro_id, config => config.is_inject_shortcut)?.[0]
  if (inject_config) {
    return store.getValueByKeys({
      key: inject_config.key ? 'main_config.' + inject_config.key : null,
      default_value_from_key: inject_config.default_value_from_key ? 'main_config.' + inject_config.default_value_from_key : inject_config.default_value_from_key,
      default_value: inject_config.default_value
    })
  }
  return false
}
module.exports = MINI_PROGRAM