const request = require('@heybox/electron-utils/request')
const {
  matchOfficialGame,
  getHigherPriorityProcessMatch,
} = require('@heybox/process-iterate/match')
let appid_process_list_cache = {}, appid_process_map_cache = {}

/**
 * 根据小程序 ID 获取绑定的进程列表。
 * 支持使用缓存，如果指定 `noCache` 为 `true`，则会强制刷新数据。
 * 
 * @param {string} mini_pro_id - 小程序的唯一标识 ID。
 * @param {boolean} noCache - 是否忽略缓存，默认使用缓存。
 * @returns {Promise<Array>} 返回包含进程信息的数组。
 * 
 * 示例返回数据结构：
 * [
 *   {
      process_name: "dota2",
      window_name: "dota 2",
      appid: 570,
      level: 1, // 没有level字段标识优先级为0
    }
 *   ...
 * ]
 */
async function getMultBindProcessByMiniProId(mini_pro_id, noCache) {
  if (appid_process_list_cache[mini_pro_id] && !noCache) {
    return appid_process_list_cache[mini_pro_id]
  }
  const url = '/chatroom/v2/minipro/process'
  try {
    let res = await request.$get(url, { mini_pro_id })
    if (res.status === 'ok') {
      appid_process_list_cache[mini_pro_id] = res.result.process
      return appid_process_list_cache[mini_pro_id]
    }
  } catch (e) {
    console.error(e);
    return []
  }
}

/**
 * 根据窗口信息和小程序 ID 匹配相关进程。
 * 
 * @param {Array} windows - 窗口信息的数组，每个元素结构如下：
 *   {
 *     进程名字段二选一，兼容c++ api
 *     processName: "process_name.exe",
 *     process_name: "process_name.exe",
 * 
 *     窗口名字段二选一，兼容c++ api
 *     title: "Window Title"
 *     window_name: "Window Title"
 *   }
 * @param {string} mini_pro_id - 小程序的唯一标识 ID。
 * @param {boolean} noCache - 是否忽略缓存，默认使用缓存。
 * @returns {Promise<Object>} 返回匹配的进程和窗口信息对象，结构如下：
 *   {
 *     process_data: { 
        process_name: "dota2",
        window_name: "dota 2",
        appid: 570,
        need_admin: true, // 没有说明是false
 *    }, // 匹配的进程数据
 *     window_item: {
        windowId: 10490734,
        processId: 43248,
        title: "Dota 2",
        processPath: "D:\\steam\\steamapps\\common\\dota 2 beta\\game\\bin\\win64\\dota2.exe",
        processName: "dota2.exe",
 *    }  // 匹配的窗口数据
 *   }
 */
async function getMatchMiniProProcessByWindows(windows, mini_pro_id, noCache) {
  if (!appid_process_map_cache[mini_pro_id] || noCache) {
    const process_list = await getMultBindProcessByMiniProId(mini_pro_id, noCache)
    const process_map = {}
    process_list.forEach(item => {
      process_map[item.process_name] = item
    })
    appid_process_map_cache[mini_pro_id] = process_map
  }
  let final_match_result = null
  windows.forEach(window_item => {
    const processItem = {
      process_name: window_item.processName || window_item.process_name || ''
    }
    const game_data = matchOfficialGame(processItem, appid_process_map_cache[mini_pro_id])
    if(game_data) {
      game_data._process_data = appid_process_map_cache[mini_pro_id][game_data.match_process_name]
      game_data._window_item = window_item
      final_match_result = getHigherPriorityProcessMatch(final_match_result, game_data)
    }
  })
  return {
    process_data: final_match_result?._process_data,
    window_item: final_match_result?._window_item
  }
}

module.exports = {
  getMultBindProcessByMiniProId,
  getMatchMiniProProcessByWindows,
}