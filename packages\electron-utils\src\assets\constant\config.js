module.exports = {
  MAIN_DEFAULT_CONFIG: {
    setting_data: {
      auto_launch: undefined, // 开机自启
      auto_launch_hide: undefined, // 启动时自动最小化
      keep_login_state: true, // 是否保持登录，web为30天，客户端为持久化
      hardware_acceleration: true, // 是否开启硬件加速
      ignore_proxy: false, // 是否忽略系统代理
      close_event: 'hide', // hide为关闭窗口，隐藏到任务栏托盘，close为退出主程序
    }
  },
  CHAT_DEFAULT_CONFIG: {
  },
  ACC_DEFAULT_CONFIG: {
  },
  LIBRARY_DEFAULT_CONFIG: {
  },
  KM_CONFIG: {
    "black_myth_xbox_map_key": ['GAMEPAD_RIGHT_SHOULDER', 'RIGHT_TRIGGER'], // 黑神话xbox手柄地图快捷键
    "black_myth_ps_map_key": ['GAMEPAD_RIGHT_SHOULDER', 'RIGHT_TRIGGER'], // 黑神话ps手柄地图快捷键
    "black_myth_map_key": [77], // 黑神话地图快捷键 m
    "black_myth_hide_key": [113], // 黑神话地图隐藏快捷键  f2
    "cancel_key": [27], // 取消快捷键 esc
    "miniprogram_hide_key": [113], // 小程序浮窗 隐藏快捷键  f2
    "miniprogram_cancel_key": [27], // 小程序浮窗 取消快捷键 esc
    "roulette_switch_key": [18,192], // 语音轮盘默认快捷键
    "miniprogram_overlay_key": [115], // 浮窗管理 开关浮窗快捷键 默认f4
    "miniprogram_detect_key": '', // 硬件监测 开关监测快捷键 默认不配置
    "crosshair_show_key": [121], // 准星工具显示快捷键 默认f10
    "crosshair_switch_key": [2], // 准星工具切换开镜准星快捷键 默认 鼠标右键
    "recorder_key": '', // 屏幕录制快捷键

    continuous_key_list: [ // 幻兽帕鲁连点器，默认配置
      {
        "name": "幻兽帕鲁自动长按",
        "trigger_key": [
          112
        ],
        "executed_key": [
          70
        ],
        "enable": true,
        "mode": 0,
        "interval": 100,
        "random_interval": 0,
        "key": 1707122971651
      }
    ],
  },
  GLOBAL_CONFIG: { // mock数据，后续在后端存储
    "auto_fill_team_appids": [
      1,
      2,
      3
    ],
    "switches": {
      "hardware_detect_monitor": true,
      "report_op_mem": false,
      "upload_bmw_point": true,
      "upload_inject_dump": true,
      "use_shared_texture": true,
    },
  }
}