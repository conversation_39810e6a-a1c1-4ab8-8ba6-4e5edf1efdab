; ****************************************************************************
; * Copyright (C) 2002-2014 OpenVPN Technologies, Inc.                            *
; *  This program is free software; you can redistribute it and/or modify    *
; *  it under the terms of the GNU General Public License version 2          *
; *  as published by the Free Software Foundation.                           *
; ****************************************************************************

; SYNTAX CHECKER
; cd \WINDDK\3790\tools\chkinf
; chkinf c:\src\openvpn\tap-win32\i386\oemvista.inf
; OUTPUT -> file:///c:/WINDDK/3790/tools/chkinf/htm/c%23+src+openvpn+tap-win32+i386+__OemWin2k.htm

; INSTALL/REMOVE DRIVER
;   tapinstall install OemVista.inf tapoas
;   tapinstall update OemVista.inf tapoas
;   tapinstall remove tapoas

;*********************************************************
; Note to Developers:
;
; If you are bundling the TAP-Windows driver with your app,
; you should try to rename it in such a way that it will
; not collide with other instances of TAP-Windows defined
; by other apps.  Multiple versions of the TAP-Windows
; driver, each installed by different apps, can coexist
; on the same machine if you follow these guidelines.
; NOTE: these instructions assume you are editing the
; generated OemWin2k.inf file, not the source
; OemWin2k.inf.in file which is preprocessed by winconfig
; and uses macro definitions from settings.in.
;
; (1) Rename all tapXXXX instances in this file to
;     something different (use at least 5 characters
;     for this name!)
; (2) Change the "!define TAP" definition in openvpn.nsi
;     to match what you changed tapXXXX to.
; (3) Change TARGETNAME in SOURCES to match what you
;     changed tapXXXX to.
; (4) Change TAP_COMPONENT_ID in common.h to match what
;     you changed tapXXXX to.
; (5) Change SZDEPENDENCIES in service.h to match what
;     you changed tapXXXX to.
; (6) Change DeviceDescription and Provider strings.
; (7) Change PRODUCT_TAP_WIN_DEVICE_DESCRIPTION in constants.h to what you
;     set DeviceDescription to.
;
;*********************************************************

[Version]
   Signature = "$Windows NT$"
   CatalogFile = tap0901.cat
   ClassGUID = {4d36e972-e325-11ce-bfc1-08002be10318}
   Provider = %Provider%
   Class = Net

; This version number should match the version
; number given in SOURCES.
   DriverVer=04/21/2016,**********

[Strings]
   DeviceDescription = "TAP-Windows Adapter V9"
   Provider = "TAP-Windows Provider V9"

;----------------------------------------------------------------
;                      Manufacturer + Product Section (Done)
;----------------------------------------------------------------
[Manufacturer]
   %Provider% = tap0901, NTamd64

[tap0901.NTamd64]
   %DeviceDescription% = tap0901.ndi, root\tap0901 ; Root enumerated
   %DeviceDescription% = tap0901.ndi, tap0901      ; Legacy

;---------------------------------------------------------------
;                             Driver Section (Done)
;---------------------------------------------------------------

;----------------- Characteristics ------------
;    NCF_PHYSICAL = 0x04
;    NCF_VIRTUAL = 0x01
;    NCF_SOFTWARE_ENUMERATED = 0x02
;    NCF_HIDDEN = 0x08
;    NCF_NO_SERVICE = 0x10
;    NCF_HAS_UI = 0x80
;----------------- Characteristics ------------

[tap0901.ndi]
   CopyFiles       = tap0901.driver, tap0901.files
   AddReg          = tap0901.reg
   AddReg          = tap0901.params.reg
   Characteristics = 
   *IfType            = 0x6 ; IF_TYPE_ETHERNET_CSMACD
   *MediaType         = 0x0 ; NdisMedium802_3
   *PhysicalMediaType = 14  ; NdisPhysicalMedium802_3

[tap0901.ndi.Services]
   AddService = tap0901,        2, tap0901.service

[tap0901.reg]
   HKR, Ndi,            Service,      0, "tap0901"
   HKR, Ndi\Interfaces, UpperRange,   0, "ndis5"
   HKR, Ndi\Interfaces, LowerRange,   0, "ethernet"
   HKR, ,               Manufacturer, 0, "%Provider%"
   HKR, ,               ProductName,  0, "%DeviceDescription%"

[tap0901.params.reg]
   HKR, Ndi\params\MTU,                  ParamDesc, 0, "MTU"
   HKR, Ndi\params\MTU,                  Type,      0, "int"
   HKR, Ndi\params\MTU,                  Default,   0, "1500"
   HKR, Ndi\params\MTU,                  Optional,  0, "0"
   HKR, Ndi\params\MTU,                  Min,       0, "100"
   HKR, Ndi\params\MTU,                  Max,       0, "1500"
   HKR, Ndi\params\MTU,                  Step,      0, "1"
   HKR, Ndi\params\MediaStatus,          ParamDesc, 0, "Media Status"
   HKR, Ndi\params\MediaStatus,          Type,      0, "enum"
   HKR, Ndi\params\MediaStatus,          Default,   0, "0"
   HKR, Ndi\params\MediaStatus,          Optional,  0, "0"
   HKR, Ndi\params\MediaStatus\enum,     "0",       0, "Application Controlled"
   HKR, Ndi\params\MediaStatus\enum,     "1",       0, "Always Connected"
   HKR, Ndi\params\MAC,                  ParamDesc, 0, "MAC Address"
   HKR, Ndi\params\MAC,                  Type,      0, "edit"
   HKR, Ndi\params\MAC,                  Optional,  0, "1"
   HKR, Ndi\params\AllowNonAdmin,        ParamDesc, 0, "Non-Admin Access"
   HKR, Ndi\params\AllowNonAdmin,        Type,      0, "enum"
   HKR, Ndi\params\AllowNonAdmin,        Default,   0, "1"
   HKR, Ndi\params\AllowNonAdmin,        Optional,  0, "0"
   HKR, Ndi\params\AllowNonAdmin\enum,   "0",       0, "Not Allowed"
   HKR, Ndi\params\AllowNonAdmin\enum,   "1",       0, "Allowed"

;----------------------------------------------------------------
;                             Service Section
;----------------------------------------------------------------

;---------- Service Type -------------
;    SERVICE_KERNEL_DRIVER     = 0x01
;    SERVICE_WIN32_OWN_PROCESS = 0x10
;---------- Service Type -------------

;---------- Start Mode ---------------
;    SERVICE_BOOT_START   = 0x0
;    SERVICE_SYSTEM_START = 0x1
;    SERVICE_AUTO_START   = 0x2
;    SERVICE_DEMAND_START = 0x3
;    SERVICE_DISABLED     = 0x4
;---------- Start Mode ---------------

[tap0901.service]
   DisplayName = %DeviceDescription%
   ServiceType = 1
   StartType = 3
   ErrorControl = 1
   LoadOrderGroup = NDIS
   ServiceBinary = %12%\tap0901.sys

;-----------------------------------------------------------------
;                                File Installation
;-----------------------------------------------------------------

;----------------- Copy Flags ------------
;    COPYFLG_NOSKIP = 0x02
;    COPYFLG_NOVERSIONCHECK = 0x04
;----------------- Copy Flags ------------

; SourceDisksNames
; diskid = description[, [tagfile] [, <unused>, subdir]]
; 1 = "Intel Driver Disk 1",e100bex.sys,,

[SourceDisksNames]
   1 = %DeviceDescription%, tap0901.sys

; SourceDisksFiles
; filename_on_source = diskID[, [subdir][, size]]
; e100bex.sys = 1,, ; on distribution disk 1

[SourceDisksFiles]
tap0901.sys = 1

[DestinationDirs]
   tap0901.files  = 11
   tap0901.driver = 12

[tap0901.files]
;   TapPanel.cpl,,,6   ; COPYFLG_NOSKIP | COPYFLG_NOVERSIONCHECK
;   cipsrvr.exe,,,6     ; COPYFLG_NOSKIP | COPYFLG_NOVERSIONCHECK

[tap0901.driver]
   tap0901.sys,,,6     ; COPYFLG_NOSKIP | COPYFLG_NOVERSIONCHECK

;---------------------------------------------------------------
;                                      End
;---------------------------------------------------------------
