<template>
  <transition name="popover-fade" mode="out-in">
    <div v-if="show && !disable" class="cpt-popover-wrapper" ref="popoverContent" @mousedown="handleMouseDown" :style="posStyle">
      <slot></slot>
      <div v-if="arrowPlacement" :x-placement="arrowPlacement" class="popover-arrow"></div>
    </div>
  </transition>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'

const props = defineProps({
  triggerRef: {
    default: null
  },
  triggerType: {
    type: String,
    default: 'hover'
  },
  hideDelay: {
    type: Number,
    default: 360
  },
  showDelay: {
    type: Number,
    default: 0
  },
  disable: {
    type: Boolean,
    default: false,
  },
  disabledHidden: {
    type: Boolean,
    default: false,
  },
  noHiddenClass: {
    type: String,
    default: ''
  },
  clickHidden: { // 在popover已经展示时，再次点击是否隐藏
    type: Boolean,
    default: false,
  },
  arrowPlacement: {
    type: String,
    default: ''
  },
  appendToBody: {
    type: Boolean,
    default: true
  },
  placement: {
    type: String,
    default: 'bottom'
  },
  offset: {
    type: Object,
    default: () => ({
      x: 0,
      y: 0
    })
  },
  absolutePos: {
    type: Object,
    default: null
  }
})

const POPOVER_GAP = 10

const emit = defineEmits(['updateShow'])

const init = ref(false)
const show = ref(false)
const lockContentShow = ref(false)
const animationTime = ref(0)
const hoverLock = ref(false)
const top = ref('')
const left = ref('')
const bottom = ref('')
const right = ref('')
const zIndex = ref(101)
const showPopoverHandle = ref(null)
const hidePopoverHandle = ref(null)
const popoverContent = ref(null)
const contentRef = ref(null)
const timer = ref(null)
const _showPopoverTimer = ref(null)
const waitHide = ref(false)
const noHiddenAllEls = ref(null)
const _hoverEnterEvent = ref(null)
const _hoverLeaveEvent = ref(null)
const _hoverOverEvent = ref(null)

// Computed
const posStyle = computed(() => ({
  top: top.value || '-1000px',
  left: left.value || '-1000px',
  right: right.value,
  bottom: bottom.value,
  zIndex: zIndex.value
}))

const getNextZIndex = () => {
  // 简单的z-index管理，你可以根据需要实现更复杂的逻辑
  return ++zIndex.value
}

const initEvent = () => {
  if (!props.triggerRef) {
    return
  }
  let trigger = props.triggerRef.$el || props.triggerRef
  if (props.triggerType === 'click') {
    trigger.addEventListener('click', clickShowEvent)
    if (!window.heyboxPopoverClickEventHandler) {
      window.heyboxPopoverClickEventHandler = []
      document.documentElement.addEventListener('click', _clickEvtFunc, true)
    }
  } else {
    noHiddenAllEls.value = props.noHiddenClass && document.querySelectorAll('.' + props.noHiddenClass)
    _hoverEnterEvent.value = showPopover.bind(null, 'trigger')
    noHiddenAllEls.value && noHiddenAllEls.value.forEach(item => {
      item.addEventListener('mouseenter', _hoverEnterEvent.value)
    })
    _hoverLeaveEvent.value = hidePopover.bind(null)
    _hoverOverEvent.value = overPopover.bind(null)
    trigger.addEventListener('mouseenter', _hoverEnterEvent.value)
    trigger.addEventListener('mouseleave', _hoverLeaveEvent.value)
    // 防止triggerRef的按钮和popover的最外层dom距离很近时
    // 快速把鼠标从popover滑动到按钮，会只触发mouseleave不触发mouseenter
    // 增加mousemove事件托底
    trigger.addEventListener('mousemove', _hoverOverEvent.value)
  }
}

const clickShowEvent = () => {
  console.log('clickShowEvent')
  if(props.disable) return
  if (props.clickHidden) {
    if (!lockContentShow.value) {
      showPopover()
    }
  } else {
    showPopover()
  }
}

const showPopover = (path, force) => {
  const func = () => {
    // 防止triggerType = hover时，在hide transition动画时间内重新hover后会再次触发展示
    if ((path === 'content' || props.triggerType === 'click') && (props.triggerType === 'hover' && lockContentShow.value)) return
    
    setTimeout(() => {
      calcContentPos()
    })
    
    clearTimeout(timer.value)
    timer.value = null
    show.value = true
    emit('updateShow', true)
    
    nextTick(() => {
      contentRef.value = popoverContent.value
      if (!contentRef.value) return
      if (props.appendToBody) document.body.appendChild(contentRef.value)
      
      if (props.triggerType === 'hover') {
        if (showPopoverHandle.value || hidePopoverHandle.value) return
        showPopoverHandle.value = showPopover.bind(null, 'content')
        hidePopoverHandle.value = hidePopover.bind(null)
        contentRef.value.addEventListener('mouseenter', showPopoverHandle.value)
        contentRef.value.addEventListener('mouseleave', hidePopoverHandle.value)
      }
    })
    
    if (props.triggerType === 'click') {
      if (!window.heyboxPopoverClickEventHandler) {
        window.heyboxPopoverClickEventHandler = []
      }
      window.heyboxPopoverClickEventHandler.push({
        _clickSelfEvt: _clickSelfEvt
      })
    }
  }

  if (!props.showDelay || force === true) {
    func()
  } else {
    clearTimeout(_showPopoverTimer.value)
    clearTimeout(timer.value)
    timer.value = null
    _showPopoverTimer.value = setTimeout(() => {
      func()
    }, props.showDelay)
  }
}

const calcContentPos = () => {
  let trigger = props.triggerRef.$el || props.triggerRef
  if (trigger) {
    zIndex.value = getNextZIndex()
    const rect = trigger.getBoundingClientRect()
    const offset = Object.assign({ x: 0, y: 0 }, props.offset)
    const contentRect = popoverContent.value.getBoundingClientRect()
    
    if(props.placement) {
      switch (props.placement) {
        case 'bottom':
          left.value = rect.left- (contentRect.width - rect.width) / 2 + offset.x + 'px'
          top.value = rect.top + rect.height + POPOVER_GAP + offset.y + 'px'
          break
        case 'bottom-start': 
          left.value = rect.left + offset.x + 'px'
          top.value = rect.top + rect.height + POPOVER_GAP + offset.y + 'px'
          break
        case 'bottom-end':
          left.value = rect.left - (contentRect.width - rect.width) + offset.x  + 'px'
          top.value = rect.top + rect.height + POPOVER_GAP + offset.y + 'px'
          break
        case 'top':
          left.value = rect.left- (contentRect.width - rect.width) / 2 + offset.x + 'px'
          top.value = rect.top - contentRect.height - POPOVER_GAP + offset.y + 'px'
          break
        case 'top-start': 
          left.value = rect.left + offset.x + 'px'
          top.value = rect.top - contentRect.height - POPOVER_GAP + offset.y + 'px'
          break
        case 'top-end':
          left.value = rect.left - (contentRect.width - rect.width) + offset.x  + 'px'
          top.value = rect.top - contentRect.height - POPOVER_GAP + offset.y + 'px'
          break
        case 'left':
          left.value = rect.left - contentRect.width - POPOVER_GAP + offset.x + 'px'
          top.value = rect.top - (contentRect.height - rect.height) / 2 + offset.y + 'px'
          break
        case 'left-start':
          left.value = rect.left - contentRect.width - POPOVER_GAP + offset.x + 'px'
          top.value = rect.top + offset.y + 'px'
          break
        case 'left-end':
          left.value = rect.left - contentRect.width - POPOVER_GAP + offset.x + 'px'
          top.value = rect.top - (contentRect.height - rect.height) + offset.y + 'px'
          break
        case 'right':
          left.value = rect.left + rect.width + POPOVER_GAP + offset.x + 'px'
          top.value = rect.top - (contentRect.height - rect.height) / 2 + offset.y + 'px'
          break 
        case 'right-start':
          left.value = rect.left + rect.width + POPOVER_GAP + offset.x + 'px'
          top.value = rect.top + offset.y + 'px'
          break
        case 'right-end':
          left.value = rect.left + rect.width + POPOVER_GAP + offset.x + 'px'
          top.value = rect.top - (contentRect.height - rect.height) + offset.y + 'px'
          break
        default:
          break
      }
    }
    if(!props.appendToBody) {
      left.value = offset.x + 'px'
      top.value = offset.y + POPOVER_GAP + 'px'
    }
    
    if (props.absolutePos) {
      if(props.absolutePos.left) left.value = props.absolutePos.left + 'px'
      if(props.absolutePos.top) top.value = props.absolutePos.top + 'px'
      if(props.absolutePos.right) right.value = props.absolutePos.right + 'px'
      if(props.absolutePos.bottom) bottom.value = props.absolutePos.bottom + 'px'
    }
  }
}

const overPopover = (e) => {
  // 如果popover处于展示状态，并且没有关闭的timer，不需要重复调用showPopover
  if (show.value && !timer.value) return
  showPopover()
}

const hidePopover = (e) => {
  if (!hoverLock.value) {
    clearTimeout(_showPopoverTimer.value)
    clearTimeout(timer.value)
    timer.value = null
    timer.value = setTimeout(() => {
      if (!contentRef.value) return
      showPopoverHandle.value && contentRef.value.removeEventListener('mouseenter', showPopoverHandle.value)
      hidePopoverHandle.value && contentRef.value.removeEventListener('mouseleave', hidePopoverHandle.value)
      showPopoverHandle.value = null
      hidePopoverHandle.value = null
      hidden()
    }, props.hideDelay)
    waitHide.value = false
  } else {
    waitHide.value = true
  }
}

const hidden = () => {
  show.value = false
  emit('updateShow', false)
  lockContentShow.value = true
  setTimeout(() => {
    lockContentShow.value = false
  }, animationTime.value)
  if (props.triggerType === 'click') {
    deleteShownInstance()
  }
}

const deleteShownInstance = () => {
  let index = -1
  for (let i = 0; i < window.heyboxPopoverClickEventHandler?.length; i++) {
    if (window.heyboxPopoverClickEventHandler[i]._clickSelfEvt === _clickSelfEvt) {
      index = i
      break
    }
  }
  if (index >= 0) {
    window.heyboxPopoverClickEventHandler.splice(index, 1)
  }
}

const unbindEvent = (ref) => {
  ref = ref || props.triggerRef
  if (!ref) return
  let trigger = ref.$el || ref
  if (props.triggerType === 'hover') {
    trigger.removeEventListener('mouseenter', _hoverEnterEvent.value)
    trigger.removeEventListener('mouseleave', _hoverLeaveEvent.value)
    trigger.removeEventListener('mousemove', _hoverOverEvent.value)

    noHiddenAllEls.value && noHiddenAllEls.value.forEach(item => {
      item.removeEventListener('mouseenter', _hoverEnterEvent.value)
    })
  } else if (props.triggerType === 'click') {
    trigger.removeEventListener('click', clickShowEvent)
  }
  deleteShownInstance()
}

const handleMouseDown = (e) => {
  if (e.button === 0 && show.value) {
    hoverLock.value = true
  }
}

const handleMouseUp = (e) => {
  if (e.button === 0 && hoverLock.value && show.value) {
    hoverLock.value = false
  }
}

const _clickSelfEvt = (e) => {
  const noHiddenAllEls = props.noHiddenClass && document.querySelectorAll('.' + props.noHiddenClass)
  noHiddenAllEls && noHiddenAllEls.forEach(el => {
    if (el.contains(e.target)) {
      return
    }
  })
  if (!props.disabledHidden && contentRef.value && !contentRef.value.contains(e.target)) {
    hidden()
  }
}

const _clickEvtFunc = (e) => {
  if (window.heyboxPopoverClickEventHandler && window.heyboxPopoverClickEventHandler.length) {
    window.heyboxPopoverClickEventHandler.forEach(inst => {
      inst._clickSelfEvt(e)
    })
  }
}

onMounted(() => {
  if (props.triggerRef) {
    initEvent()
  }
  window.addEventListener("mouseup", handleMouseUp)
})

onBeforeUnmount(() => {
  unbindEvent()
  window.removeEventListener("mouseup", handleMouseUp)
  if (props.appendToBody && contentRef.value && contentRef.value.parentNode) {
    contentRef.value.parentNode.removeChild(contentRef.value)
  }
})

watch(() => props.triggerRef, (n, o) => {
  if (n && o === null) {
    initEvent()
  } else if (o !== null && n !== o) {
    unbindEvent(o)
    initEvent()
  }
})

watch(hoverLock, (v) => {
  if (!v && waitHide.value) {
    hidePopover()
  }
})
</script>

<style lang="scss">
.cpt-popover-wrapper {
  position: absolute;
  
  .popover-arrow {
    border-width: 6px;
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    
    &[x-placement^=top] {
      bottom: -6px;
      border-bottom-width: 0;
    }
    
    &[x-placement^=bottom] {
      top: -6px;
      border-top-width: 0;
    }
    
    &[x-placement^=right] {
      left: -6px;
      border-left-width: 0;
    }
    
    &[x-placement^=left] {
      right: -6px;
      border-right-width: 0;
    }
  }
}
</style>