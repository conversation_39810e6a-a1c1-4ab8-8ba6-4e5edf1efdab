import { onBeforeUnmount, ref } from 'vue'
import eventBus from './eventBus'

/**
 * Vue组合式API的事件总线封装
 * 提供响应式支持和自动清理功能
 */
export function useEventBus() {
  // 存储事件监听器，用于组件卸载时自动清理
  const listeners = ref(new Map())

  /**
   * 发射事件
   * @param {string} type 事件类型
   * @param {any} data 事件数据
   */
  const emit = (type, data) => {
    eventBus.emit(type, data)
  }

  /**
   * 监听事件（带自动清理）
   * @param {string} type 事件类型
   * @param {Function} handler 事件处理函数
   * @param {boolean} autoCleanup 是否自动清理（默认true）
   */
  const on = (type, handler, autoCleanup = true) => {
    eventBus.on(type, handler)
    
    if (autoCleanup) {
      // 记录监听器以便后续清理
      if (!listeners.value.has(type)) {
        listeners.value.set(type, [])
      }
      listeners.value.get(type).push(handler)
    }
  }

  /**
   * 移除事件监听
   * @param {string} type 事件类型
   * @param {Function} handler 事件处理函数
   */
  const off = (type, handler) => {
    eventBus.off(type, handler)
    
    // 从记录中移除
    const typeListeners = listeners.value.get(type)
    if (typeListeners) {
      const index = typeListeners.indexOf(handler)
      if (index > -1) {
        typeListeners.splice(index, 1)
        if (typeListeners.length === 0) {
          listeners.value.delete(type)
        }
      }
    }
  }

  /**
   * 监听一次性事件
   * @param {string} type 事件类型
   * @param {Function} handler 事件处理函数
   */
  const once = (type, handler) => {
    const onceHandler = (data) => {
      handler(data)
      off(type, onceHandler)
    }
    on(type, onceHandler)
  }

  /**
   * 清理当前组件的所有事件监听
   */
  const cleanup = () => {
    listeners.value.forEach((handlers, type) => {
      handlers.forEach(handler => {
        eventBus.off(type, handler)
      })
    })
    listeners.value.clear()
  }

  // 组件卸载时自动清理
  onBeforeUnmount(() => {
    cleanup()
  })

  return {
    emit,
    on,
    off,
    once,
    cleanup,
    // 直接访问全局事件总线
    eventBus
  }
} 