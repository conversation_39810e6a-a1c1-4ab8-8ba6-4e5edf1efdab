const { ipcMain } = require('electron')
const { 
  isIllegalConfig,
  getKmAddon,
  getAvalibleKey,
  checkAllContains,
  getValueInKmConfig,
  emitCallback,
  xinputCallback,
  rinputCallback,
} = require('./utils')
const { MOUSE_CODE_MAP } = require('./assets/constant/mouse_map')
const { DEFAULT_SHORTCUT_CONGIF } = require('./assets/constant')
const { log, eventBus: EventBus } = require('@heybox/electron-utils')

let pressed_keys = [], unrelease_key_config_list = []
let preventSc = false
let shortcut_config = []
let _recoverTimeout = setTimeout(() => {
  recover()
}, 1000 * 10)

let mouseActive = false, keyActive = false
let xInputListeners = []
let kmIntervalLog = null

initIpc()


/**
 * 注册快捷键
 * 
 * @param {Object} data - 快捷键配置对象
 * @param {string} data.key_config - 快捷键的唯一标识，也是快捷键的key。例如 'roulette_switch_key'。
 * @param {Array<string>} data.need_config - 当前快捷键需要依赖的设置项名称数组。例如 ['isOpenLaughterRoulette'] 表示依赖isOpenLaughterRoulette配置项开启。
 * @param {boolean} data.node_deliver - 是否将事件分配给 Node 环境。如果为 true，事件会在 Node 中处理，而非 Web 环境。
 * @param {boolean} data.ignore_preventSc - 是否忽略 preventSc（阻止快捷键触发的逻辑）。如果为 true，将无视 preventSc 的阻挡。
 * @param {boolean|Array} data.km_list - 快捷键值的数组，支持不定项。如果值为 false 表示不启用此功能。
 * @param {boolean} data.ignore_illegal - 是否忽略快捷键的非法检测。非法快捷键包括鼠标左右键。
 * @param {string} data.default_value_from_key - 从key中读取快捷键默认值，在未设置快捷键时优先会读取default_value_from_key。
 * @param {boolean|Array} data.default_value - 快捷键默认值，在default_value_from_key没值时会读取快捷键默认值，例如[113] f2。
 * @param {Function} down_callback - 按下快捷键的回调函数
 * @param {Function} up_callback - 松开快捷键的回调函数
 * 
 */
function addNodeShortcut (data, down_callback, up_callback) {
  shortcut_config.push({
    ...DEFAULT_SHORTCUT_CONGIF,
    ...data,
    down_callback,
    up_callback
  })
}

/**
 * 取消快捷键
 * 
 * @param {string} key_config - 快捷键的唯一标识，也是快捷键的key。例如 'roulette_switch_key'。
 */
function removeShortcut (key_config) {
  let key = shortcut_config.find(item => item.key_config === key_config)
  if(!key) return
  shortcut_config.splice(shortcut_config.indexOf(key), 1)
}

// 开始鼠标键盘快捷键
function startWatch() {
  if (!kmIntervalLog) {
    kmIntervalLog = setInterval(() => {
      log.info(`[kmAvaliableLog] ${mouseActive}, ${keyActive}`)
      mouseActive = keyActive = false
    }, 1000 * 60 * 20) // 每二十分钟上报五分钟内是否有成功触发过鼠标键盘事件
  }

  getKmAddon().Start(keyBoardCallback, mouseCallback)
}

// 停止鼠标键盘快捷键
function stopWatch() {
  getKmAddon().Stop()
}

// 触发键盘按下或抬起事件
function keyBoardCallback(r){
  keyActive = true
  if(main_config.kmlog) {
    log.info(`keycode: ${r.key_code}, keystatus: ${r.key_status}, delay: ${new Date().getTime() - r.time_stamp}`)
  }
	let result = r;
  let isDown = result.key_status % 2 === 0 ? true : false
  mouseKeyDeliver(result.key_code, isDown)
  EventBus.emit('keyboard_event', result.key_code, isDown)

  // 未收到键盘消息10s后触发recover，防止键盘事件失效
  clearTimeout(_recoverTimeout)
  _recoverTimeout = setTimeout(() => {
    recover()
  }, 1000 * 10)
}

// 触发鼠标按下或抬起事件
function mouseCallback(r){
  mouseActive = true
	let result = MOUSE_CODE_MAP[r.mouse_code];
  if (result) {
    let isDown = result.key_status === 0
    if(main_config.kmlog) {
      log.info(`mouse: ${result.key_code}, keystatus: ${result.key_status}}`)
    }
    mouseKeyDeliver(result.key_code, isDown)
    EventBus.emit('mouse_event', result.key_code, isDown)
  }
}

// 鼠标键盘按键触发时，通知注册事件的功能调用对应函数
function mouseKeyDeliver(keyCode, isDown) {
  if (pressed_keys.length > 0) {
    pressed_keys = getAvalibleKey(pressed_keys)
  }
  if (isDown && pressed_keys.includes(keyCode)) {
    return
  }
  if (isDown) {
    pressed_keys.push(keyCode)
  } else {
    let i = pressed_keys.indexOf(keyCode)
    if (i !== -1) {
      pressed_keys.splice(i, 1)
    }
  }
  if(main_config.kmDeliverlog) {
    log.info('pressed_keys', pressed_keys)
  }
  let down_types = [], up_types = []

  let real_shortcut_config = []
  // 处理语音包快捷键这种不定项的快捷键
  for (let c of shortcut_config) {
    let setting_obj = global.km_config[c.key_config]
    if (c.km_list && Array.isArray(setting_obj)) {
      for (let index in setting_obj) {
        if (setting_obj[index].enable === undefined || setting_obj[index].enable) {
          real_shortcut_config.push({
            ...c,
            ...setting_obj[index],
            index: index,
            key_config: c.key_config + '.' + index + '.trigger_key',
          })
        }
      }
    } else if (setting_obj && typeof setting_obj === 'object' && !Array.isArray(setting_obj)) { // 判断是对象，对象形式字段是不定项的快捷键
      for (let key in setting_obj) {
        real_shortcut_config.push({
          ...c,
          key_config: c.key_config + '.' + key,
          addition_key: key,
        })
      }
    } else {
      real_shortcut_config.push(c)
    }
  }

  // TODO real_shortcut_config缓存
  for (let c of real_shortcut_config) {
    if (preventSc && !c.ignore_preventSc) continue
    let shortcut_key_codes = getValueInKmConfig(c.key_config) ?? getValueInKmConfig(c.default_value_from_key, c.default_value)
    if (!shortcut_key_codes) continue

    if (!c.ignore_illegal && isIllegalConfig(shortcut_key_codes)) {
      continue
    } else if (c.need_config) {
      let pass = true
      c.need_config.forEach((v) => {
        if (!getValueInKmConfig(v)) pass = false
      })
      if (!pass) continue
    }

      
    if (checkAllContains(shortcut_key_codes, pressed_keys)) {
      down_types.push(c)
    } else {
      up_types.push(c)
    }
  }
  if(main_config.kmDeliverlog) {
    log.info('down_types', down_types)
    log.info('up_types', up_types)
    log.info('real_shortcut_config', real_shortcut_config)
  }

  // 未释放的快解决不需要重复触发
  down_types = down_types.filter(({key_config}) => {
    return !unrelease_key_config_list.includes(key_config)
  })
  // 更新新增 未释放的快解决
  down_types.forEach(({key_config}) => {
    unrelease_key_config_list.push(key_config)
  })
  
  up_types = up_types.filter(({key_config}) => {
    let index = unrelease_key_config_list.indexOf(key_config)
    if (index > -1) {
      unrelease_key_config_list.splice(index, 1)
    }
    return index > -1
  })
  // 快捷键事件推送
  for (let t of down_types) {
    try {
      t.node_deliver ? emitCallback(t, true) : mainWindow?.webContents?.send('key-mouse-event', t, true)
    } catch (e) {}
  }
  for (let t of up_types) {
    try {
      t.node_deliver ? emitCallback(t, false) : mainWindow?.webContents?.send('key-mouse-event', t, false)
    } catch (e) {}
  }
}

/**
 * 开始手柄监听
 * 
 * @param {string} id - 手柄监听的唯一标识，通常使用小程序id即可，用于在所有监听卸载时停止手柄监听
 */
function startXinputWatch(id) {
  if (xInputListeners.length === 0) {
    getKmAddon().StartListenXinput(xinputCallback, rinputCallback)
  }
  if (!xInputListeners.includes(id)) {
    xInputListeners.push(id)
  }
}

/**
 * 停止手柄监听
 * 
 * @param {string} id - 手柄监听的唯一标识，通常使用小程序id即可，用于在所有监听卸载时停止手柄监听
 */
function stopXinputWatch(id) {
  xInputListeners = xInputListeners.filter(xid => xid !== id)
  if (xInputListeners.length === 0) {
    getKmAddon().StopListenXinput()
  }
}


/**
 * 判断某几个按键当前是否按下
 * 
 * @param {number|Array<number>} l - 快捷键的value，如16或[16]
 */
function isPressKeys(l) {
  if (typeof l !== 'object') {
    l = [l]
  }
  for (let k of l) {
    if (!pressed_keys.includes(k)) {
      return false
    }
  }
  return true
}

// 恢复快捷键监听，防止异常导致的监听卡死
function recover() {
  getKmAddon().RecoverKm()
}

// 重置preventSc标记，防止页面跳转时preventSc标记卡死
function resetPrevent() {
  preventSc = false
}

function initIpc() {
  // web添加快捷键事件
  ipcMain.on('add-km-shortcut', (_, config) => {
    shortcut_config.push(config) 
  })

  // web删除快捷键事件
  ipcMain.on('remove-km-shortcut', (_, key_config) => {
    removeShortcut(key_config)
  })

  // 设置屏蔽鼠标键盘事件
  ipcMain.on('set-key-mouse-prevent', function (e, v) {
    preventSc = v
  })
}

module.exports = {
  startWatch,
  stopWatch,
  recover,
  isPressKeys,
  resetPrevent,
  addNodeShortcut,
  startXinputWatch,
  stopXinputWatch,
  removeShortcut,
}