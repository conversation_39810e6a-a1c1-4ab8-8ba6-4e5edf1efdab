const { Tray, nativeImage} = require('electron')
const path = require('path')
const os = require('os')
const utils = require('@heybox/electron-utils')

class TrayMenu {
  constructor() {
    this.tray = null
    this.icon = null
    this.contextMenu = null
  }

  init() {
    this.icon = nativeImage.createFromPath(path.join(__dirname, '../../buildResources/icon.ico'))
    this.tray = new Tray(this.icon)
    this.tray.setToolTip('小黑盒')
    this.tray.setTitle('小黑盒')

    this.tray.on('mouse-move', () => {
      if (this.contextMenu && this.contextMenu.isWinShow) {
        return
      }
    });
    this.tray.on('right-click', () => {
      if (this.contextMenu) {
        this.contextMenu.destroyWindow && this.contextMenu.destroyWindow()
      } else {
        if (this.isWindows7()) {
          const NativeContextMenu = require('./nativeContextMenu/index')
          this.contextMenu = new NativeContextMenu(this.tray)
        } else {
          const ContextMenu = require('./contextMenu/index')
          this.contextMenu = new ContextMenu(this.tray)
        }
      }
      this.contextMenu.showBox()
    });

    this.tray.on('click', function () {
      if (!mainWindow) {
        return
      }
      if (mainWindow.isMinimized()) mainWindow.restore()
      utils.focusWindow(mainWindow)
    })

    this.initEvent()
  }
  initEvent() {
    // 接收Web推送的消息
  }
  isWindows7() {
    return process.platform === 'win32' && os.release().startsWith('6.1')
  }
}
module.exports = new TrayMenu()