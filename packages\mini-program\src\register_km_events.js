const { log, store } = require('@heybox/electron-utils')
const utils = require('@heybox/electron-utils')
const tastIterate = require('@heybox/process-iterate')

let _avaliableOverlayMiniProCache = null

function registerShowOverlayKmEvent(mini_pro_id, enable_shortcut_config) {
  let mini_data = global.mini_map[mini_pro_id]
  if (mini_data) {
    global.kmEvent.addNodeShortcut({
      key_config: enable_shortcut_config.key,
      default_value_from_key: enable_shortcut_config.default_value_from_key,
      default_value: enable_shortcut_config.default_value,
    }, handleOverlayKey.bind(this, mini_pro_id))
  }
}


// TODO 兼容老版本小程序，后续小程序的快捷键应该在小程序里注册
function registerKmEvent() {
  global.kmEvent.addNodeShortcut({key_config: "miniprogram_hide_key"}, handleSwitchHideKey.bind(this))
  global.kmEvent.addNodeShortcut({key_config: "miniprogram_cancel_key"}, handleCancelKey.bind(this))

  global.kmEvent.addNodeShortcut({key_config: "black_myth_map_key"}, (data) => {
    const cur_focus_window = utils.getCurFocusWindow()
    main_config.wukongLog && log.info('[black_myth_map_key]', cur_focus_window)
    tastIterate.getAvaliableOverlayMiniPro(cur_focus_window).then(mini_pro_ids => {
      if (mini_pro_ids.length > 0) {
        mini_pro_ids.forEach(mini_pro_id => {
          global.MiniProgram.patchWindowEvent('MiniProgramSDK:on-down-keyboard', { focus_mini_key: mini_pro_id, cur_focus_window, key_code: 'black_myth_map_key' })
        })
      } else {
        global.MiniProgram.patchWindowEvent('MiniProgramSDK:on-down-keyboard', { focus_mini_key: '', cur_focus_window, key_code: 'black_myth_map_key' })
      }
    })
  })
  global.kmEvent.addNodeShortcut({key_config: "black_myth_hide_key"}, (data) => {
    const cur_focus_window = utils.getCurFocusWindow()
    main_config.wukongLog && log.info('[black_myth_hide_key]', cur_focus_window)
    tastIterate.getAvaliableOverlayMiniPro(cur_focus_window).then(mini_pro_ids => {
      if (mini_pro_ids.length > 0) {
        mini_pro_ids.forEach(mini_pro_id => {
          global.MiniProgram.patchWindowEvent('MiniProgramSDK:on-down-keyboard', { focus_mini_key: mini_pro_id, cur_focus_window, key_code: 'black_myth_hide_key' })
        })
      } else {
        global.MiniProgram.patchWindowEvent('MiniProgramSDK:on-down-keyboard', { focus_mini_key: '', cur_focus_window, key_code: 'black_myth_hide_key' })
      }
    })
  }) 
  
}

// miniprogram_overlay_key快捷键处理，创建or销毁overlay小程序
async function handleOverlayKey(mini_pro_id) {
  let mini_pro_ids = await getAvaliableOverlayMiniProCache()
  if (mini_pro_ids.includes(mini_pro_id)) {
    triggerMiniProShow(mini_pro_id)
  }
}

function triggerMiniProShow(mini_pro_id) {
  let miniprogram_setting = store.get('miniprogram_setting')?.find((setting) => mini_pro_id === setting.mini_pro_id)
  log.info('[km miniprogram_overlay]', miniprogram_setting?.enable)
  if (mini_pro_id && miniprogram_setting?.enable) {
    global.MiniProgram.beforeMount(mini_pro_id, {
      isOverlay: true
    })
  } else {
    global.MiniProgram.clearAllOverlayMiniProgram()
  }
}

// miniprogram_hide_key快捷键处理，地图小程序使用，如帕鲁地图的配置面板显隐
function handleSwitchHideKey() {
  const cur_focus_window = utils.getCurFocusWindow()
  main_config.wukongLog && log.info('[black_myth_palmap_hide_key]', cur_focus_window)
  tastIterate.getAvaliableOverlayMiniPro(cur_focus_window).then(mini_pro_ids => {
    if (mini_pro_ids.length > 0) {
      mini_pro_ids.forEach(mini_pro_id => {
        global.MiniProgram.patchWindowEvent('MiniProgramSDK:on-switch-hide-state', { focus_mini_key: mini_pro_id, cur_focus_window })
      })
    } else {
      global.MiniProgram.patchWindowEvent('MiniProgramSDK:on-switch-hide-state', { focus_mini_key: '', cur_focus_window })
    }
  })

}

// miniprogram_cancel_key快捷键处理
function handleCancelKey() {
  const cur_focus_window = utils.getCurFocusWindow()
  tastIterate.getAvaliableOverlayMiniPro(cur_focus_window).then(mini_pro_ids => {
    if (mini_pro_ids.length > 0) {
      mini_pro_ids.forEach(mini_pro_id => {
        global.MiniProgram.patchWindowEvent('MiniProgramSDK:on-down-keyboard', { focus_mini_key: mini_pro_id, cur_focus_window, key_code: 'cancel_key' })
      })
    } else {
      global.MiniProgram.patchWindowEvent('MiniProgramSDK:on-down-keyboard', { focus_mini_key: '', cur_focus_window, key_code: 'cancel_key' })
    }
  })
}

// 缓存当前聚焦窗口对应的小程序id，缓存150ms，防止多个浮窗小程序快捷键绑定同一个按键导致频繁获取系统窗口数据
function getAvaliableOverlayMiniProCache() {
  if (_avaliableOverlayMiniProCache) {
    return _avaliableOverlayMiniProCache
  } else {
    return new Promise(resolve => {
      tastIterate.getAvaliableOverlayMiniPro().then((mini_pro_ids) => {
        _avaliableOverlayMiniProCache = mini_pro_ids
        setTimeout(() => {
          _avaliableOverlayMiniProCache = null
        }, 150)
        resolve(_avaliableOverlayMiniProCache)
      })
    })
  }
}

module.exports = { registerKmEvent, registerShowOverlayKmEvent };
