const { xx } = require('./variable')
const md5 = require('js-md5')

function gencode(path, time, nonce) {
  path = `/${path
    .split('/')
    .filter((_) => _)
    .join('/')}/`;
  //AB45STUVWZEFGJ6CH01D237IXYPQRKLMN89
  const charset = 'AB45STUVWZEFGJ6CH01D237IXYPQRKLMN89';

  const transformedTime = xt6(String(time), charset, -2);
  const transformedPath = xt4(path, charset);
  const transformedNonce = xt4(nonce, charset);

  const combinedStr = combineStrings([
    transformedTime,
    transformedPath,
    transformedNonce,
  ]).slice(0, 20);
  const hash = md5(combinedStr);

  let sign = `${
    sum(
      xx(
        hash
          .slice(-6)
          .split('')
          .map((_) => _.charCodeAt()),
      ),
    ) % 100
  }`;
  sign = sign.length < 2 ? `0${sign}` : sign;
  const signaturePart = xt6(hash.substring(0, 5), charset, -4);
  const finalSignature = `${signaturePart}${sign}`;
  return finalSignature;
}

// 确定性变换字符串
function xt6(str, charset, offset) {
  let transformedStr = '',
    t = charset.slice(0, offset);
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i);
    const newIndex = charCode % t.length;
    transformedStr += t[newIndex];
  }
  return transformedStr;
}
function xt4(str, charset) {
  let transformedStr = '';
  for (let i = 0; i < str.length; i++) {
    const charCode = str.charCodeAt(i);
    const newIndex = charCode % charset.length;
    transformedStr += charset[newIndex];
  }
  return transformedStr;
}

// 组合字符串
function combineStrings(strings) {
  let combinedStr = '';
  for (let i = 0; i < Math.max(...strings.map((s) => s.length)); i++) {
    strings.forEach((str) => {
      if (i < str.length) {
        combinedStr += str[i];
      }
    });
  }
  return combinedStr;
}

function sum(arr) {
  return arr.reduce((p, c) => {
    return p + c;
  }, 0);
}

const G = {
  a(url, time, nonce) {
    return gencode(url, time - 1, nonce);
  },
  b(url, time, nonce) {
    return gencode(url, time - 2, nonce);
  },
  c(url, time, nonce) {
    return gencode(url, time - 3, nonce);
  },
  d(url, time, nonce) {
    return gencode(url, time - 4, nonce);
  },
  e(url, time, nonce) {
    return gencode(url, time - 5, nonce);
  },
  f(url, time, nonce) {
    return gencode(url, time, nonce);
  },
  g(url, time, nonce) {
    return gencode(url, time + 1, nonce);
  },
  h(url, time, nonce) {
    return gencode(url, time + 2, nonce);
  },
  i(url, time, nonce) {
    return gencode(url, time + 3, nonce);
  },
  j(url, time, nonce) {
    return gencode(url, time + 4, nonce);
  },
  k(url, time, nonce) {
    return gencode(url, time + 5, nonce);
  },
  l(url, time, nonce) {
    return gencode(`/${url}/`, time - 1, nonce);
  },
  m(url, time, nonce) {
    return gencode(`/${url}/`, time - 2, nonce);
  },
  w() {
    return new Date();
  },
  n(url, time, nonce) {
    return gencode(`/${url}/`, time - 3, nonce);
  },
  o(url, time, nonce) {
    return gencode(`/${url}/`, time - 4, nonce);
  },
  p(url, time, nonce) {
    return gencode(`/${url}/`, time - 5, nonce);
  },
  q(url, time, nonce) {
    return gencode(`/${url}/`, time, nonce);
  },
  r(url, time, nonce) {
    return gencode(`/${url}/`, time + 1, nonce);
  },
  s(url, time, nonce) {
    return gencode(`/${url}/`, time + 2, nonce);
  },
  t(url, time, nonce) {
    return gencode(`/${url}/`, time + 3, nonce);
  },
  u(url, time, nonce) {
    return gencode(`/${url}/`, time + 4, nonce);
  },
  v(url, time, nonce) {
    return gencode(`/${url}/`, time + 5, nonce);
  },
};
module.exports = { G };
