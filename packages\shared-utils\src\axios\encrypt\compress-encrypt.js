import AES from 'crypto-js/aes';
import UTF8 from 'crypto-js/enc-utf8';
import { mode } from 'crypto-js';
import pako from 'pako';
import dskEncrypt from './dsk';

/**
 * 对数据进行gzip压缩和AES加密（使用dsk.js的加密方式）
 * @param {Object} data - 要处理的数据对象
 * @returns {Object} 返回加密后的数据，包含加密的数据、密钥和sid
 */
export function compressAndEncrypt(data) {
  try {
    // 1. 将数据转换为JSON字符串
    const jsonString = JSON.stringify(data);
    
    // 2. 将JSON字符串编码为UTF8二进制
    // const utf8Bytes = new TextEncoder().encode(jsonString);
    
    // 3. 使用pako进行gzip压缩
    const compressed = data
    console.log('compressed', compressed)

    // const compressedBase64 = btoa(String.fromCharCode.apply(null, compressed));
    // const compressed = pako.gzip(jsonString);
    
    // 3. 使用dsk.js的加密方法处理压缩后的数据
    const dskResult = dskEncrypt(data);
    
    return {
      encryptedData: dskResult.data,
      key: dskResult.key,
      sid: dskResult.sid,
      time: dskResult.time,
    };
  } catch (error) {
    console.error('压缩加密失败:', error);
    throw error;
  }
}

/**
 * 解密并解压缩数据（使用dsk.js的解密方式）
 * @param {string} encryptedData - 加密的数据
 * @param {string} key - RSA解密后的AES密钥
 * @returns {Object} 返回解密并解压缩后的原始数据
 */
export function decryptAndDecompress(encryptedData, key) {
  try {
    // 1. 使用RSA解密密钥（如果需要的话）
    // 注意：这里假设传入的key已经是解密后的AES密钥
    // 如果传入的是RSA加密的密钥，需要先解密
    
    // 2. 使用AES解密（使用dsk.js的方式）
    const iv = UTF8.parse('abcdefghijklmnop');
    const decrypted = AES.decrypt(encryptedData, UTF8.parse(key), {
      iv: iv,
      mode: mode.CBC
    }).toString(UTF8);
    
    // 3. 将Base64字符串转换回Buffer
    const compressedBuffer = new Uint8Array(atob(decrypted).split('').map(char => char.charCodeAt(0)));
    
    // 4. 使用pako解压缩
    const decompressed = pako.inflate(compressedBuffer, { to: 'string' });
    
    // 5. 解析JSON字符串
    return JSON.parse(decompressed);
  } catch (error) {
    console.error('解密解压缩失败:', error);
    throw error;
  }
}



/**
 * 仅进行gzip压缩（不加密）
 * @param {Object} data - 要压缩的数据对象
 * @returns {string} 返回Base64编码的压缩数据
 */
export function compressOnly(data) {
  try {
    const jsonString = JSON.stringify(data);
    const compressed = pako.gzip(jsonString);
    return btoa(String.fromCharCode.apply(null, compressed));
  } catch (error) {
    console.error('压缩失败:', error);
    throw error;
  }
}

/**
 * 仅进行gzip解压缩（不解密）
 * @param {string} compressedData - Base64编码的压缩数据
 * @returns {Object} 返回解压缩后的原始数据
 */
export function decompressOnly(compressedData) {
  try {
    const compressedBuffer = new Uint8Array(atob(compressedData).split('').map(char => char.charCodeAt(0)));
    const decompressed = pako.inflate(compressedBuffer, { to: 'string' });
    return JSON.parse(decompressed);
  } catch (error) {
    console.error('解压缩失败:', error);
    throw error;
  }
} 