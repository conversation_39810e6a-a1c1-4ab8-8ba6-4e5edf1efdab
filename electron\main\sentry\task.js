const AUTH_TOKEN = '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'
const fs = require('fs')
const path = require('path')

const _execFile = (args) => {
  const execFile = require('child_process').execFile;
  const sentryCliPath = path.resolve(__dirname, 'sentry-cli.exe')
  
  return new Promise((resolve, reject) => {
    execFile(
      sentryCliPath,
      args,
      {
        env: {'SENTRY_AUTH_TOKEN': AUTH_TOKEN}
      },
      (error, stdout, stderr) => {
        if (error) reject(error)
        if (stdout) console.error(stderr)
        console.log(stdout)
        resolve()
      })
  })
}

const getSentryRelease = () => {
  const { version } = require('../package.json')
  const timestamp = Date.now()
  const release = `heybox-app-electron@${version}_${timestamp}`
  fs.writeFileSync(path.resolve(__dirname, '../webapp', 'sentry-release'), release)
  return release
}

const processSourcemap = (dir) => {
  function updateSourceInMap(filePath, targetDir) {
    const content = fs.readFileSync(filePath, 'utf-8');
    let mapData = JSON.parse(content);
    
    const filenameWithoutExt = path.basename(filePath, '.map');
    
    const relativePath = path.posix.join(
      path.relative(targetDir, path.dirname(filePath)),
      filenameWithoutExt
    );
    
    mapData.sources = [relativePath];
    console.log('processing map file:', filePath)
    fs.writeFileSync(filePath, JSON.stringify(mapData));
  }
  
  function findMapFiles(dir, rootDir) {
    const files = fs.readdirSync(dir);
    
    for (let file of files) {
      const fullPath = path.join(dir, file);
      const stats = fs.statSync(fullPath);
      
      if (stats.isDirectory()) {
        findMapFiles(fullPath, rootDir);
      } else if (path.extname(file) === '.map') {
        updateSourceInMap(fullPath, rootDir);
      }
    }
  }
  
  console.log('find', dir)
  findMapFiles(dir, dir);
}

function copyDirectory(sourceDir, targetDir) {
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir);
  }

  const files = fs.readdirSync(sourceDir);

  files.forEach(file => {
    const sourcePath = path.join(sourceDir, file);
    const targetPath = path.join(targetDir, file);

    if (fs.statSync(sourcePath).isDirectory()) {
      copyDirectory(sourcePath, targetPath);
    } else {
      fs.copyFileSync(sourcePath, targetPath);
    }
  });
}

const uploadSourcemap = async (org, project, dir, webapp) => {
  processSourcemap(dir)
  copyDirectory(path.join(webapp, 'main/js'), path.join(dir, 'webapp'))
  copyDirectory(path.join(webapp, 'chat/js'), path.join(dir, 'webapp'))
  copyDirectory(path.join(webapp, 'accelerator/js'), path.join(dir, 'webapp'))
  let release = getSentryRelease()
  console.log(release)
  
  const cliInject = () => {
    return _execFile(['sourcemaps', 'inject', '--org', org, '--project', project, dir])
  }
  const cliCreateRelease = () => {
    return _execFile(['releases', 'new', release, '--project', project])
  }
  const cliUploadElectron = () => {
    return _execFile(['--url', 'https://monitor.xiaoheihe.cn', 'sourcemaps', 'upload', '--url-prefix', 'app:///src_/', '--org', org,  '--project', project, dir, '--release', release])
  }
  
  await cliInject()
  await cliCreateRelease()
  await cliUploadElectron()
}

const createSentryConfigFile = () => {
  const filename = path.resolve(__dirname, '..', '.sentryclirc')
  console.log(`去 https://monitor.xiaoheihe.cn/settings/account/api/auth-tokens/ 复制 token 到 ${filename} 里`)
  const content = '[auth]\ntoken=\n'
  fs.writeFileSync(filename, content)
}


const main = (argv) => {
  const org = 'heybox'
  const project = 'heybox-chat-mix'
  const dir = path.join(__dirname, '..', 'src_')
  const webapp = path.join(__dirname, '..', 'webapp')
  console.log('on', dir)
  
  uploadSourcemap(org, project, dir, webapp)
}

main(process.argv)
