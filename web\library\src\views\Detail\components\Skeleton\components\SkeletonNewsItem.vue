<template>
  <div class="skeleton-news-item">
    <SkeletonFriendItem />
    
    <div class="skeleton-news-content">
      <div class="skeleton-news-line long"></div>
      <div class="skeleton-news-line long"></div>
      <div class="skeleton-news-line short"></div>
    </div>
    
    <div class="skeleton-news-images">
      <div class="skeleton-news-image" v-for="i in 3" :key="i"></div>
    </div>
  </div>
</template>

<script setup name="SkeletonNewsItem">
import SkeletonFriendItem from './SkeletonFriendItem.vue';
</script>

<style lang="scss" scoped>
.skeleton-news-item {
  display: flex;
  flex-direction: column;
  padding: 14px 20px;
  border-radius: 8px;
  background: var(---general-color-primary-0, #FFF);

  .skeleton-news-content {
    display: flex;
    margin-top: 8px;
    flex-direction: column;
    gap: 7px;

    .skeleton-news-line {
      border-radius: 6px;
      background: var(---general-color-bg-0, #F1F2F3);
      height: 12px;

      &.long {
        width: 465px;
      }

      &.short {
        width: 161px;
      }
    }
  }

  .skeleton-news-images {
    display: flex;
    gap: 4px;
    padding: 6px 0;

    .skeleton-news-image {
      border-radius: var(--5503450, 5px);
      background: var(---general-color-bg-0, #F1F2F3);
      width: 114px;
      height: 100px;
    }
  }
}
</style>
