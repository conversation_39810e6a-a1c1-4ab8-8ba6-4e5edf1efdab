const vm = require("vm");
const fs = require("fs");
const _module = require("module");
const v8 = require("v8");
const path = require("path");
const bytesource = require("./bytesource");
v8.setFlagsFromString("--no-lazy");

function compileFile(filePath, outputDir = "", code) {
  outputDir = outputDir || path.dirname(filePath);
  const prefix = path.join(outputDir, path.basename(filePath).replace(/\.js$/i, ""));
  const wrappedCode = _module.wrap(code);
  const script = new vm.Script(wrappedCode, {
    filename: filePath.slice(filePath.indexOf('src_')).replaceAll('\\', '/'),
    columnOffset: -62
  });
  const bytecode = script.createCachedData();
  fs.writeFileSync(prefix + ".jsc", bytecode);
  if (code.length < 10 * 1024 * 1024) {
    const souceMap = bytesource(wrappedCode);
    fs.writeFileSync(prefix + ".jsm", souceMap, "utf-8");
  }
}
exports.compileFile = compileFile;
