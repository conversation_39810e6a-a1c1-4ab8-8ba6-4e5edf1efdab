const { app, BrowserWindow, screen, ipcMain } = require('electron');
const path = require('path');
const utils = require('@heybox/electron-utils')
const EventBus = utils.eventBus
class ContextMenu {
  constructor(tray) {
    this.tray = tray;
    
    const MENU_ITEM_WIDTH = 130;
    const MENU_ITEM_HEIGHT = 70;
    const SHADOW_WIDTH = 36;
    const MENU_GAP = 4;
    this.winWidth = MENU_ITEM_WIDTH + MENU_ITEM_WIDTH + MENU_GAP + SHADOW_WIDTH;
    this.winHeight = MENU_ITEM_HEIGHT + SHADOW_WIDTH;
    this.resetData();
    this.clickHidden = (_, isDown) => {
      isDown && this.win.webContents.send('get-hover-status')
    }
    this.setupIpcHandlers();
  }

  setupIpcHandlers() {
    // 接收Web推送的消息
    ipcMain.on('handle-menu-click', (_, v) => {
      this.handleMenuClick(v)
    })
    ipcMain.on('set-hover-status', (e, data) => {
      if (!data) {
        this.destroyWindow()
        EventBus.off('mouse_event', this.clickHidden)
      }
    })
  }
  resetData() {
    this.win = null;
    this.isWinShow = false;
    this._windowCreating = false;
    this.winDirection = 'column';
    this.showSubMenu = null;
  }

  showBox() {
    if (this.isWinShow || !mainWindow?.webContents || mainWindow.isDestroyed()) return;
    mainWindow.webContents.send('get-tray-context-menu-data')
    this.createWindow();
  }

  createWindow() {
    if (this._windowCreating || (this.win && !this.win.isDestroyed() && this.win.isVisible())) return;
    const trayBounds = this.tray.getBounds();
    const primaryDisplay = screen.getPrimaryDisplay();
    const workArea = primaryDisplay.workAreaSize;
  
    // 计算窗口的 x 坐标，使窗口的水平中心对齐托盘图标的水平中心
    let x = Math.round(trayBounds.x + trayBounds.width / 2 - this.winWidth / 2);
  
    // 计算窗口的 y 坐标，使窗口显示在托盘图标的正上方
    let y = trayBounds.y - this.winHeight;
  
    // 确保窗口不会超出屏幕的左边或右边
    if (x < 0) {
      x = 32;
    } else if (x + this.winWidth > workArea.width) {
      x = workArea.width - this.winWidth + 65;
    } else {
      x += 65
    }
  
    // 如果托盘在屏幕顶部，窗口应该显示在托盘图标的正下方
    if (trayBounds.y < primaryDisplay.size.height / 2) {
      y = trayBounds.y + trayBounds.height
    }

    this.win = new BrowserWindow({
      x, 
      y,
      focusable: false,
      width: this.winWidth,
      height: this.winHeight, 
      show: true, 
      frame: false, 
      skipTaskbar: true, 
      transparent: true,
      backgroundColor: '#00ffffff',
      resizable: false,
      webPreferences: {
        preload: path.join(__dirname, './preload.js')
      }
    });

    this._windowCreating = true;
    this.win.loadFile(path.join(__dirname, './index.html'));

    this.win.setAlwaysOnTop(true, 'pop-up-menu');
    this.win.webContents.on('did-finish-load', () => {
      this.win.show();
      this.isWinShow = true;
      this._windowCreating = false;
      this.win.webContents.send('receive-tray-menu-data', this.menu_data);
      this.startClickListening()
    });
  }

  destroyWindow() {
    if (this.win && !this.win.isDestroyed()) {
      this.win.destroy()
    }
    this.isWinShow = false;
    this.resetData();
  }
  startClickListening () {
    EventBus.on('mouse_event', this.clickHidden)
  }
  checkIsClickWindow () {
    const { x, y } = screen.getCursorScreenPoint()
    const winBounds = this.win.getBounds()
    // 子菜单的位置在窗口的右上角。宽度为130，showSubMenu为online_state时高度为120，为status_privacy时高度为94
    // 如果this.showSubMenu为null，说明没有显示子菜单，所以此时只能按照窗口左边宽度130的范围来判断
    if (!this.showSubMenu) {
      return x >= winBounds.x && x <= winBounds.x + 130 && y >= winBounds.y && y <= winBounds.y + winBounds.height
    }
    // 如果this.showSubMenu不为null，说明显示了子菜单，此时需要判断点击的位置是否在子菜单的范围内
    if (this.showSubMenu === 'online_state') {
      return x >= winBounds.x + 130 && x <= winBounds.x + 260 && y >= winBounds.y && y <= winBounds.y + 120
    }
    if (this.showSubMenu === 'status_privacy') {
      return x >= winBounds.x + 130 && x <= winBounds.x + 260 && y >= winBounds.y && y <= winBounds.y + 94
    }
    return false
  }
  handleMenuClick ({ menu }) {
    // 根据主菜单的逻辑处理
    if (menu.key === 'show_main_window') {
      if (!mainWindow) {
        return
      }
      if (mainWindow.isMinimized()) {
        mainWindow.restore()
      }
      utils.focusWindow(mainWindow)
    } else if (menu.key === 'exit_app') {
      if (mainWindow) {
        mainWindow.webContents?.send('exit-app')
      }
      global.kmEvent?.stopWatch()
      app.exit()
    }
    this.destroyWindow()
  }
  setMenuData (data) {
    if (!this.win || this.win.isDestroyed()) return
    this.menu_data = data
    this.win?.webContents.send('receive-tray-menu-data', this.menu_data);
  }
}

module.exports = ContextMenu;
