{"name": "@heybox/process-iterate", "version": "1.0.0", "description": "process iterate", "main": "src/index.js", "exports": {".": "./src/index.js", "./match": "./src/match.js", "./music": "./src/music.js", "./utils": "./src/utils.js"}, "files": ["src"], "dependencies": {"@heybox/electron-utils": "workspace:*", "@heybox/mini-program": "workspace:*", "@heybox/node-inject": "workspace:*"}, "peerDependencies": {"electron": "33.2.0"}}