<template>
  <div
    class="hb-switch"
    :class="{
      'hb-switch--active': modelValue,
      'hb-switch--disabled': disabled,
      [`hb-switch--${size}`]: size !== 'medium'
    }"
    @click="handleClick"
  >
    <div class="hb-switch__slider"></div>
  </div>
</template>

<script setup name="HbSwitch">
import { defineProps, defineEmits } from 'vue';

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const handleClick = () => {
  if (props.disabled) return;
  
  const newValue = !props.modelValue;
  emit('update:modelValue', newValue);
  emit('change', newValue);
};
</script>

<style lang="scss" scoped>
.hb-switch {
  width: 33px;
  height: 18px;
  background: #E5E5E7;
  border-radius: 9px;
  position: relative;
  cursor: pointer;
  transition: background 0.3s ease;
  display: inline-block;
  
  &--active {
    background: #111111;
  }
  
  &--disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }
  
  &__slider {
    width: 14px;
    height: 14px;
    background: white;
    border-radius: 50%;
    position: absolute;
    top: 2px;
    left: 2px;
    transition: transform 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  }
  
  &--active &__slider {
    transform: translateX(15px);
  }
  
  &--small {
    width: 28px;
    height: 16px;
    border-radius: 8px;
    
    .hb-switch__slider {
      width: 12px;
      height: 12px;
      top: 2px;
      left: 2px;
    }
    
    &.hb-switch--active .hb-switch__slider {
      transform: translateX(12px);
    }
  }
  
  &--large {
    width: 44px;
    height: 24px;
    border-radius: 12px;
    
    .hb-switch__slider {
      width: 20px;
      height: 20px;
      top: 2px;
      left: 2px;
    }
    
    &.hb-switch--active .hb-switch__slider {
      transform: translateX(20px);
    }
  }
}
</style>
