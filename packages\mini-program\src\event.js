const { ipcMain } = require('electron')

// 存储IPC和EventBus事件及管理器的全局变量
const IPC_EVENTS = {}, EVENTBUS_EVENTS = {}, EVENT_MANAGERS = {}

// 事件类型定义
const EVENT_TYPE = {
  IPC: 'ipc', // IPC事件类型
  EVENTBUS: 'eventbus', // EventBus事件类型
}

// 方法映射：用于绑定和解绑方法之间的对应关系
const EVENT_METHODS = {
  on: 'off',
  off: 'on',
  removeAllListeners: 'on',
  removeListener: 'on',

  handle: 'removeHandler',
  removeHandler: 'handle',
}

const { eventBus: EventBus } = require('@heybox/electron-utils')

/**
 * 事件管理类
 * 每个小程序实例对应一个EventManager，用于管理其IPC和EventBus事件
 */
class EventManager {
  constructor(mini_pro_id) {
    this.mini_pro_id = mini_pro_id // 小程序ID
    this.$ipc = { // IPC事件管理快捷方法
      register: this.register.bind(this),
      unregister: this.unregister.bind(this),
      on: (name, callback) => this.register.bind(this, 'on', name, callback)(),
      off: (name, callback) => this.unregister.bind(this, 'off', name, callback)(),
      removeListener: (name, callback) => this.unregister.bind(this, 'removeListener', name, callback)(),
      removeAllListeners: (name, callback) => this.unregister.bind(this, 'removeAllListeners', name, callback)(),
      handle: (name, callback) => this.register.bind(this, 'handle', name, callback)(),
      removeHandler: (name, callback) => this.unregister.bind(this, 'removeHandler', name, callback)(),
    }
    this.$eventBus = { // EventBus事件管理快捷方法
      on: this.on.bind(this),
      off: this.off.bind(this)
    }
  }

  /**
   * 注册IPC事件 (兼容旧版本，建议使用 $ipc.register 方法)
   * @param {string} method 方法类型 (on/handle)
   * @param {string} name 事件名称
   * @param {function} callback 回调函数
   */
  register (method, name, callback) {
    if (!['on', 'handle'].includes(method)) {
      throw new Error('Invalid method params') // 参数校验
    }
    let event = getEvent(EVENT_TYPE.IPC, name, method)
    if (event) {
      // 如果事件已存在，直接追加回调函数
      event.callbacks.push({
        mini_pro_id: this.mini_pro_id,
        callback,
      })
      if (method === 'handle') {
        console.warn(`[EventManager warn] name ${name}, repeat handle register, return value maybe wrong. Suggest using unique event names`)
      }
    } else {
      // 初始化新事件
      initIpcEvent(method, name, callback, this.mini_pro_id)
    }
  }

  /**
   * 解除IPC事件 (兼容旧版本，建议使用 $ipc.unregister 方法)
   * @param {string} method 方法类型
   * @param {string} name 事件名称
   * @param {function} callback 回调函数
   */
  unregister (method, name, callback) {
    unbindIpcEvent(method, name, callback, this.mini_pro_id)
  }

  /**
   * 注册EventBus事件 (建议使用 $eventBus.on 方法)
   * @param {string} name 事件名称
   * @param {function} callback 回调函数
   */
  on (name, callback) {
    let event = getEvent(EVENT_TYPE.EVENTBUS, name)
    if (!event) {
      EVENTBUS_EVENTS[name] = []
    }
    EVENTBUS_EVENTS[name].push({
      mini_pro_id: this.mini_pro_id,
      callback,
    })
    EventBus.on(name, callback)
  }

  /**
   * 解除EventBus事件 (建议使用 $eventBus.off 方法)
   * @param {string} name 事件名称
   * @param {function} callback 回调函数
   */
  off (name, callback) {
    let event = getEvent(EVENT_TYPE.EVENTBUS, name)
    if (event) {
      event.forEach((data) => {
        if (data.mini_pro_id === this.mini_pro_id) {
          if (!callback || callback === data.callback) {
            EventBus.off(name, data.callback)
            data.callback = null
          }
        }
      })
      EVENTBUS_EVENTS[name] = event.filter((data) => data.callback) // 过滤已解绑的事件
    }
  }

  /**
   * 解除当前实例的所有事件 (小程序销毁时自动调用)
   */
  unbindAllEvent() {
    this.unbindAllIpcEvent() // 解除所有IPC事件
    this.unbindAllEventbusEvent() // 解除所有EventBus事件
    console.log(EVENTBUS_EVENTS, IPC_EVENTS)
  }

  /**
   * 解除所有IPC事件
   */
  unbindAllIpcEvent() {
    for (let method in IPC_EVENTS) {
      for (let name in IPC_EVENTS[method]) {
        for (let data of IPC_EVENTS[method][name].callbacks) {
          if (data.mini_pro_id === this.mini_pro_id) {
            unbindIpcEvent(EVENT_METHODS[method], name, data.callback, this.mini_pro_id)
          }
        }
      }
    }
  }

  /**
   * 解除所有EventBus事件
   */
  unbindAllEventbusEvent() {
    for (let name in EVENTBUS_EVENTS) {
      for (let event of EVENTBUS_EVENTS[name]) {
        if (event.mini_pro_id === this.mini_pro_id) {
          this.off(name, event.callback)
        }
      }
    }
  }
}

/**
 * IPC事件分发器
 * @param {object} param0 方法与事件名称
 * @param  {...any} arguments 回调参数
 */
function ipcEventDispatcher({method, name}, ...arguments) {
  let event = getEvent(EVENT_TYPE.IPC, name, method)
  if (event) {
    if (method === 'handle') {
      return event.callbacks[0].callback(...arguments) // 返回值处理
    } else {
      event.callbacks.forEach((data) => {
        data.callback(...arguments) // 执行所有回调
      })
    }
  }
}

/**
 * 获取指定事件
 * @param {string} type 事件类型 (IPC/EventBus)
 * @param {string} name 事件名称
 * @param {string} method 方法类型
 */
function getEvent(type, name, method) {
  if (type === EVENT_TYPE.IPC) {
    return IPC_EVENTS[method]?.[name]
  } else if (type === EVENT_TYPE.EVENTBUS) {
    return EVENTBUS_EVENTS[name]
  }
}

/**
 * 初始化IPC事件
 * @param {string} method 方法类型
 * @param {string} name 事件名称
 * @param {function} callback 回调函数
 * @param {string} mini_pro_id 小程序ID
 */
function initIpcEvent(method, name, callback, mini_pro_id) {
  if (!IPC_EVENTS[method]) {
    IPC_EVENTS[method] = {}
  }
  IPC_EVENTS[method][name] = {
    callbacks: [
      {
        mini_pro_id,
        callback
      }
    ],
    dispatchFunc: (...arguments) => {
      return ipcEventDispatcher({method, name}, ...arguments)
    }
  }
  ipcMain[method](name, IPC_EVENTS[method][name].dispatchFunc)
}

/**
 * 解绑IPC事件
 * @param {string} method 方法类型
 * @param {string} name 事件名称
 * @param {function} callback 回调函数
 * @param {string} mini_pro_id 小程序ID
 */
function unbindIpcEvent(method, name, callback, mini_pro_id) {
  const on_method = EVENT_METHODS[method]
  let event = getEvent(EVENT_TYPE.IPC, name, on_method)
  if (event) {
    event.callbacks = event.callbacks.filter((data) => {
      if (mini_pro_id === data.mini_pro_id) {
        if (callback) {
          return callback !== data.callback
        } else {
          return false
        }
      }
      return true
    })
    if (event.callbacks.length === 0) {
      // 兼容老版本小程序调用
      if (method === 'removeAllListeners') {
        method = 'off'
      }
      ipcMain[method](name, IPC_EVENTS[on_method][name].dispatchFunc)
      delete IPC_EVENTS[on_method][name]
    }
  }
}

module.exports = {
  /**
   * 获取EventManager实例
   * @param {string} mini_pro_id 小程序ID
   */
  getEventManager: function(mini_pro_id) {
    if (!EVENT_MANAGERS[mini_pro_id]) {
      EVENT_MANAGERS[mini_pro_id] = new EventManager(mini_pro_id)
    }
    return EVENT_MANAGERS[mini_pro_id]
  }
}
