<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>小黑盒加速器插件</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/theme.css">
    <link rel="stylesheet" href="../assets/font/iconfont.css">
    <link rel="stylesheet" href="../assets/font/font.css">
  </head>
  <body>
    <div class="shading"></div>
    <div class="recharge-success-cpt">
      <div class="title-wrapper">
        <div class="title">
          VIP充值
        </div>
        <svg id="close-button" class="close pointer" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="Dismiss">
            <path id="Shape" d="M4.08859 4.21569L4.14645 4.14645C4.32001 3.97288 4.58944 3.9536 4.78431 4.08859L4.85355 4.14645L10 9.293L15.1464 4.14645C15.32 3.97288 15.5894 3.9536 15.7843 4.08859L15.8536 4.14645C16.0271 4.32001 16.0464 4.58944 15.9114 4.78431L15.8536 4.85355L10.707 10L15.8536 15.1464C16.0271 15.32 16.0464 15.5894 15.9114 15.7843L15.8536 15.8536C15.68 16.0271 15.4106 16.0464 15.2157 15.9114L15.1464 15.8536L10 10.707L4.85355 15.8536C4.67999 16.0271 4.41056 16.0464 4.21569 15.9114L4.14645 15.8536C3.97288 15.68 3.9536 15.4106 4.08859 15.2157L4.14645 15.1464L9.293 10L4.14645 4.85355C3.97288 4.67999 3.9536 4.41056 4.08859 4.21569L4.14645 4.14645L4.08859 4.21569Z" fill="#424242"/>
          </g>
        </svg>
      </div>
      <div class="success-wrapper">
        <div class="success-tip">
          <i class="iconfont icon-toast-success"></i>
          <p class="success-text">
            充值成功
          </p>
        </div>
        <p class="left-times">
          剩余时间：<span class="left-time"></span>
        </p>
      </div>
      <div class="order-wrapper">
        <p class="label">
          订单编号：<span class="order-id"></span>
        </p>
        <div class="order-content">
          <img class="order-img" src="">
          <div class="order-desc">
            <p class="order-name"></p>
            <p class="order-time"></p>
          </div>
          <p class="order-price">
            ￥<span class="price"></span>
          </p>
        </div>
      </div>
      <div class="gift-wrapper">
        <p class="label">
          额外赠送
        </p>
        <div class="gift-content">

        </div>
      </div>
    </div>
  </body>
  <script src="./common/initTheme.js"></script>
  <script type="module">
    import { getUrlParam, copyToClipboard } from './common/utils.js'
    let params = JSON.parse(getUrlParam('params'))
    let heybox_id = getUrlParam('heybox_id')
    function init() {
      document.getElementById('close-button').addEventListener('click', close)
      document.querySelector('.left-time').innerHTML = params.left_days
      document.querySelector('.order-id').innerHTML = params.order_id
      document.querySelector('.order-img').src = params.img
      document.querySelector('.order-name').innerHTML = params.product_name_cn
      document.querySelector('.order-time').innerHTML = params.charge_time
      document.querySelector('.price').innerHTML = params.price
      console.log('window.location.href', window.location.href)
      console.log('params', params)
      initGiftList()
    }
    function close() {
      window.electronAPI.close('recharge_success')
    }
    function initGiftList() {
      let code = params.code
      if(code) {
        document.querySelector('.gift-wrapper').style.display = 'block'
        let giftListEl = document.querySelector('.gift-content')
        let el = document.createElement('div')
        el.classList.add('coupon-item')
        console.log('time', new Date(params.expire_time * 1000))
        el.innerHTML = `
          <div class="name">${params.type}</div>
          <div class="info">
            <p class="key">${params.code}</p>
            <p class="time">有效期至: <span>${params.expire_time}</span></p>
          </div>
          <button class="copy-button opacity-button">复制</button>
          <button class="use-button opacity-button">使用</button>
        `
        el.querySelector('.copy-button').addEventListener('click', () => {
          copyToClipboard(params.code)
        })
        el.querySelector('.use-button').addEventListener('click', () => {
          window.electronAPI.openPage('coupon', {code: params.code, heybox_id})
        })
        giftListEl.appendChild(el)
      }
    }
    
    init()
  </script>
  <style lang="less">
    * {
      box-sizing: border-box;
    }
    .recharge-success-cpt {
      width: 100%;
      height: 100%;
      padding: 0 24px 24px;
      background-color: var(--nb1r);
      border-radius: 16px;
      overflow: hidden;
      .title-wrapper {
        height: 52px;
        margin-bottom: 28px;
      }
      .success-wrapper {
        margin-bottom: 28px;
        .success-tip {
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 14px;
          margin-bottom: 7px;
          .iconfont {
            font-size: 24px;
            color: var(--success);
          }
          .success-text {
            font-size: 16px;
            color: var(--nf1r);
          }
        }
        .left-times {
          font-size: 14px;
          color: var(--nf4r);
          line-height: 14px;
          text-align: center;
          .left-time {
            color: var(--bf2r);
          }
        }
      }
      .gift-wrapper {
        display: none;
      }
      .label {
        color: var(--nf3r);
        font-size: 14px;
        line-height: 20px;
        margin-bottom: 4px;
      }
      .order-wrapper {
        margin-bottom: 8px;
        .order-content {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          padding: 12px;
          gap: 12px;
          background-color: var(--nb2r);
          border-radius: 4px;
          .order-img {
            width: 126px;
            height: 63px;
            border-radius: 4px;
          }
          .order-desc {
            flex: 1;
            .order-name {
              font-size: 14px;
              font-weight: 600;
              line-height: 20px;
              color: var(--nf1r);
              margin-bottom: 4px;
            }
            .order-time {
              font-size: 12px;
              line-height: 16px;
              color: var(--nf3r);
            }
          }
          .order-price {
            font-size: 14px;
            font-weight: 700;
            line-height: 34px;
            color: var(--nf1r);
            font-family: 'Roboto';
            .price {
              font-size: 28px;
              font-weight: 500;
            }
          }
        }
      }
      .coupon-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 12px;
        gap: 12px;
        background-color: var(--nb2r);
        border-radius: 10px;
        margin-top: 8px;
        .name {
          color: var(--coupon-label);
          font-size: 14px;
          min-width: 42px;
          line-height: 20px;
          font-weight: 600;
        }
        .info {
          flex: 1;
        }
        .key {
          font-size: 14px;
          line-height: 20px;
          color: var(--nf1r);
          margin-bottom: 4px;
        }
        .time {
          font-size: 12px;
          line-height: 16px;
          color: var(--nf3r);
          
        }
        .desc {
          font-size: 12px;
          line-height: 16px;
          color: var(--nf3r);
        }
        .state {
          font-size: 12px;
          line-height: 16px;
          color: var(--nf3r);
        }
      }
    }
  </style>
</html>
