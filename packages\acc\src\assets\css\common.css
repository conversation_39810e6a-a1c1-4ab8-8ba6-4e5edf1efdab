body {
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  position: relative;
  margin: 0;
  user-select: none;
  .shading {
    width: 157px;
    height: 123px;
    position: absolute;
    top: 0;
    right: 23px;
    background-image: url('https://imgheybox.max-c.com/oa/2025/04/24/********************************.png');
    background-size: 100% 100%;
  }
}

*::-webkit-scrollbar {
  height: 0;
  width: 8px;
}
/* 两个滚动条交接处 -- x轴和y轴 */
*::-webkit-scrollbar-corner {
  background-color: transparent;
}
/* 滚动条滑块 */
*::-webkit-scrollbar-thumb {
  border-radius: 20px;
  /* box-shadow: 6px 0 0 var(--dark-op-2) inset; */
  background: var(--dark-op-1);
  /* border: rgba(0, 0, 0, 0) 2px solid; */
}
/* 滚动条轨道 */
*::-webkit-scrollbar-track {
  background: transparent;
}

body[theme="light"] {
  .shading {
    background-image: url('https://imgheybox.max-c.com/oa/2025/04/24/********************************.png');
  }
}

button {
  border-style: none;
  cursor: pointer;
  padding: 0;
  -webkit-app-region: no-drag;
}
button.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}
.close {
  -webkit-app-region: no-drag;
}

p {
  margin: 0;
}

input[type="radio" i] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border: 1px solid var(--dark-op-3);
  border-radius: 50%;
  vertical-align: middle;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0;
}

input[type="radio" i]::before {
  content: "";
  display: block;
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  opacity: 0;
}

input[type="radio" i]:checked {
  border: 5px solid rgba(1, 107, 197, 1);
}

input[type="radio" i]:checked::before {
  opacity: 1;
}

input[type="radio" i]:focus-visible {
  outline: 2px solid var(--dark-op-3);
  outline-offset: 2px;
}

input[type="checkbox" i] {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  border: 2px solid var(--ns3r);
  border-radius: 3px;
  background: transparent;  /* 设置透明背景 */
  vertical-align: middle;
  position: relative;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="checkbox" i]:checked {
  background: var(--bf2r);  /* 选中状态半透明背景 */
  border-color: var(--bf2r);
}

input[type="checkbox" i]::before {
  content: "\e74a";  /* 使用iconfont的勾选图标 */
  font-family: "iconfont" !important;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s;
}

input[type="checkbox" i]:checked::before {
  opacity: 1;
}

input[type="checkbox" i]:focus-visible {
  outline: none;
}

input {
  -webkit-app-region: no-drag;
}

.title-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  padding-top: 24px;
  margin-bottom: 18px;
  height: 28px;
  z-index: 1;
  -webkit-app-region: drag;
  .title {
    color: var(--nf1r);
    font-size: 20px;
    font-weight: 600;
    line-height: 28px;
    .iconfont {
      font-size: 20px;
    }
    .sub-title {
      margin-left: 8px;
      font-size: 12px;
      font-weight: 400;
      color: var(--nf4r);
      line-height: 12px;
    }
  }
  .title.pointer {
    -webkit-app-region: no-drag;
  }
  svg.close {
    width: 20px;
    height: 20px;
    path {
      fill: var(--nf2r) !important;
    }
  }
  button {
    font-size: 12px;
    font-weight: 500;
    color: var(--nf4r);
  }
}

.input-wrapper {
  box-sizing: border-box;
  height: 40px;
  padding: 0 12px;
  border: 1px solid var(--nsdr);
  background-color: var(--nb1r);
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  input {
    flex: 1;
    height: 20px;
    border: none;
    outline: none;
    background-color: transparent;
    color: var(--nf1r);
    font-size: 14px;
    line-height: 20px;
  }
  button {
    height: 32px;
    padding: 0 12px;
    line-height: 32px;
    background-color: var(--dark-op-1);
    border-radius: 4px;
    font-size: 12px;
    color: var(--nf1r);
  }
}
.input-wrapper input::placeholder {
  color: var(--nf4r);
}
.button-wrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 8px;
  button {
    width: 96px;
    height: 36px;
    line-height: 36px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
  }
}
button.full-button {
  width: 100%;
}
.primary-button {
  width: 96px;
  height: 32px;
  text-align: center;
  background: linear-gradient(90deg, #3F81F4 0%, #966FF7 100%);
  color: white;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
}
.default-button {
  width: 96px;
  height: 32px;
  text-align: center;
  border-radius: 4px;
  border: 1px solid var(--ns1r);
  background-color: var(--nb1r);
  color: var(--nf1r);
}
.ghost-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  background-color: transparent;
  color: var(--nf3r);
  cursor: pointer;
  font-size: 12px;
  line-height: 16px;
  .iconfont {
    font-size: 16px;
  }
  p {
    line-height: 12px;
  }
  svg path {
    fill: var(--nf3r);
  }
}
.opacity-button {
  padding: 0 12px;
  background-color: var(--dark-op-1);
  border-radius: 4px;
  font-size: 12px;
  line-height: 32px;
  color: var(--nf1r);
}
.pointer {
  cursor: pointer;
}
a {
  -webkit-app-region: no-drag;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: var(--nf4r);
  text-decoration: none;
  cursor: pointer;
}
.link {
  color: var(--link);
}
.underline {
  text-decoration: underline;
}
.required {
  color: var(--danger);
}
.tip {
  color: var(--nf3r);
  font-size: 12px;
  line-height: 20px;
  margin: 0 0 2px;
}
.selector-wrapper {
  position: relative;
  .selector-tab {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    gap: 4px;
    color: var(--nf2r);
    font-size: 14px;
    padding: 0 12px;
    height: 32px;
    border-radius: 4px;
    background-color: var(--dark-op-1);
    line-height: 32px;
    cursor: pointer;
    .iconfont {
      font-size: 12px;
      transition: all 0.2s ease;
    }
  }
  .selector-content {
    position: absolute;
    box-sizing: border-box;
    top: 42px;
    left: 0;
    width: 100%;
    padding: 4px;
    border-radius: 4px;
    background-color: var(--nb2r);
    box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.12), 0px 0px 2px 0px rgba(0, 0, 0, 0.12);
    z-index: 9;
    .option {
      padding: 6px;
      border-radius: 2px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 6px;
      color: var(--nf2r);
      font-size: 14px;
      .iconfont {
        font-size: 16px;
        opacity: 0;
      }
    }
    .option:hover {
      background-color: var(--nb2h);
      color: var(--nf2h);
    }
    .option.selected {
      .iconfont {
        opacity: 1;
      }
    }
  }
}
.selector-wrapper.unexpand {
  .selector-tab {
    .iconfont {
      transform: rotate(180deg);
    }
  }
  .selector-content {
    display: none;
  }
}

.toast-container {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toast {
  box-sizing: border-box;
  height: 56px;
  padding: 19px 24px;
  max-width: 500px;
  background-color: var(--nb2r);
  color: var(--nf1r);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: toastIn 0.3s ease;
  gap: 10px;
  border: 1px solid var(--dark-op-2);
  box-shadow: 0px 3px 15px 0px rgba(0, 0, 0, 0.35);
  font-size: 14px;
  font-weight: 700;
  .iconfont {
    font-size: 18px;
  }
  .icon-toast-success {
    color: var(--success);
  }
  .icon-toast-warn {
    color: var(--danger);
  }
  p {
    white-space: nowrap;
  }
}

@keyframes toastIn {
  from { transform: translateY(0); opacity: 0 }
  to { transform: translateY(10px); opacity: 1 }
}

.toast.out {
  animation: toastOut 0.3s ease forwards;
}

@keyframes toastOut {
  to { transform: translateY(0); opacity: 0 }
}

.toast-icon {
  font-family: 'iconfont';
  font-size: 18px;
}

