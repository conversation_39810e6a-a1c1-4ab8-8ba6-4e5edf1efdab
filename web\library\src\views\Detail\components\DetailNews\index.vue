<template>
  <div class="cpt-detail-news">
    <div
      class="comp-title news-title"
      style="margin-bottom: 0"
    >
      动态
    </div>
    <div class="news-wrapper">
      <component
        class="news-item"
        v-for="(newItem, index) in news"
        :key="index"
        :is="getNewComponentType(newItem)"
        :newsData="newItem.data"
      >
      </component>
    </div>
  </div>
</template>

<script setup name="DetailNews">
import { defineProps, defineAsyncComponent } from 'vue';
const BbsContent = defineAsyncComponent(() =>
  import('./components/BbsContent/index.vue')
);
const AchievementNews = defineAsyncComponent(() =>
  import('./components/AchievementNews/index.vue')
);
const ActionNews = defineAsyncComponent(() =>
  import('./components/ActionNews/index.vue')
);
const BbsComment = defineAsyncComponent(() =>
  import('./components/BbsComment/index.vue')
);

const props = defineProps({
  news: {
    type: Array,
    default: () => [],
  },
});

const getNewComponentType = (item) => {
  switch (item.type) {
    case 'bbs':
      return BbsContent;
    case 'achievement':
      return AchievementNews;
    case 'action':
      return ActionNews;
    case 'comment':
      return BbsComment;
  }
};
</script>

<style lang="scss">
.cpt-detail-news {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 8px;
  // background: var(---general-color-primary-0, #fff);

  padding-bottom: 70px;
  .news-title {
    width: 100%;
    background: var(---general-color-primary-0, #fff);
    padding-left: 20px;
    padding-top: 14px;
  }

  .news-wrapper {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 4px;

    .news-item {
      padding: 14px 20px;
      background: var(---general-color-primary-0, #fff);
    }
  }
}
</style>
