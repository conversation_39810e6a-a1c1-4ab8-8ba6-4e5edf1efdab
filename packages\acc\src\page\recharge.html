<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>小黑盒加速器插件</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/theme.css">
    <link rel="stylesheet" href="../assets/font/iconfont.css">
    <link rel="stylesheet" href="../assets/font/font.css">
  </head>
  <body>
    <div class="shading"></div>
    <div class="recharge-cpt">
      <div class="title-wrapper">
        <div class="title">
          小黑盒加速器会员购买
        </div>
        <div class="button-list">
          <!-- <button id="recharge-success-button" class="opacity-button">充值成功</button> -->
          <button id="recharge-history-button" class="opacity-button">充值记录</button>
          <button id="coupon-button" class="opacity-button">会员卡券</button>
          <svg id="close-button" class="close pointer" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g id="Dismiss">
              <path id="Shape" d="M4.08859 4.21569L4.14645 4.14645C4.32001 3.97288 4.58944 3.9536 4.78431 4.08859L4.85355 4.14645L10 9.293L15.1464 4.14645C15.32 3.97288 15.5894 3.9536 15.7843 4.08859L15.8536 4.14645C16.0271 4.32001 16.0464 4.58944 15.9114 4.78431L15.8536 4.85355L10.707 10L15.8536 15.1464C16.0271 15.32 16.0464 15.5894 15.9114 15.7843L15.8536 15.8536C15.68 16.0271 15.4106 16.0464 15.2157 15.9114L15.1464 15.8536L10 10.707L4.85355 15.8536C4.67999 16.0271 4.41056 16.0464 4.21569 15.9114L4.14645 15.8536C3.97288 15.68 3.9536 15.4106 4.08859 15.2157L4.14645 15.1464L9.293 10L4.14645 4.85355C3.97288 4.67999 3.9536 4.41056 4.08859 4.21569L4.14645 4.14645L4.08859 4.21569Z" fill="#424242"/>
            </g>
          </svg>
        </div>
      </div>
      <div class="user-wrapper">
        <div class="avatar">
          <img id="avatar" src="">
        </div>
        <div class="user-info">
          <div class="username"></div>
          <div class="time"></div>
        </div>
      </div>
      <div class="deposit-type-wrapper">
        <div class="scroll-list">
          <div class="deposit-type-content">
          </div>
        </div>
        <div class="scroll-left-button">
          <i class="iconfont icon-arrow-left-bold"></i>
        </div>
        <div class="scroll-right-button">
          <i class="iconfont icon-arrow-right-bold"></i>
        </div>
      </div>
      <div class="qr-code-wrapper">
        <div class="qr-code-inner scan">
          <div class="scan-desc">
            <svg id="wechat-tag" xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M1 6.28571C1 3.91914 3.35025 2 6.25 2C8.812 2 10.9426 3.49914 11.4046 5.48086C11.1491 5.45 10.8901 5.42857 10.625 5.42857C7.72962 5.42857 5.375 7.35114 5.375 9.71429C5.37596 9.98527 5.40825 10.2553 5.47125 10.5191C5.02238 10.4651 4.60237 10.3494 4.19812 10.2089L2.3125 11L3.00463 9.644C1.78575 8.86057 1 7.65029 1 6.28571ZM3.84375 4.57143C3.84375 4.92647 4.13756 5.21429 4.5 5.21429C4.86244 5.21429 5.15625 4.92647 5.15625 4.57143C5.15625 4.21639 4.86244 3.92857 4.5 3.92857C4.13756 3.92857 3.84375 4.21639 3.84375 4.57143ZM8 5.21429C7.63756 5.21429 7.34375 4.92647 7.34375 4.57143C7.34375 4.21639 7.63756 3.92857 8 3.92857C8.36244 3.92857 8.65625 4.21639 8.65625 4.57143C8.65625 4.92647 8.36244 5.21429 8 5.21429ZM10.625 6.28571C13.0409 6.28571 15 7.82086 15 9.71429C15 10.8106 14.3315 11.7757 13.3095 12.4031L14.125 14L11.7669 13.01C11.4002 13.088 11.0223 13.1429 10.625 13.1429C8.20913 13.1429 6.25 11.6077 6.25 9.71429C6.25 7.82086 8.20913 6.28571 10.625 6.28571ZM8.65625 8.85714C8.65625 9.21218 8.95006 9.5 9.3125 9.5C9.67494 9.5 9.96875 9.21218 9.96875 8.85714C9.96875 8.5021 9.67494 8.21429 9.3125 8.21429C8.95006 8.21429 8.65625 8.5021 8.65625 8.85714ZM11.9375 9.5C11.5751 9.5 11.2812 9.21218 11.2812 8.85714C11.2812 8.5021 11.5751 8.21429 11.9375 8.21429C12.2999 8.21429 12.5938 8.5021 12.5938 8.85714C12.5938 9.21218 12.2999 9.5 11.9375 9.5Z" fill="#46B222"/>
            </svg>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M9.92774 9.50598C9.92774 9.50598 10.2705 8.88445 10.6623 7.73704C11.054 6.54181 11.103 5.92033 11.103 5.92033L8.01791 5.87248V4.72513L11.7396 4.67727V3.86454H8.01791V2H6.206V3.86454H2.72916V4.67727L6.206 4.62947V5.87248H3.41475V6.54181H9.1932C9.1932 6.54181 9.14418 7.06776 8.89933 7.73704C8.65448 8.40636 8.45859 8.9801 8.45859 8.9801C8.45859 8.9801 5.76526 7.92827 4.34512 7.92827C2.92505 7.92827 1.1621 8.54981 1.01522 10.3666C0.868287 12.1832 1.79871 13.1873 3.21886 13.5219C4.63898 13.8566 5.86323 13.5219 6.94053 12.9004C8.06687 12.3267 9.14418 10.9402 9.14418 10.9402L14.8737 14C14.8737 14 15.2165 13.4263 15.5103 12.8526C15.8041 12.2789 16 11.6574 16 11.6574L9.92774 9.50598ZM4.00236 12.5657C1.99461 12.5657 1.55388 11.4661 1.55388 10.7012C1.55388 9.93628 1.99461 9.07569 3.70856 8.93224C5.42249 8.78885 7.77306 10.2709 7.77306 10.2709C7.77306 10.2709 6.05913 12.5657 4.00236 12.5657Z" fill="#2B74D9"/>
            </svg>
            <div class="text">扫一扫</div>
          </div>
          <canvas id="charge-v2-qr-code" width="112" height="112"></canvas>
        </div>
        <div class="qr-code-text">
          <div class="current-price">
            <span class="text">扫码支付:</span>
            <span class="unit">￥</span><span id="price"></span>
          </div>
          <div class="coupon-selector selector-wrapper unexpand"></div>
          <div class="des">买家须知: 
            <a id="privacy-terms">《小黑盒加速器服务及隐私条款》</a>
            <a id="auto-renewal-terms">《小黑盒加速器自动续费服务规则》</a>
          </div>
        </div>
      </div>
    </div>
  </body>
  <script src="./common/initTheme.js"></script>
  <script type="module">
    import { getDepositType, getPayToken, getPayMonthToken, queryOrderPayStateV2, checkoutChargeState, getCouponList, getChargePrice } from './api/index.js'
    import { formatDate, getUrlParam } from './common/utils.js'
    import { toastError } from './common/toast.js'
    const QRCode = require('qrcode')
    let chargeSetMeal = []
    let activeChargeMeal = 0
    let isMonthPay = false
    let heybox_id = getUrlParam('heybox_id')
    let version = ''
    let wechatPayState = 0
    let _chargetokenV2 = ''
    let checkPayStateV2Count = 0
    let payStateCode = 0
    let chargeStep = 0
    let checkPayStateV2Timeout = null
    let chargeStateDetail = null
    let _chosenMeal = null
    let _chosenMealCoupon = null
    let selected_coupon_id = null
    let couponList = []
    let canUserCouponList = []
    let currentPrice = 0
    let isCouponSelectorExpand = false

    async function init() {
      console.log('init', heybox_id)
      document.getElementById('close-button').addEventListener('click', close)
      // document.getElementById('recharge-success-button').addEventListener('click', () => openPage('recharge_success', {heybox_id, params: JSON.stringify({
      //   code: "3XKP2HF4L4XU3J8E",
      //   img: "https://imgheybox.max-c.com/oa/2024/03/01/********************************.png",
      //   order_id: "v2alipayproxy153255061745914602603",
      //   price: 139,
      //   charge_time: "2025-04-29 16:16",
      //   days: 4650,
      //   left_days: "219天",
      //   state: 1,
      //   product_name_cn: "双学期卡",
      //   expire_time: "2026-05-06 16:17",
      //   type: "150天卡"
      // })}))
      document.getElementById('recharge-history-button').addEventListener('click', () => openPage('recharge_history', {heybox_id}))
      document.getElementById('coupon-button').addEventListener('click', () => openPage('coupon', {heybox_id}))
      document.getElementById('privacy-terms').addEventListener('click', () => openURL('https://accapi.xiaoheihe.cn/account/booster_regular_charge_terms/'))
      document.getElementById('auto-renewal-terms').addEventListener('click', () => openURL('https://accapi.xiaoheihe.cn/account/booster_monthly_charge_terms/'))
      document.querySelector('.scroll-left-button').addEventListener('click', () => {
        document.querySelector('.scroll-list').scrollTo({
          left: 0,
          behavior: 'smooth'
        })
        document.querySelector('.scroll-right-button').style.display = 'block'
        document.querySelector('.scroll-left-button').style.display = 'none'
        document.querySelector('.deposit-type-wrapper').classList.remove('hidden-overflow')
      })
      document.querySelector('.scroll-right-button').addEventListener('click', () => {
        document.querySelector('.scroll-list').scrollTo({
          left: document.querySelector('.scroll-list').scrollWidth,
          behavior: 'smooth'
        })
        document.querySelector('.scroll-left-button').style.display = 'block'
        document.querySelector('.scroll-right-button').style.display = 'none'
        document.querySelector('.deposit-type-wrapper').classList.add('hidden-overflow')
      })
      version = await window.electronAPI.getVersion()
      await getCurrentUserInfo()
      await initDepositType()
      await initCouponSelector()
    }
    function openPage(page, params) {
      window.electronAPI.openPage(page, params)
    }
    function close() {
      window.electronAPI.close('recharge')
    }
    async function rechargeSuccess() {
      console.log('rechargeSuccess')
      let res = await window.electronAPI.recharge({type: 'success'})
      console.log('rechargeSuccess', res)
    }
    async function rechargeFailed() {
      console.log('rechargeFailed')
      let res = await window.electronAPI.recharge({type: 'failed'})
      console.log('rechargeFailed', res)
    }

    async function getCurrentUserInfo() {
      let res = await window.electronAPI.getUserInfo()
      if(res.status === 'ok') {
        let userInfo = res.result
        console.log('userInfo', userInfo)
        let userWrapper = document.querySelector('.user-wrapper')
        let avatarEl = document.getElementById('avatar')
        avatarEl.src = userInfo.avatar
        userWrapper.querySelector('.username').innerHTML = userInfo.nickname
        if(userInfo.vip_end_time) {
          userWrapper.querySelector('.time').innerHTML = formatDate(userInfo.vip_end_time * 1000, 'YYYY-MM-DD') + ' 到期'
        } else {
          userWrapper.querySelector('.time').innerHTML = ''
        }
      }
    }

    function openURL(url) {
      window.electronAPI.openInBrowser(url)
    }

    async function initDepositType() {
      let res = await getDepositType()
      console.log('getDepositType', res)
      if(res.status === 'ok') {
        chargeSetMeal = res.result.deposits
        let depositTypeContent = document.querySelector('.deposit-type-content')
        depositTypeContent.style.width = ((chargeSetMeal.length * 119) - 4) + 'px'
        chargeSetMeal.forEach((item, index) => {
          let desositItemEl = document.createElement('div')
          desositItemEl.classList.add('deposit-type-item')
          if(index === 0) {
            desositItemEl.classList.add('selected')
            let priceEl = document.getElementById('price')
            priceEl.innerHTML = item.current
          }
          desositItemEl.style.cursor = 'pointer'
          desositItemEl.innerHTML = `
            <div class="content">
              ${
                item.is_hot ? `
                  <div class="hot-tag">
                    <img src="https://imgheybox.max-c.com/oa/2025/04/25/********************************.png">
                  </div>
                ` : ''
              }
              <div class="discount-tag">
                -${((item.original - item.current) / item.original * 100).toFixed(0)}%
              </div>
              <p class="name">${item.type_name_cn}</p>
              <p class="current"><span class="unit">￥</span>${item.current}</p>
              <p class="original">￥${item.original}</p>
              <div class="discount-tip">
                首充特惠
              </div>
            </div>
          `
          desositItemEl.addEventListener('click', () => {
            chooseDepositType(index)
          })
          depositTypeContent.appendChild(desositItemEl)
        })
        activeChargeMeal = 0
        payHandler()
      }
    }

    function chooseDepositType(index) {
      activeChargeMeal = index
      let depositTypeContent = document.querySelector('.deposit-type-content')
      depositTypeContent.querySelectorAll('.deposit-type-item').forEach(item => {
        item.classList.remove('selected')
      })
      let desositItemEl = depositTypeContent.querySelectorAll('.deposit-type-item')[index]
      desositItemEl.classList.add('selected')
      let priceEl = document.getElementById('price')
      priceEl.innerHTML = chargeSetMeal[index].current
      // payHandler()
      initCanUserCouponList()
      initCouponSelectorDom()
      autoChooseConpon()
    }

    function initCanUserCouponList() {
      let currentMeal = chargeSetMeal[activeChargeMeal]
      canUserCouponList = couponList.filter(item => {
        return item.support_deposit_type.split(',').includes(currentMeal.type) || item.support_deposit_type === 'all'
      })
      console.log('canUserCouponList', canUserCouponList)
      let couponSelector = document.querySelector('.coupon-selector')
      if(canUserCouponList.length > 0) {
        couponSelector.style.display = 'block'
      } else {
        couponSelector.style.display = 'none'
        return
      }
    }
    // 自动选择最便宜的优惠券
    function autoChooseConpon () {
      chooseCheapestCoupon(() => {
        getOrderPrice()
      }, payHandler)
    }
    function chooseCheapestCoupon(cb, noCouponCb) {
      console.log(canUserCouponList, activeChargeMeal)
      if (canUserCouponList.length > 0) {
        let coupon = null, discount = 100
        canUserCouponList.forEach(v => {
          if (v.discount < discount) {
            coupon = v
            discount = v.discount
          }
        })
        if (coupon) {
          selected_coupon_id = coupon.id
          let couponSelector = document.querySelector('.coupon-selector')
          couponSelector.querySelector('.selector-tab').innerHTML = `
            ${coupon.name}
            <i class="iconfont icon-arrow-up-bold"></i>
          `
          couponSelector.querySelector(`[type="radio"][value="${coupon.id}"]`).checked = true
          cb && cb()
        }
      } else {
        selected_coupon_id = ''
        // let couponSelector = document.querySelector('.coupon-selector')
        // couponSelector.querySelector('.selector-tab').innerHTML = ''
        // selected_coupon = {}
        console.log('noCouponCb')
        noCouponCb && noCouponCb()
      }
    }

    function getOrderPrice () {
      getChargePrice({
        coupon_id: String(selected_coupon_id),
        product_name: chargeSetMeal[activeChargeMeal].type
      }).then((res) => {
        console.log('getChargePrice', res)
        if (res.status == 'ok') {
          let priceEl = document.getElementById('price')
          priceEl.innerHTML = parseFloat(res.result.p)
          currentPrice = parseFloat(res.result.p)
          payHandler()
        } else {
          let priceEl = document.getElementById('price')
          priceEl.innerHTML = chargeSetMeal[activeChargeMeal].current
          currentPrice = 0
        }
      })
    }

    function payHandler() {
      var meal = chargeSetMeal[activeChargeMeal]
      if (_chosenMeal === meal && _chosenMealCoupon === selected_coupon_id) return
      _chosenMeal = meal
      _chosenMealCoupon = selected_coupon_id
      // this._chosenMealInvite = this.discountCode
      console.log('meal', meal)
      if (meal.type.endsWith('_cycle')) {
        isMonthPay = true
        document.getElementById('wechat-tag').style.display = 'none'
      } else {
        isMonthPay = false
        document.getElementById('wechat-tag').style.display = 'block'
      }
      getHeyboxPayToken(meal)
    }

    function getHeyboxPayToken(meal) {
      console.log('currentPrice', currentPrice)
      var params = {
        amount_paid: currentPrice || meal.current,
        cycle_paid: meal.cycle_price,
        heybox_id: heybox_id,
        product_name: meal.type,
        version: version,
      }
      console.log('params', params)
      // if (this.discountCode) {
      //   params.invite_id = this.discountCode
      // }
      if (selected_coupon_id) {
        params.coupon_id = selected_coupon_id
      }
      // if (this.from_charge_tip) {
      //   params.from_charge_tip = 1
      // }
      wechatPayState = 0
      // 连续包月和正常支付使用不同的获取token接口
      let requestFunc = isMonthPay ? getPayMonthToken : getPayToken
      requestFunc(params).then(res => {
        console.log('getHeyboxPayToken', res)
        if (res.data.status == "ok") {
          let host = 'https://api.xiaoheihe.cn'
          const mobil_url = `${host}/pay/wx_proxy_charge_page/?token=` + res.data.result.token + (isMonthPay ? '&is_cyc=1' : '')
          _chargetokenV2 = res.data.result.token
          getQrCodeAndToCanvasV2(mobil_url)
        } else {
          chargeStep = 2
          wechatPayState = 1
        }
      })
    }
    function getQrCodeAndToCanvasV2 (url) {
      QRCode.toCanvas(document.getElementById('charge-v2-qr-code'), url, {
        width: 112,
      }, (err) => {
        if (err) {
          console.log(err)
          toastError('err')
        }
        checkPayStateV2Count = 0
        payStateCode = 0
        checkPayStateV2()
      })
    }
    function checkPayStateV2 () {
      if (payStateCode) return
      // 300次对应10分钟
      if (checkPayStateV2Count > 300) return
      let axiosFunc, params
      if (isMonthPay) {
        axiosFunc = checkoutChargeState
        params = {
          params: {
            'heybox_id': heybox_id
          }
        }
      } else {
        axiosFunc = queryOrderPayStateV2
        params = {
          'token': _chargetokenV2
        }
      }
      axiosFunc(params).then(res => {
        res = res.data || res
        // console.log('checkPayStateV2', res)
        if (res.status === 'ok') {
          let state = res.result.state
          if (state === 1) {
            payStateCode = 1
            chargeStateDetail = res.result
            console.log('chargeStateDetail', chargeStateDetail)
            window.electronAPI.recharge({type: 'success', params: chargeStateDetail})
            window.electronAPI.openPage('recharge_success', {heybox_id, params: JSON.stringify(chargeStateDetail)})
          } else if (state === 0) {
            clearTimeout(checkPayStateV2Timeout)
            checkPayStateV2Count++
            checkPayStateV2Timeout = setTimeout(() => {
              checkPayStateV2()
            }, 2000)
          }
        } else {
          payStateCode = 0
          toastError('请检查网络状况，并刷新查看是否充值成功')
        }
      })
    }
    async function initCouponSelector() {
      let res = await getCouponList()
      if (res.status == 'ok') {
        console.log('getCouponList', res)
        couponList = res.result.coupons
        initCanUserCouponList()
        initCouponSelectorDom()
        autoChooseConpon()
      }
    }
    function initCouponSelectorDom() {
      let couponSelector = document.querySelector('.coupon-selector')
      console.log('canUserCouponList', canUserCouponList)
      couponSelector.innerHTML = `
        <div class="selector-tab">
        </div>
        <div class="selector-content">
          ${canUserCouponList.map(item => `
            <div class="option" value="${item.id}">
              <input type="radio" name="coupon" value="${item.id}">
              <div class="option-content">
                <p class="name">${item.discount / 10}折 ${item.name}</p>
                <p class="time">${item.expire}</p>
                <p class="desc">${item.desc}</p>
              </div>
            </div>
          `).join('')}
        </div>
      `
      let couponSelectorTab = couponSelector.querySelector('.selector-tab')
      let couponSelectorContent = couponSelector.querySelector('.selector-content')
      const unexpandCouponSelector = (e) => {
        if(!couponSelector.contains(e.target)) {
          couponSelector.classList.add('unexpand')
          isCouponSelectorExpand = false
        }
      }
      couponSelectorTab.addEventListener('click', () => {
        if(isCouponSelectorExpand) {
          couponSelector.classList.add('unexpand')
          document.removeEventListener('click', unexpandCouponSelector)
        } else {
          couponSelector.classList.remove('unexpand')
          document.addEventListener('click', unexpandCouponSelector)
        }
        isCouponSelectorExpand = !isCouponSelectorExpand
      })
      couponSelectorContent.querySelectorAll('.option').forEach(el => {
        el.addEventListener('click', () => {
          let radio = el.querySelector('[type="radio"]')
          if(radio) {
            radio.checked = true
            selected_coupon_id = Number(radio.value)
            couponSelectorTab.innerHTML = `
              ${canUserCouponList.find(item => item.id === selected_coupon_id).name}
              <i class="iconfont icon-arrow-up-bold"></i>
            `
            couponSelector.classList.add('unexpand')
            document.removeEventListener('click', unexpandCouponSelector)
            isCouponSelectorExpand = false
            getOrderPrice()
          }
        })
      })
    }
    init()
  </script>
  <style lang="less">
    .recharge-cpt {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 24px 24px;
      background-color: var(--nb1r);
      border-radius: 16px;
      overflow: hidden;
    }
    .title-wrapper {
      height: 32px;
      margin-bottom: 8px;
      button {
        width: 72px;
        height: 32px;
        font-size: 12px;
        line-height: 32px;
      }
      .button-list {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 10px;
      }
    }
    .user-wrapper {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 12px;
      padding-bottom: 10px;
      border-bottom: 1px solid var(--ns3r);
      margin-bottom: 11px;
      .avatar {
        width: 56px;
        height: 56px;
        img {
          width: 56px;
          height: 56px;
          border-radius: 50%;
        }
      }
      .user-info {
        .username {
          font-size: 16px;
          font-weight: 600;
          color: var(--nf1r);
          line-height: 22px;
        }
        .time {
          font-size: 14px;
          color: var(--nf2r);
          line-height: 20px;
        } 
      }
    }
    .hidden-overflow {
      overflow: hidden;
    }
    .deposit-type-wrapper {
      margin-bottom: 8px;
      position: relative;
      .scroll-list {
        overflow-x: hidden;
        overflow-y: hidden;
        padding-left: 8px;
        margin-left: -8px;
      }
      .scroll-left-button, .scroll-right-button {
        position: absolute;
        top: 0;
        bottom: 0;
        width: 24px;
        height: 146px;
        line-height: 146px;
        text-align: center;
        cursor: pointer;
        z-index: 1;
        i {
          font-size: 14px;
          color: var(--nf1r);
        }
      }
      .scroll-left-button {
        display: none;
        left: 0;
        background: var(--scroll-mask-left);
      }
      .scroll-right-button {
        right: 0;
        background: var(--scroll-mask-right);
      }
      .deposit-type-content {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        gap: 4px;
      }
      .deposit-type-item {
        width: 114px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: flex-start;
        padding: 4px;
        border-radius: 11px;
        .content {
          width: 100%;
          padding-top: 15px;
          border-radius: 8px;
          position: relative;
          background-color: var(--deposit-bg);
          .hot-tag img{
            width: 20px;
            height: 20px;
            transform: rotate(-30deg);
            position: absolute;
            left: -10.16px;
            top: -5px;
          }
          .discount-tag {
            position: absolute;
            top: -2px;
            right: -2px;
            width: 38px;
            height: 17px;
            background-color: var(--dark-brand-fill);
            border-radius: 12px;
            font-size: 12px;
            color: white;
            line-height: 17px;
            text-align: center;
          }
          .name {
            font-size: 12px;
            color: var(--nf3r);
            line-height: 20px;
            margin-bottom: 6px;
            text-align: center;
          }
          .current {
            margin-bottom: 6px;
            text-align: center;
            font-family: Roboto;
            font-size: 27px;
            font-weight: 700;
            line-height: 32px;
            color: transparent;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-image: var(--price-gradient);
            .unit {
              font-size: 16px;
            }
          }
          .original {
            margin-bottom: 15px;
            font-size: 14px;
            color: var(--nf3r);
            line-height: 16px;
            text-align: center;
            font-family: Roboto;
            text-decoration-line: line-through;
          }
          .discount-tip {
            width: 100%;
            height: 24px;
            background-color: var(--price-discount);
            border-radius: 0 0 8px 8px;
            font-size: 13px;
            color: black;
            line-height: 24px;
            text-align: center;
          }
        }
        &.selected {
          border: 2px solid var(--dark-brand-line);
          padding: 2px;
        }
      }
    }
    
    .qr-code-wrapper {
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      .qr-code-inner {
        background: #FFFFFF;
        border-radius: 4px;
        width: 112px;
        height: 112px;
        position: relative;
        overflow: hidden;
        flex-shrink: 0;
        &.scan {
          padding: 6px 6px 6px 24px;
          border-radius: 6px;
          background: #14161C;
          #charge-v2-qr-code {
            border-radius: 4px;
          }
          .scan-desc {
            position: absolute;
            top: 50%;
            left: 2px;
            height: 100%;
            width: 20px;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            svg {
              margin-bottom: 2px;
            }
            .text {
              color: #87888C;
              font-size: 12px;
              font-weight: 400;
              writing-mode: vertical-rl; /* 垂直排列，从右向左 */
              letter-spacing: 0.2em; /* 调整文字间距 */
            }
          }
          
        }
        #charge-v2-qr-code {
          width: 100%;
          height: 100%;
        }
        .cover {
          position: absolute;
          left: 0;
          top: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.8);
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          i {
            font-size: 30px;
            color: #FFB40A;
            margin-bottom: 9px;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center
          }
          p {
            font-weight: 400;
            font-size: 12px;
            line-height: 20px;
            letter-spacing: 0.04em;
            color: #000;
            &.link {
              cursor: pointer;
              color: #99AEFF;
              text-decoration: underline;
            }
          }
        }
      }
      .qr-code-text {
        margin-left: 12px;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        
        .des {
          width: 399px;
          color: var(--nf3r);
          font-size: 12px;
          line-height: 20px;
        }
        .current-price {
          margin-bottom: 10px;
          .text {
            color: var(--nf1r);
            font-size: 16px;
            font-weight: 700;
            margin-right: 6px;
          }
          .unit {
            font-weight: 700;
            font-size: 16px;
            line-height: 21px;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-image: var(--price-gradient);
          }
          #price {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 800;
            font-size: 27px;
            line-height: 32px;
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-image: var(--price-gradient);
          }
        }
      }
      .coupon-selector {
        display: none;
        margin-bottom: 10px;
        .selector-tab {
          justify-content: space-between;
        }
        .selector-content {
          box-sizing: border-box;
          top: unset;
          bottom: 42px;
          .option {
            .option-content {
              flex: 1;
              .name {
                font-size: 14px;
                color: var(--nf1r);
                margin-bottom: 4px;
                line-height: 20px;
              }
              .time, .desc {
                font-size: 12px;
                color: var(--nf3r);
                line-height: 16px;
              }
            }
          }
        }
      }
    }
  </style>
</html>
