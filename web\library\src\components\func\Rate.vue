<template>
  <div class="cpt-rate">
    <svg
      width="76"
      height="13"
      viewBox="0 0 76 13"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M8.29004 4.4502L12.667 5.08398L9.5 8.1582L10.2471 12.5L6.33301 10.4502L2.41895 12.5L3.16699 8.1582L0 5.08398L4.37598 4.4502L6.33301 0.5L8.29004 4.4502ZM24.124 4.4502L28.5 5.08398L25.333 8.1582L26.0811 12.5L22.167 10.4502L18.2529 12.5L19 8.1582L15.833 5.08398L20.21 4.4502L22.167 0.5L24.124 4.4502ZM39.957 4.4502L44.333 5.08398L41.167 8.1582L41.9141 12.5L38 10.4502L34.0859 12.5L34.833 8.1582L31.667 5.08398L36.043 4.4502L38 0.5L39.957 4.4502ZM55.79 4.4502L60.167 5.08398L57 8.1582L57.7471 12.5L53.833 10.4502L49.9189 12.5L50.667 8.1582L47.5 5.08398L51.876 4.4502L53.833 0.5L55.79 4.4502ZM71.624 4.4502L76 5.08398L72.833 8.1582L73.5811 12.5L69.667 10.4502L65.7529 12.5L66.5 8.1582L63.333 5.08398L67.71 4.4502L69.667 0.5L71.624 4.4502Z"
        fill="#F3F4F5"
      />
      <mask
        id="mask0_337_32337"
        style="mask-type: luminance"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="76"
        height="13"
      >
        <path
          d="M8.29004 4.4502L12.667 5.08398L9.5 8.1582L10.2471 12.5L6.33301 10.4502L2.41895 12.5L3.16699 8.1582L0 5.08398L4.37598 4.4502L6.33301 0.5L8.29004 4.4502ZM24.124 4.4502L28.5 5.08398L25.333 8.1582L26.0811 12.5L22.167 10.4502L18.2529 12.5L19 8.1582L15.833 5.08398L20.21 4.4502L22.167 0.5L24.124 4.4502ZM39.957 4.4502L44.333 5.08398L41.167 8.1582L41.9141 12.5L38 10.4502L34.0859 12.5L34.833 8.1582L31.667 5.08398L36.043 4.4502L38 0.5L39.957 4.4502ZM55.79 4.4502L60.167 5.08398L57 8.1582L57.7471 12.5L53.833 10.4502L49.9189 12.5L50.667 8.1582L47.5 5.08398L51.876 4.4502L53.833 0.5L55.79 4.4502ZM71.624 4.4502L76 5.08398L72.833 8.1582L73.5811 12.5L69.667 10.4502L65.7529 12.5L66.5 8.1582L63.333 5.08398L67.71 4.4502L69.667 0.5L71.624 4.4502Z"
          fill="white"
        />
      </mask>
      <g mask="url(#mask0_337_32337)">
        <rect
          y="0.5"
          width="76"
          height="12"
          fill="#FF9F00"
        />
      </g>
    </svg>
  </div>
</template>

<script setup name="Rate">
import { defineProps } from 'vue';

const props = defineProps({
  rate: {
    type: Number,
    default: 0,
  },
});
</script>

<style lang="scss">
.cpt-rate {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
}
</style>
