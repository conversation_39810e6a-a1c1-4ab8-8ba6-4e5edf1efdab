const { parentPort } = require('worker_threads');
const path = require('path')
const client = require('../../../heybox_js/heyboxacc_x64.node');
const heybox_js_path = path.join(__dirname, '../../../heybox_js')

// 监听主线程消息
parentPort.on('message', async (data) => {
  try {
    const { type, params } = data;
    
    if (type === 'startAcc') {
      if (!client) {
        throw new Error('heyboxAcc module not loaded');
      }
      
      console.log('Worker: Starting StartAcc with params:', params);
      
      // 执行耗时的 StartAcc 方法
      const result = client.heyboxAcc.StartAcc(JSON.stringify(params));
      
      console.log('Worker: StartAcc completed with result:', result);
      
      // 发送结果回主线程
      parentPort.postMessage({
        type: 'startAccResult',
        status: 'ok',
        result: JSON.parse(result)
      });
    } else if (type === 'initAcc') {
      console.log(params)
      const result = client.heyboxAcc.Init(heybox_js_path, JSON.stringify(params));
      console.log('Worker: InitAcc completed with result:', params);
      parentPort.postMessage({
        type: 'initAccResult',
        status: 'ok',
        result: result
      });
    }
  } catch (error) {
    const { type } = data;
    console.error('Worker error:', error);
    
    // 发送错误回主线程
    parentPort.postMessage({
      type: type + 'Result',
      status: 'failed',
      error: error.message
    });
  }
});

// 通知主线程 Worker 已准备就绪
parentPort.postMessage({
  type: 'workerReady',
  success: true
});