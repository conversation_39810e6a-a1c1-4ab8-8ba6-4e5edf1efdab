import { ref, computed, watch } from 'vue';
import { useStore } from 'vuex';
import steamGames from '@/assets/js/steam-games.js';

/**
 * 账户切换
 * @param {Function} emit
 * @param {Array} accounts 接口返回的账户数据
 */
export function useAccountSwitch(emit, accounts = []) {
  const fileInputRef = ref(null);
  const selectedAccountId = ref(null);
  const store = useStore();

  watch(() => accounts, (newAccounts) => {
    if (newAccounts.length > 0) {
      const currentSteamId = store.state.current_account;
      
      if (currentSteamId) {
        const currentAccount = newAccounts.find(acc => acc.account_id === currentSteamId);
        if (currentAccount) {
          selectedAccountId.value = currentAccount.account_id;
          return;
        }
      }
      if (!selectedAccountId.value) {
        selectedAccountId.value = newAccounts[0].account_id;
      }
    }
  }, { immediate: true });

  const accountOptions = computed(() => {
    return accounts.map((account) => ({
      id: account.account_id,
      nickname: account.nickname,
      avatar: account.avatar,
      platform: account.platform,
      playtime_forever: account.playtime_forever,
      owned: account.owned,
      active: account.account_id === store.state.current_account
    }));
  });

  const handleSelectAccount = (account) => {
    selectedAccountId.value = account.id;
    const originalAccount = accounts.find(acc => acc.account_id === account.id);
    emit('selectAccount', originalAccount);
  };

  const handleUpdateShow = (show) => {
    emit('updateShow', show);
  };

  const handleAddLocalFile = async () => {
    try {
      const result = await window.electronAPI.invoke('file:path', {
        filters: [
          { name: '可执行文件', extensions: ['exe'] }
        ],
        properties: ['openFile']
      });

      if (result.status === 'ok' && !result.canceled && result.filePaths && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        const fileName = filePath.split(/[/\\]/).pop().replace(/\.[^/.]+$/, "");

        // 获取所有应用信息，然后根据文件路径匹配
        const appsInfo = await steamGames.getAppsInfo();
        if (!appsInfo.success) {
          console.error('[AccountSwitch] 获取应用列表失败:', appsInfo.error);
          return;
        }

        // 单词匹配
        const matchedApp = appsInfo.apps.find(app => {
          if (!app.displayName) return false;

          const appName = app.displayName.toLowerCase();
          const fileNameLower = fileName.toLowerCase();

          const appWords = appName.split(/\s+/).filter(word => word.length > 2);
          const fileWords = fileNameLower.split(/[^a-z0-9]+/).filter(word => word.length > 2);

          for (const appWord of appWords) {
            for (const fileWord of fileWords) {
              if (appWord.includes(fileWord) || fileWord.includes(appWord)) {
                return true;
              }
            }
          }

          return false;
        });
        console.log('匹配结果:', matchedApp);

        if (!matchedApp) {
          console.warn('[AccountSwitch] 未找到匹配的应用');
          return;
        }

        try {
          const desktopResult = await steamGames.createDesktopShortcut(matchedApp.id);
          if (desktopResult.success) {
            console.log('[AccountSwitch] 桌面快捷方式创建成功');
          } else {
            console.warn('[AccountSwitch] 桌面快捷方式创建失败:', desktopResult.error);
          }
        } catch (error) {
          console.error('[AccountSwitch] 创建桌面快捷方式时出错:', error);
        }

        const fileInfo = {
          name: fileName + '.exe',
          path: filePath
        };

        emit('addLocalFile', fileInfo);
      }
    } catch (error) {
      console.error('[AccountSwitch] 处理本地文件时出错:', error);
    }
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      emit('addLocalFile', file);
      event.target.value = '';
    }
  };

  return {
    fileInputRef,
    accountOptions,
    handleSelectAccount,
    handleUpdateShow,
    handleAddLocalFile,
    handleFileSelect,
  };
}
