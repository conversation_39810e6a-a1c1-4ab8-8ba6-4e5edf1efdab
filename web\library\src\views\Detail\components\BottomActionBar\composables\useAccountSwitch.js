import { ref, computed, watch, onMounted } from 'vue';
import { useStore } from 'vuex';

/**
 * 账户切换
 * @param {Function} emit
 * @param {Array} accounts 接口返回的账户数据
 */
export function useAccountSwitch(emit, accounts = []) {
  const fileInputRef = ref(null);
  const selectedAccountId = ref(null);
  const localExeFile = ref(null);
  const isDragOver = ref(false);
  const store = useStore();

  // 从 store 加载本地文件信息
  const loadLocalExeFile = () => {
    const currentGame = store.state.current_game;
    if (currentGame) {
      const storedFile = window.electronAPI.getStoreData(`local_exe_${currentGame}`, 'library_config');
      if (storedFile) {
        localExeFile.value = storedFile;
      }
    }
  };

  // 保存本地文件信息到 store
  const saveLocalExeFile = (fileInfo) => {
    const currentGame = store.state.current_game;
    if (currentGame) {
      window.electronAPI.setStoreData(`local_exe_${currentGame}`, fileInfo, 'library_config');
      localExeFile.value = fileInfo;
      emit('localFileChanged');
    }
  };

  // 删除本地文件信息
  const removeLocalExeFile = () => {
    const currentGame = store.state.current_game;
    if (currentGame) {
      window.electronAPI.delStoreData(`local_exe_${currentGame}`, 'library_config');
      localExeFile.value = null;
      emit('localFileChanged');
    }
  };

  watch(() => accounts, (newAccounts) => {
    if (newAccounts.length > 0) {
      const currentSteamId = store.state.current_account;
      
      if (currentSteamId) {
        const currentAccount = newAccounts.find(acc => acc.account_id === currentSteamId);
        if (currentAccount) {
          selectedAccountId.value = currentAccount.account_id;
          return;
        }
      }
      if (!selectedAccountId.value) {
        selectedAccountId.value = newAccounts[0].account_id;
      }
    }
  }, { immediate: true });

  // 监听当前游戏变化，加载对应的本地文件
  watch(() => store.state.current_game, () => {
    loadLocalExeFile();
  }, { immediate: true });

  onMounted(() => {
    loadLocalExeFile();
  });

  const accountOptions = computed(() => {
    return accounts.map((account) => ({
      id: account.account_id,
      nickname: account.nickname,
      avatar: account.avatar,
      platform: account.platform,
      playtime_forever: account.playtime_forever,
      owned: account.owned,
      active: account.account_id === store.state.current_account
    }));
  });

  const handleSelectAccount = (account) => {
    selectedAccountId.value = account.id;
    const originalAccount = accounts.find(acc => acc.account_id === account.id);
    emit('selectAccount', originalAccount);
  };

  const handleUpdateShow = (show) => {
    emit('updateShow', show);
  };

  const handleAddLocalFile = async () => {
    try {
      const result = await window.electronAPI.invoke('file:path', {
        filters: [
          { name: '可执行文件', extensions: ['exe'] }
        ],
        properties: ['openFile']
      });

      if (result.status === 'ok' && !result.canceled && result.filePaths && result.filePaths.length > 0) {
        const filePath = result.filePaths[0];
        const fileName = filePath.split(/[/\\]/).pop();

        const fileInfo = {
          name: fileName,
          path: filePath
        };

        saveLocalExeFile(fileInfo);
        emit('addLocalFile', fileInfo);
      }
    } catch (error) {
      console.error('[AccountSwitch] 处理本地文件时出错:', error);
    }
  };

  const handleFileSelect = (event) => {
    const file = event.target.files[0];
    if (file) {
      const fileInfo = {
        name: file.name,
        path: file.path || file.name
      };

      saveLocalExeFile(fileInfo);
      emit('addLocalFile', fileInfo);
      event.target.value = '';
    }
  };

  const handleRemoveLocalFile = () => {
    removeLocalExeFile();
    emit('removeLocalFile');
  };

  // 拖拽处理函数
  const handleDragOver = (event) => {
    event.preventDefault();
    isDragOver.value = true;
  };

  const handleDragLeave = (event) => {
    event.preventDefault();
    isDragOver.value = false;
  };

  const handleDrop = (event) => {
    event.preventDefault();
    isDragOver.value = false;

    const files = event.dataTransfer.files;
    if (files.length > 0) {
      const file = files[0];
      if (file.name.toLowerCase().endsWith('.exe')) {
        const fileInfo = {
          name: file.name,
          path: file.path || file.name
        };

        saveLocalExeFile(fileInfo);
        emit('addLocalFile', fileInfo);
      } else {
        console.warn('[AccountSwitch] 只支持 .exe 文件');
      }
    }
  };

  return {
    fileInputRef,
    accountOptions,
    localExeFile,
    isDragOver,
    handleSelectAccount,
    handleUpdateShow,
    handleAddLocalFile,
    handleFileSelect,
    handleRemoveLocalFile,
    handleDragOver,
    handleDragLeave,
    handleDrop,
  };
}
