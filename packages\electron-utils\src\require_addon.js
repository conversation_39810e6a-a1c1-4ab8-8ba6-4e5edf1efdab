const path = require('path');
const log = require('./log');
global.REQUIRED_ADDON = {};
const node_bit = process.arch.includes('64') ? 64 : 32

module.exports = function requireAddon (addon) {
  try {
    if (!global.REQUIRED_ADDON[addon]) {
      const filename = (node_bit === 64 ? addon : `${addon}_32`) + '.node';
      const filePath = path.join(__dirname, './addon', filename);
      REQUIRED_ADDON[addon] = require(filePath);
    }
  } catch (e) {
    log.error('[requireAddon] error', e);
  }
  return REQUIRED_ADDON[addon];
};
