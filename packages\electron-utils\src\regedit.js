const path = require('path')
const INSTALL_PATH_REGEDIT = ['HKLM', 'SOFTWARE\\WOW6432Node\\Qingfeng\\HeyboxApp']
const MACHINE_ID_REGEDIT = ['HKLM', 'SOFTWARE\\Microsoft\\SQMClient']
const HEYBOXAPP_REGEDIT = ['HKCU', 'Software\\Microsoft\\Windows\\CurrentVersion\\Run']
const HEYBOXAPP_INSTALL_PATH = ['HKLM', 'SOFTWARE\\WOW6432Node\\Qingfeng\\HeyboxApp']
const HEYBOXAPP_VERSION = ['HKCU', 'Software\\Qingfeng\\HeyboxApp\\version']
const HEYBOX_VERSION_JSON_PATH = path.join(process.env.SystemDrive, 'ProgramData', 'Qingfeng', 'HeyboxApp', 'version.json');
const requireAddon = require('./require_addon')
const log = require('./log')
const fs = require('fs')
const { regGetStringValue, regSetStringValue, regDeleteKeyValue } = requireAddon('reg_helper')
const { addLogonTriggerTaskIntoScheduler, removeTaskFromScheduler, queryTaskFromScheduler, checkAdmin, getSystemVersion } = requireAddon('get_endpoint_info')

/**
 * 读取 JSON 文件内容
 * @param {string} filePath - JSON 文件路径
 * @returns {Promise<Object>} - 解析后的 JSON 对象
 */
async function readJsonFile(filePath) {
  try {
    const data = await fs.promises.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    return null
  }
}

/**
* 写入 JSON 文件（自动创建目录）
* @param {string} filePath - JSON 文件路径
* @param {Object} content - 要写入的内容
*/
async function writeJsonFile(filePath, content) {
  // 确保目录存在
  const dir = path.dirname(filePath);
  await fs.promises.mkdir(dir, { recursive: true });
  
  // 格式化并写入文件
  const jsonString = JSON.stringify(content, null, 2);
  await fs.promises.writeFile(filePath, jsonString, 'utf8');
}

function getKey(path, key) {
  let d = regGetStringValue(path[0], path[1], key)
  if (d.success == 1) {
    return d.value
  } else {
    log.info('[regedit error: get] ', path, key, d)
    return false
  }
}

function deleteKey(path, key) {
  let d = regDeleteKeyValue(path[0], path[1], key)
  if (d.success == 1) {
    return true
  } else {
    log.info('[regedit error: del] ', path, key, d)
    return false
  }
}

async function addKey(path, key, value) {
  let d = regSetStringValue(path[0], path[1], key, value)
  if (d.success == 1) {
    return true
  } else {
    log.info('[regedit error: add] ', path, key, d)
    return false
  }
}

async function editAutoStart(v) {
  try {
    if (v) {
      let exePath = await getKey(INSTALL_PATH_REGEDIT, 'InstallPath')
      let isAdmin = await checkIsAdmin()
      return new Promise((resolve) => {
        addLogonTriggerTaskIntoScheduler((result) => {
          log.info('[addLogonTriggerTaskIntoScheduler]', result)
          resolve(result)
        }, { 
          taskName: "HeyboxApp Startup", 
          exePath: exePath + '\\HeyboxApp.exe', 
          exeArgs: "-auto", 
          highestPrivilege: isAdmin && !!main_config.admin_mode_launch 
        })
      })
    } else {
      return new Promise((resolve) => {
        removeTaskFromScheduler((result) => {
          log.info('[removeTaskFromScheduler]', result)
          resolve(result)
        }, { taskName: "HeyboxApp Startup" })
      })
    }
  } catch (e) {
    log.info(e)
  }
}

async function checkAutoStart() {
  let v = await getKey(HEYBOXAPP_REGEDIT, 'HeyboxApp')
  let [isExist, isAdmin] = await new Promise((resolve) => {
    queryTaskFromScheduler((isExist, isAdmin) => {
      log.info('[queryTaskFromScheduler]', isExist, isAdmin)
      resolve([isExist, isAdmin])
    }, { taskName: "HeyboxApp Startup" })
  })
  // 如果存在老版本的注册表自启动，则删除对应注册表并重新设置自启动任务
  if (v) {
    deleteKey(HEYBOXAPP_REGEDIT, 'HeyboxApp')
    editAutoStart(true)
    return true
  }
  // 设置自启动任务时，可能由于客户端权限不足导致无法设置管理员启动，每次启动时检查是否需要重新设置更新配置
  if (isExist && isAdmin != main_config.admin_mode_launch) {
    editAutoStart(true)
  }
  return isExist
}

async function getClientVersion() {
  const version = await readJsonFile(HEYBOX_VERSION_JSON_PATH)
  if (version?.current) {
    return version.current
  }
  let v = await getKey(HEYBOXAPP_VERSION, 'current')
  return v
}

async function getMachineId() {
  let v = await regGetStringValue(...MACHINE_ID_REGEDIT, 'MachineId', '64key')
  return v.value.replace(/[{}]/g, '')
}

async function setNextLaunchVersion (v) {
  try {
    // 并行执行json读写和注册表写入
    await Promise.all([
      (async () => {
        const version = (await readJsonFile(HEYBOX_VERSION_JSON_PATH)) || {}
        version.nextLaunchVersion = v
        await writeJsonFile(HEYBOX_VERSION_JSON_PATH, version)
      })(),
      addKey(HEYBOXAPP_VERSION, 'nextLaunchVersion', v)
    ])
  } catch (e) {
    console.log(e)
  }
}

async function getAsarVersion () {
  const version = await readJsonFile(HEYBOX_VERSION_JSON_PATH)
  if (version?.asarVersion) {
    return version.asarVersion
  }
  let v = await getKey(HEYBOXAPP_VERSION, 'asarVersion')
  return v
}

function getInstallPath () {
  return getKey(INSTALL_PATH_REGEDIT, 'InstallPath')
}

async function setAdminMode (v) {
  try {
    // 设置管理员权限时，如果设置了开机自启动，则重新设置开机自启动任务, 更新自启动的管理员权限
    if (main_config?.auto_launch) {
      editAutoStart(true)
    }
    return addKey(HEYBOXAPP_VERSION, 'RunAsAdmin', v)
  } catch (e) {
    log.info('[setAdminMode error]', e)
  }
}

async function checkIsAdmin () {
  return new Promise((resolve) => {
    checkAdmin((isAdmin) => {
      log.info('[checkIsAdmin]', isAdmin)
      resolve(isAdmin)
    })
  })
}
async function getNextLaunchVersion () {
  const version = readJsonFile(HEYBOX_VERSION_JSON_PATH)
  if (version?.nextLaunchVersion) {
    return version.nextLaunchVersion
  }
  let v = await getKey(HEYBOXAPP_VERSION, 'nextLaunchVersion')
  return v
}

async function getExeInstallPath () {
  let v = await getKey(HEYBOXAPP_INSTALL_PATH, 'InstallPath')
  return v
}

exports.readJsonFile = readJsonFile
exports.writeJsonFile = writeJsonFile
exports.editAutoStart = editAutoStart
exports.checkAutoStart = checkAutoStart
exports.getClientVersion = getClientVersion
exports.getMachineId = getMachineId
exports.getKey = getKey
exports.setNextLaunchVersion = setNextLaunchVersion
exports.getAsarVersion = getAsarVersion
exports.getInstallPath = getInstallPath
exports.setAdminMode = setAdminMode
exports.getNextLaunchVersion = getNextLaunchVersion
exports.getExeInstallPath = getExeInstallPath