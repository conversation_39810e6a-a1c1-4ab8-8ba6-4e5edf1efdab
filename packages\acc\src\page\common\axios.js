const axios = require('axios')
const qs = require('qs')
const m = require('js-md5');
const { G } = require('./encrypt/index.js')

// import store from '@/store'
// import { getSmDeviceId, getStorage, setStorage } from './utils'
// import { logout } from '_api/login'
// import EventBus from '@/common/EventBus'

const Axios = axios.create()
Axios.create = axios.create
Axios.all = axios.all

const auto_reload_ts_key = 'hc_auto_reload_ts'
let timeoutToastLock = false

// Axios连续失败次数
let axiosErrorTime = 0

// Axios连续失败次数重置 
let axiosErrorTimeout = null

// Axios连续失败提示更改DNS设置
let isAxiosErrorAlert = false


let logoutPromise = null

Axios.interceptors.request.use(
  async (config) => {

    let p = {
      ...config.params
    }

    // 时间戳
    let t = ~~(+G.w() / 1000);

    // nonce生成
    let n = m(
      t + Math.random(new Date().getTime()).toString(),
    ).toLocaleUpperCase();

    p.hkey = G.g(new URL(config.url).pathname, t, n)

    p.nonce = n
    p._time = t
    config.params = p

    if (config.method === `POST`) {
      if (!config.upload) {
        config.data = {
          ...config.data,
        }
        if (!config.headers['Content-Type'].includes('application/json')) {
          config.data = qs.stringify(config.data, { indices: false });
        }
      }
    }
    config.withCredentials = true
    return config;
  },
  (error) => {
    return Promise.reject(error.message);
  }
)


Axios.interceptors.response.use(
  (res) => {
    axiosErrorTime = 0
    let status_key = res.data.status
    if (status_key == 'relogin' && !res.config.params['_norelogin']) {
    } else if (status_key === 'redirect_room') {
      // EventBus.$dynamic('RoomRedirectDialog', res.data.result)
    } else if (status_key != 'ok' && res.data.msg) {
      // if (res.data.msg.indexOf('数据上报失败') == -1 && res.data.msg != '请先验证' &&  res.data.msg != 'no_toast' && !res.config.params['_notip']) {
      //   Vue.prototype.$toast.error(res.data.msg)
      // }
    }
    return res
  },
  (error) => {
    if (error.code === 'ECONNABORTED' && error.message.indexOf('timeout') !== -1) {
      // 处理超时错误
      if (!timeoutToastLock) {
        // Vue.prototype.$toast.error('请求超时，请刷新重试')
        timeoutToastLock = true
        setTimeout(() => {
          timeoutToastLock = false
        }, 2000)
      }
    } else {
      // 处理其他错误
      console.error(error);
    }
  
    // 如果是403，说明是后端检验拦截，toast msg信息即可
    let isForbidden = error.response && error.response.status === 403 
    if (isForbidden) {
      let msg = error.response.data?.msg
      console.error(msg)
      // msg && Vue.prototype.$toast.error(msg)
    }
  
    return Promise.reject(error);
  }
)

module.exports = Axios