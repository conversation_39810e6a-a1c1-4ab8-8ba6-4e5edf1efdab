<template> 
  <div tabindex="-1" ref="elImageViewerWrapper" class="cpt-chat-image-viewer__wrapper" :style="{ 'z-index': viewerZIndex }">
    <div class="el-image-viewer__mask" @click.stop="handleMaskClick"></div>
    <!-- CLOSE -->
    <span class="el-image-viewer__btn el-image-viewer__close" @click.stop="close">
      <i class="iconfont icon-common-close-line"></i>
    </span>
    <!-- ARROW -->
    <template v-if="!isSingle">
      <span
        class="el-image-viewer__btn el-image-viewer__prev"
        :class="{ 'is-disabled': !infinite && isFirst }"
        @click="prev">
        <i class="iconfont icon-common-arrow-left-line"/>
      </span>
      <span
        class="el-image-viewer__btn el-image-viewer__next"
        :class="{ 'is-disabled': !infinite && isLast }"
        @click="next">
        <i class="iconfont icon-common-arrow-left-line right"/>
      </span>
    </template>
    <!-- ACTIONS -->
    <!-- <div class="el-image-viewer__btn el-image-viewer__actions">
      <div class="el-image-viewer__actions__inner">
        <i class="el-icon-zoom-out pointer" @click="handleActions('zoomOut')"></i>
        <i class="el-icon-zoom-in pointer" @click="handleActions('zoomIn')"></i>
        <i class="el-image-viewer__actions__divider"></i>
        <i :class="[mode.icon, 'pointer']" @click="toggleMode"></i>
        <i class="el-image-viewer__actions__divider"></i>
        <i v-if="!isLongImg" class="el-icon-refresh-left pointer" @click="handleActions('anticlocelise')"></i>
        <i v-if="!isLongImg" class="el-icon-refresh-right pointer" @click="handleActions('clocelise')"></i>
        <i class="el-icon-download pointer" @click="handleActions('download')"></i>
        <i class="iconfont icon-common-copy-line pointer" @click="handleActions('copy')"></i>
      </div>
    </div> -->
    <!-- CANVAS -->
    <div class="el-image-viewer__canvas" ref="canvas">
      <template v-if="isLongImg">
        <div 
        class="scroll-container" 
        ref="scroll" 
        :style="{
          width: imageWidth + 'px',
        }">
          <img
            ref="img"
            class="el-image-viewer__img disabled-prevent-contextmenu long-image"
            :src="currentImg"
            :style="imgStyle"
            referrerpolicy='no-referrer'
            @load="handleImgLoad"
            @error="handleImgError"
            @mousedown="handleMouseDown"
          >
        </div>
      </template>
      <template v-else>
        <img
          ref="img"
          class="el-image-viewer__img disabled-prevent-contextmenu"
          :src="currentImg"
          :style="imgStyle"
          referrerpolicy='no-referrer'
          @load="handleImgLoad"
          @error="handleImgError"
          @mousedown="handleMouseDown"
        >
      </template>
    </div>

    <div 
      v-if="isLongImg" 
      class="el-image-viewer__overview" 
      ref="overview"
      :style="{
        right: overviewRight + 'px',
        transition: transform.enableTransition ? 'all 0.15s ease 0s' : '',
        opacity: initOffsetY ? '1' : '0'
      }"
    >
      <div class="overview-container">
        <img
          ref="overviewImg"
          class="el-image-viewer__img disabled-prevent-contextmenu"
          :src="currentImg"
          referrerpolicy='no-referrer'
        >
        <div class="mask"></div>
        <div class="viewport-slider">
          <div 
            class="slider"
            :style="sliderStyle"
            @mousedown="handleSliderMouseDown">
            <img
              class="el-image-viewer__img disabled-prevent-contextmenu slider-img"
              :src="currentImg"
              :style="{
                top: `-${sliderTransform.offsetY + 2}px`,
                left: `-${sliderTransform.offsetX + 2}px`
              }"
              referrerpolicy='no-referrer'
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { copyToClipboard, throttle, sleep } from "@heybox-app-web-shared/utils"
// import { downloadImage } from "_lib/utils/utilsFile"
import { useToast } from 'vue-toastification'
const toast = useToast()

const on = (function() {
  if (document.addEventListener) {
    return function(element, event, handler) {
      if (element && event && handler) {
        element.addEventListener(event, handler, false);
      }
    };
  } else {
    return function(element, event, handler) {
      if (element && event && handler) {
        element.attachEvent('on' + event, handler);
      }
    };
  }
})();

const off = (function() {
  if (document.removeEventListener) {
    return function(element, event, handler) {
      if (element && event) {
        element.removeEventListener(event, handler, false);
      }
    };
  } else {
    return function(element, event, handler) {
      if (element && event) {
        element.detachEvent('on' + event, handler);
      }
    };
  }
})();

const isFirefox = function() {
  return !!window.navigator.userAgent.match(/firefox/i);
};

const Mode = {
  CONTAIN: {
    name: 'contain',
    icon: 'el-icon-full-screen'
  },
  ORIGINAL: {
    name: 'original',
    icon: 'el-icon-c-scale-to-original'
  }
};

const ZOOM_RATE = 0.2;
const HEADER_HEIGHT = 30;

const mousewheelEventName = isFirefox() ? 'DOMMouseScroll' : 'mousewheel';

function rafThrottle(fn) {
  let locked = false;
  return function(...args) {
    if (locked) return;
    locked = true;
    window.requestAnimationFrame(_ => {
      fn.apply(this, args);
      locked = false;
    });
  };
}

// Props
const props = defineProps({
  urlList: {
    type: Array,
    default: () => []
  },
  zIndex: {
    type: Number,
    default: 2000
  },
  onSwitch: {
    type: Function,
    default: () => {}
  },
  onClose: {
    type: Function,
    default: () => {}
  },
  initialIndex: {
    type: Number,
    default: 0
  },
  maskClosable: {
    type: Boolean,
    default: true
  },
})

// Emits
const emit = defineEmits(['close'])

// Refs
const elImageViewerWrapper = ref(null)
const canvas = ref(null)
const img = ref(null)
const scroll = ref(null)
const overview = ref(null)
const overviewImg = ref(null)

// Reactive data
const index = ref(props.initialIndex)
const infinite = ref(true)
const loading = ref(false)
const isLongImg = ref(true)
const mode = ref(Mode.CONTAIN)
const initOffsetX = ref(0)
const initOffsetY = ref(0)
const imageWidth = ref(600)
const imageHeight = ref(0)
const overviewRight = ref(18)
const transform = ref({
  scale: 1,
  deg: 0,
  offsetX: 0,
  offsetY: 0,
  enableTransition: false,
  origin: {
    x: 'center',
    y: 'center',
  },
})
const sliderTransform = ref({
  offsetX: 0,
  offsetY: 0,
  height: 50,
  width: 24,
})
const lastXY = ref({
  x: 0,
  y: 0,
})
const currentXY = ref({
  x: 0,
  y: 0,
})
const scaleStart = ref({
  x: 0,
  y: 0,
  distance: 0
})

// Event handlers
let _keyDownHandler = null
let _mouseWheelHandler = null
let _dragHandler = null
let _dragEndHandler = null
let _dragSliderHandler = null
let _dragEndSliderHandler = null
let showContextMenu = ref(false)

// Computed
const realUrlList = computed(() => {
  return props.urlList.map(item => item.split('?')[0])
})

const isSingle = computed(() => {
  return realUrlList.value.length <= 1
})

const isFirst = computed(() => {
  return index.value === 0
})

const isLast = computed(() => {
  return index.value === realUrlList.value.length - 1
})

const currentImg = computed(() => {
  return realUrlList.value[index.value]
})

const imgStyle = computed(() => {
  const { scale, deg, offsetX, offsetY, enableTransition } = transform.value
  
  const style = {
    transform: `scale(${scale}) rotate(${deg}deg) translateZ(0)`,
    display: loading.value ? 'none' : 'block',
    transformOrigin: `${transform.value.origin.x} ${transform.value.origin.y}`,
    transition: enableTransition ? 'all 0.15s ease 0s' : '',
    'left': `${initOffsetX.value + offsetX}px`,
    'top': `${initOffsetY.value + offsetY}px`,
  }
  if (!isLongImg.value && mode.value.name === Mode.CONTAIN.name) {
    style.maxWidth = style.maxHeight = '100%'
  }
  return style
})

const sliderStyle = computed(() => {
  const { offsetX, offsetY, height, width } = sliderTransform.value
  const style = {
    left: `${offsetX}px`,
    top: `${offsetY}px`,
    height: `${height}px`,
    width: `${width}px`,
  }
  return style
})

// TODO 后续添加z-index的管理方法
const zIndex = ref(2000)
const viewerZIndex = computed(() => {
  return props.zIndex ? props.zIndex : zIndex.value++
})

// Methods
const close = () => {
  deviceSupportUninstall()
  emit('close')
}

const deviceSupportInstall = () => {
  _keyDownHandler = e => {
    e.stopPropagation()
    const keyCode = e.keyCode
    switch (keyCode) {
      // ESC
      case 27:
        close()
        break
      // SPACE
      case 32:
        toggleMode()
        break
      // LEFT_ARROW
      case 37:
        prev()
        break
      // UP_ARROW
      case 38:
        handleActions('zoomIn')
        break
      // RIGHT_ARROW
      case 39:
        next()
        break
      // DOWN_ARROW
      case 40:
        handleActions('zoomOut')
        break
    }
  }
  
  _mouseWheelHandler = rafThrottle(e => {
    if (!e.deltaY) {
      return
    }
    let origin = {
      x: e.offsetX + 'px',
      y: e.offsetY + 'px',
    }

    let zoomRate = ZOOM_RATE
    if (e.deltaY >= 0 && transform.value.scale - zoomRate < 0.1) {
      return
    }
    
    currentXY.value = {
      x: e.offsetX,
      y: e.offsetY,
    }
    
    if (e.deltaY < 0) {
      handleActions('zoomIn', {
        enableTransition: true,
        origin,
      })
    } else {
      handleActions('zoomOut', {
        enableTransition: true,
        origin,
      })
    }
  })
  
  on(canvas.value, 'keydown', _keyDownHandler)
  if(isLongImg.value) {
    on(scroll.value, 'scroll', handleImgScroll)
    on(window, 'resize', handleWindowResize)
  }
  else on(canvas.value, mousewheelEventName, _mouseWheelHandler)
}

const deviceSupportUninstall = () => {
  off(canvas.value, 'keydown', _keyDownHandler)
  off(scroll.value, 'scroll', handleImgScroll)
  off(window, 'resize', handleWindowResize)
  off(canvas.value, mousewheelEventName, _mouseWheelHandler)
  off(document, 'mousemove', _dragHandler)
  off(document, 'mousemove', _dragSliderHandler)
  off(document, 'mouseup', _dragEndHandler)
  off(document, 'mouseup', _dragEndSliderHandler)
  
  _keyDownHandler = null
  _mouseWheelHandler = null
  _dragHandler = null
  _dragEndHandler = null
  _dragSliderHandler = null
  _dragEndSliderHandler = null
}

const handleWindowResize = throttle(function() {
  const containerHeight = document.documentElement.clientHeight
  const containerWidth = document.documentElement.clientWidth
  initOffsetY.value = (imageHeight.value - containerHeight) / 2
  initOffsetX.value = (imageWidth.value - containerWidth) / 2
  if(initOffsetX.value < 0) initOffsetX.value = 0
  if(imageWidth.value > containerWidth) sliderTransform.value.width = containerWidth / imageWidth.value * overviewImg.value.width
  else sliderTransform.value.width = overviewImg.value.width
  sliderTransform.value.height = containerHeight / imageHeight.value * overviewImg.value.height
  overviewRight.value = (containerWidth - imageWidth.value) / 2 - 30 // 减去自身宽度及与图片的距离
  if(overviewRight.value < 0) overviewRight.value = 18 // 防止概览图溢出屏幕
}, 100)

const handleImgScroll = () => {
  const containerHeight = document.documentElement.clientHeight
  const containerWidth = document.documentElement.clientWidth
  if(scroll.value.scrollTop > imageHeight.value - containerHeight + HEADER_HEIGHT) {
    scroll.value.scrollTop = imageHeight.value - containerHeight + HEADER_HEIGHT
  }
  if(scroll.value.scrollLeft > imageWidth.value - containerWidth) {
    scroll.value.scrollLeft = imageWidth.value - containerWidth
  }
  sliderTransform.value.offsetY = scroll.value.scrollTop / imageHeight.value * overviewImg.value.height
  sliderTransform.value.offsetX = scroll.value.scrollLeft / imageWidth.value * overviewImg.value.width
}

const handleImgLoad = (e) => { 
  if(isLongImg.value) {
    transform.value.scale = parseFloat((600 / img.value.width).toFixed(3))
    let containerHeight = document.documentElement.clientHeight - HEADER_HEIGHT
    let newHeight = img.value.height * transform.value.scale
    sliderTransform.value.height = containerHeight / newHeight * overviewImg.value.height
    initOffsetY.value = -(containerHeight - newHeight) / 2
  }
  loading.value = false
}

const handleImgError = (e) => {
  loading.value = false
  e.target.alt = '加载失败'
}

const handleMouseDown = (e) => {
  if (loading.value || e.button !== 0) return
  const { offsetX, offsetY } = transform.value
  let startX = e.pageX
  let startY = e.pageY
  _dragHandler = ev => {
    if(!isLongImg.value) {
      transform.value.offsetX = offsetX + ev.pageX - startX
      transform.value.offsetY = offsetY + ev.pageY - startY
    }
    else {
      scroll.value.scrollTop = scroll.value.scrollTop - ev.pageY + startY
      scroll.value.scrollLeft = scroll.value.scrollLeft - ev.pageX + startX
      startY = ev.pageY
    }
    transform.value.enableTransition = false
  }
  _dragEndHandler = ev => {
    off(document, 'mousemove', _dragHandler)
  }

  on(document, 'mousemove', _dragHandler)
  on(document, 'mouseup', _dragEndHandler)
  // showContextMenu.value && window.$contextmenu.destroy()
  e.preventDefault()
}

const handleSliderMouseDown = (e) => {
  if (loading.value || e.button !== 0) return
  const { offsetX, offsetY, height, width } = sliderTransform.value
  const startX = e.pageX
  const startY = e.pageY
  const overviewHeight = overviewImg.value.height
  const overviewWidth = overviewImg.value.width
  const imgHeight = img.value.height * transform.value.scale
  const imgWidth = img.value.width * transform.value.scale
  _dragSliderHandler = ev => {
    sliderTransform.value.offsetX = offsetX + ev.pageX - startX
    sliderTransform.value.offsetY = offsetY + ev.pageY - startY
    if(sliderTransform.value.offsetY < 0) {
      sliderTransform.value.offsetY = 0
    }
    if(sliderTransform.value.offsetY > overviewHeight - height) {
      sliderTransform.value.offsetY = overviewHeight - height
    }
    if(sliderTransform.value.offsetX < 0) {
      sliderTransform.value.offsetX = 0
    }
    if(sliderTransform.value.offsetX > overviewWidth - width) {
      sliderTransform.value.offsetX = overviewWidth - width
    }
    scroll.value.scrollTop = sliderTransform.value.offsetY / overviewHeight * imgHeight
    scroll.value.scrollLeft = sliderTransform.value.offsetX / overviewWidth * imgWidth
  }
  _dragEndSliderHandler = ev => {
    off(document, 'mousemove', _dragSliderHandler)
  }
  on(document, 'mousemove', _dragSliderHandler)
  on(document, 'mouseup', _dragEndSliderHandler)
  // showContextMenu.value && window.$contextmenu.destroy()
  e.preventDefault()
}

const handleMaskClick = () => {
  if (props.maskClosable) {
    close()
  }
}

const reset = () => {
  initOffsetY.value = 0
  imageWidth.value = 600
  transform.value = {
    scale: 1,
    deg: 0,
    offsetX: 0,
    offsetY: 0,
    enableTransition: false,
    origin: {
      x: 'center',
      y: 'center',
    }
  }
}

const toggleMode = () => {
  if (loading.value) return

  const modeNames = Object.keys(Mode)
  const modeValues = Object.values(Mode)
  const currentIndex = modeValues.indexOf(mode.value)
  const nextIndex = (currentIndex + 1) % modeNames.length
  mode.value = Mode[modeNames[nextIndex]]
  reset()
}

const prev = () => {
  if (isFirst.value && !infinite.value) return
  const len = realUrlList.value.length
  index.value = (index.value - 1 + len) % len
}

const next = () => {
  if (isLast.value && !infinite.value) return
  const len = realUrlList.value.length
  index.value = (index.value + 1) % len
}

const handleActions = (action, options = {}) => {
  if (loading.value) return
  const { rotateDeg, enableTransition, origin, zoomRate } = {
    zoomRate: ZOOM_RATE,
    rotateDeg: 90,
    enableTransition: true,
    origin: {
      x: 'center',
      y: 'center',
    },
    ...options
  }
  
  const correctOriginAndOffset = () => {
    let {scale, offsetX, offsetY} = transform.value
    let {x: lastX, y: lastY} = lastXY.value
    const offsetLeft = (scale - 1) * (currentXY.value.x - lastX) + offsetX
    const offsetTop = (scale - 1) * (currentXY.value.y - lastY) + offsetY
    lastXY.value = {
      x: currentXY.value.x,
      y: currentXY.value.y,
    }
    transform.value.offsetX = offsetLeft
    transform.value.offsetY = offsetTop
  }
  
  switch (action) {
    case 'zoomOut':
      correctOriginAndOffset()
      if(transform.value.scale - zoomRate <= 0) break
      
      transform.value.scale = parseFloat((transform.value.scale - zoomRate).toFixed(3))
      transform.value.origin.x = origin.x
      transform.value.origin.y = origin.y
      break
    case 'zoomIn':
      correctOriginAndOffset()
      
      transform.value.scale = parseFloat((transform.value.scale + zoomRate).toFixed(3))
      transform.value.origin.x = origin.x
      transform.value.origin.y = origin.y
      break
    case 'clocelise':
      transform.value.deg += rotateDeg
      transform.value.origin.x = 'center'
      transform.value.origin.y = 'center'
      break
    case 'anticlocelise':
      transform.value.deg -= rotateDeg
      transform.value.origin.x = 'center'
      transform.value.origin.y = 'center'
      break
    // case 'download':
    //   const imageNameFromUrl = (url) => {
    //     let u = url.split('?')[0] // strip query
    //     for (let i = u.length - 1; i >= 0; i--) {
    //       if (u[i] === '/') {
    //         return u.slice(i + 1)
    //       }
    //     }
    //     return ''
    //   }

    //   window.$toast.success('图片下载中')
    //   const imageName = imageNameFromUrl(currentImg.value)
    //   downloadImage(currentImg.value, imageName)
    //   break
    case 'copy':
      try {
        copyToClipboard(currentImg.value)
        toast.success('复制图片链接成功')
      } catch (error) {
        toast.error('复制图片链接失败')
      }
      break
  }
  transform.value.enableTransition = enableTransition
}

const escEvent = (e) => {
  if (e.keyCode === 27) {
    // 这里的settimeout是为了不和私聊面板的esc事件冲突
    setTimeout(() => {
      close()
    }, 0)
  }
}

// const handleContextMenu = (event) => {
//   if (process.env.IS_CLIENT) {
//     event.preventDefault()
//     let optionList = [
//       {
//         component: 'menu-button',
//         data: {
//           label: '图片存储为',
//         },
//         onClick: () => {
//           showContextMenu.value = false
//           handleActions('download')
//         },
//       },
//       {
//         component: 'menu-button',
//         data: {
//           label: '复制图片',
//         },
//         onClick: async () => {
//           showContextMenu.value = false
//           window.$contextmenu.destroy()
//           copyImage(currentImg.value).then(() => {
//             window.$toast.success('复制成功')
//           }).catch(() => {
//             window.$toast.error('复制失败')
//           })
//         },
//       },
//       {
//         component: 'menu-button',
//         data: {
//           label: '复制图片地址',
//         },
//         onClick: () => {
//           showContextMenu.value = false
//           handleActions('copy')
//         },
//       }
//     ]
//     window.$contextmenu({
//       groups: [
//         {
//           items: optionList
//         },
//       ],
//       event,
//     })
//     showContextMenu.value = true
//   }
// }

const judgeImgType = (url) => {
  let img = new Image()
  img.src = url
  img.onload = () => {
    isLongImg.value = img.height / img.width >= 3
  }
}

// Lifecycle hooks
onMounted(() => {
  deviceSupportInstall()
  document.body.appendChild(elImageViewerWrapper.value)
  on(document, 'keydown', escEvent)
  elImageViewerWrapper.value.focus()
  judgeImgType(currentImg.value)
})

onBeforeUnmount(() => {
  deviceSupportUninstall()
  off(document, 'keydown', escEvent)
  showContextMenu.value && window.$contextmenu.destroy()
})

// Watchers
watch(index, (val) => {
  reset()
  props.onSwitch(val)
})

watch(currentImg, (val) => {
  judgeImgType(val)
  nextTick(() => {
    if (!img.value.complete) {
      loading.value = true
    }
  })
})

watch(isLongImg, async (val) => {
  reset()
  nextTick(() => {
    if(val) {
      while(!scroll.value) sleep(100)
      on(scroll.value, 'scroll', handleImgScroll)
      on(window, 'resize', handleWindowResize)
      off(canvas.value, mousewheelEventName, _mouseWheelHandler)
    }
    else {
      on(canvas.value, mousewheelEventName, _mouseWheelHandler)
      off(scroll.value, 'scroll', handleImgScroll)
      off(window, 'resize', handleWindowResize)
    }
  })
})

watch(() => transform.value.scale, (val) => {
  if(!isLongImg.value) return
  const containerHeight = document.documentElement.clientHeight
  const containerWidth = document.documentElement.clientWidth
  const newHeight = img.value.height * val
  const newWidth = img.value.width * val
  if(img.value) {
    initOffsetY.value = -(containerHeight - newHeight) / 2
    initOffsetX.value = (newWidth - containerWidth) / 2
    if(initOffsetX.value < 0) initOffsetX.value = 0
    imageWidth.value = newWidth
    imageHeight.value = newHeight
    overviewRight.value = (containerWidth - imageWidth.value) / 2 - 30 // 减去自身宽度及与图片的距离
    if(overviewRight.value < 0) overviewRight.value = 18 // 防止概览图溢出屏幕
    if(scroll.value) scroll.value.height = imageHeight.value
  }
  if(overviewImg.value) {
    sliderTransform.value.height = containerHeight / newHeight * overviewImg.value.height
    if(newWidth > containerWidth) sliderTransform.value.width = containerWidth / newWidth * overviewImg.value.width
    else sliderTransform.value.width = overviewImg.value.width
  }
  if(scroll.value && scroll.value.scrollTop > imageHeight.value - containerHeight + HEADER_HEIGHT) {
    scroll.value.scrollTop = imageHeight.value - containerHeight + HEADER_HEIGHT
  }
}, { deep: true })
</script>

<style lang='scss'>
.cpt-chat-image-viewer__wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  .el-image-viewer__btn {
    position: absolute;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    opacity: .8;
    cursor: pointer;
    box-sizing: border-box;
    user-select: none;
  }

  .icon-common-arrow-left-line.right {
    transform: rotate(180deg);
  }

  .el-image-viewer__close {
    top: 40px;
    right: 40px;
    width: 40px;
    height: 40px;
    font-size: 24px;
    color: #fff;
    background-color: #606266;
  }
  .el-image-viewer__overview {
    width: 24px;
    height: auto;
    position: fixed;
    right: 18px;
    bottom: 18px;
    
    .el-image-viewer__img {
      width: 24px;
      border-radius: 3px;
      border: 1px #000;
      &.slider-img {
        position: relative;
        top: -2px;
        left: -2px;
      }
    }
    .mask {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: $general-color-bg-4;
      border-radius: 3px;
    }
    .viewport-slider {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      .slider {
        width: 100%;
        height: 50px;
        position: absolute;
        top: 0;
        width: 100%;
        height: 50px;
        border-radius: 3px;
        border: 2px solid #7dd95e;
        overflow: hidden;
      }
      
    }
  }
  .el-image-viewer__canvas {
    width: auto;
    height: 100%;
    margin-top: 30px;
    overflow: hidden;
    .scroll-container {
      position: relative;
      width: auto;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: auto;
      // padding-top: 30px;
    }
    .el-image-viewer__img {
      position: relative;
      pointer-events: auto;
      margin: auto;
    }
    .long-img {
      object-fit: none;
    }
  }
  .el-image-viewer__actions {
    left: 50%;
    bottom: 30px;
    transform: translateX(-50%);
    width: 282px;
    height: 44px;
    padding: 0 23px;
    background-color: #606266;
    border-color: #fff;
    border-radius: 22px;
    .el-image-viewer__actions__inner {
      width: 100%;
      height: 100%;
      text-align: justify;
      cursor: default;
      font-size: 23px;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: space-around;
    }
  }
  .el-image-viewer__prev {
    top: 50%;
    transform: translateY(-50%);
    width: 44px;
    height: 44px;
    font-size: 24px;
    color: #fff;
    background-color: #606266;
    border-color: #fff;
    left: 40px;
  }
  .el-image-viewer__next {
    top: 50%;
    transform: translateY(-50%);
    width: 44px;
    height: 44px;
    font-size: 24px;
    color: #fff;
    background-color: #606266;
    border-color: #fff;
    right: 40px;
    text-indent: 2px;
  }
  .el-image-viewer__mask {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    color: rgba(0, 0, 0, 0.5);
  }

  &:focus-visible {
    outline: unset;
  }
}
</style>
