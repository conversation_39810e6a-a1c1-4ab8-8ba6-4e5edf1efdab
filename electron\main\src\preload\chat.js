const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');
const store = require('@heybox/electron-utils/store')
const { version } = require('../../package.json');

contextBridge.exposeInMainWorld('electronAPI', {
  // 发送消息到主进程
  send: (channel, ...args) => {
    ipcRenderer.send(channel, ...args);
  },

  // 从主进程接收消息
  receive: (channel, callback) => {
    // 删除任何现有监听器
    ipcRenderer.removeAllListeners(channel);

    // 添加新的监听器
    ipcRenderer.on(channel, (event, ...args) => callback(...args));
  },

  // 调用主进程方法并等待结果
  invoke: async (channel, ...args) => {
    return await ipcRenderer.invoke(channel, ...args);
  },

  sentryReport: (message, stack) => ipcRenderer.send('sentryReport', message, stack),
  setCacheData: (k, v) => {
    ipcRenderer.send('levelstore:set', k, JSON.parse(v))
  },
  delCacheData: (k) => ipcRenderer.invoke('levelstore:del', k),
  getCacheData: (k) => ipcRenderer.invoke('levelstore:get', k),
  getStoreData: (k, base = 'chat_config') => {
    return store.get(`${base}${k ? `.${k}` : ''}`)
  },
  setStoreData: (k, v, base = 'chat_config') => {
    store.set(`${base}${k ? `.${k}` : ''}`, v)
  },
  delStoreData: (k, base = 'chat_config') => {
    try {
      if(!k) throw new Error('k is required')
      return store.del(`${base}.${k}`)
    } catch (error) {
      console.error('delStoreData error', error)
    }
  },
  onStoreChange: (k, cb, base = 'chat_config') => {
    return store.original_store.onDidChange(`${base}${k ? `.${k}` : ''}`, (v) => {
      cb(v)
    })
  },
  getDeviceId: () => ipcRenderer.invoke('getDeviceId'),
  base: 'chat_config' // 默认的store存储位置
});

contextBridge.exposeInMainWorld('versionAPI', {
  version,
});