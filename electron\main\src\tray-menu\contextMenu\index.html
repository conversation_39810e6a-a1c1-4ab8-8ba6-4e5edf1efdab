<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title></title>
  <style>
    html, body {
      background: transparent;
    }
    body {
      transform: translateX(-68px);
      width: 300px;
      max-width: 300px;
    }
    [v-cloak] {
      display: none;
    }
    * {
      padding: 0;
      margin: 0;
      user-select: none;
      box-sizing: border-box;
    }
    #app {
      padding: 8px;
    }
    .menu {
      position: relative;
      margin: auto;
      width: 130px;
      background-color: #FFFFFF;
      color: rgba(20, 25, 30, 1);
      list-style: none;
      padding: 8px;
      border-radius: 5px;
      box-shadow: 0px 2px 15px 0px rgba(0, 0, 0, 0.15);
    }
    .menu-item {
      padding: 0 8px;
      height: 26px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      line-height: 16px;
      border-radius: 5px;
    }
    .menu-item .icon-arrow-left {
      width: 6px;
      height: 6px;
      color: #8B8D94;
    }
    .menu-item .icon-arrow-left::before {
      display: block;
      font-size: 12px;
      width: 12px;
      height: 12px;
      transform: scale(0.5) translateY(-3px);
      transform-origin: left top;
    }
    .menu-item:hover {
      background: linear-gradient(46deg, var(---greadient-color-primary-left, #464B50) -0.9%, var(---greadient-color-primary-right, #14191E) 100.9%);
      color: #FFFFFF;
    }
    .menu-item:hover .icon-arrow-left {
      height: 6px;
      color: #FFF;
    }
    .submenu {
      position: absolute;
      left: calc(100% + 4px);
      top: 0;
      width: 130px;
      padding: 8px;
      background-color: #FFFFFF;
      color: rgba(20, 25, 30, 1);
      list-style: none;
      margin: 0;
      border-radius: 5px;
      box-shadow: 0px 2px 15px 0px rgba(0, 0, 0, 0.15);
    }
    .submenu-item {
      padding: 0 8px;
      height: 26px;
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 12px;
      line-height: 16px;
      border-radius: 3px;
      letter-spacing: 0.12px;
      white-space: nowrap;
    }
    .submenu-item:hover {
      background: linear-gradient(46deg, var(---greadient-color-primary-left, #464B50) -0.9%, var(---greadient-color-primary-right, #14191E) 100.9%);
      color: #FFFFFF;
    }
    .submenu-item:hover .iconfont {
      color: #FFFFFF !important; 
    }
    .submenu-item .iconfont {
      font-size: 12px;
      margin-right: 3px;
    }
    .submenu-item .iconfont.icon-correct-bold {
      font-size: 12px;
      margin-right: 0;
      margin-left: auto;
      color: #7DD95E;
    }
    .split-line {
      width: 100%;
      height: 1px;
      background-color: rgba(0, 0, 0, 0.08);
      margin: 4px 0;
    }
  </style>
  
  <link href="./iconfont.css" rel="stylesheet">
</head>
<body>
  <div id="app" v-cloak>
    <ul class="menu" @mouseenter="handleMenuEnter" @mouseleave="handleMenuLeave">
      <li v-for="menu in menus" :key="menu.key">
        <!-- 分割线处理 -->
        <div v-if="menu.split" class="split-line"></div>
        <!-- 主菜单项 -->
        <div
          v-else
          class="menu-item"
          @mouseenter="showSubMenu(menu)"
          @mouseleave="hideSubMenu"
          @click="handleMenuClick(menu)"
        >
          <span>{{ menu.label }}</span>
          <i v-if="menu.sub_menus" class="iconfont icon-common-arrow-left-line"></i>
        </div>
      </li>
      <!-- 子菜单 -->
      <ul
        v-if="activeMenu"
        class="submenu"
        @mouseenter="handleSubMenuEnter"
        @mouseleave="handleSubMenuLeave"
      >
        <li v-for="sub in activeMenu.sub_menus" :key="sub.label" class="submenu-item" @click="handleSubMenuClick(sub)">
          <i v-if="sub.icon" :class="['iconfont', sub.icon]" :style="{ color: sub.color }"></i>
          <span>{{ sub.label }}</span>
        </li>
      </ul>
    </ul>
  </div>
  <script src="../../../weblibs/vue.global.prod.js"></script>
  <script>
    const { createApp, ref, computed, watch, onMounted, onBeforeUnmount } = Vue;

    const app = createApp({
      setup() {
        // 响应式数据
        const activeMenu = ref(null); // 用于追踪当前显示的子菜单
        const menu_data = ref({
          isMouseEnter: false,
        });
        const hideTimer = ref(null);

        // 计算属性
        const menus = computed(() => {
          return [{
            label: '显示主菜单',
            key: 'show_main_window'
          }, {
            label: '退出',
            key: 'exit_app'
          }]
        });

        // 监听器
        watch(activeMenu, (val) => {
          window.trayContextMenuAPI.handleShowSubMenu(val ? val.key : null)
        });

        // 方法
        const init = () => {
          window.trayContextMenuAPI && window.trayContextMenuAPI.onReceiveData(handleData)
          window.trayContextMenuAPI.getHoverStatus((_) => {
            window.trayContextMenuAPI.setHoverStatus(menu_data.value.isMouseEnter)
          })
        };

        const handleData = (data) => {
          menu_data.value = {
            ...menu_data.value,
            ...data
          }
        };

        const showSubMenu = (menu) => {
          clearTimeout(hideTimer.value);
          if (menu.sub_menus) {
            activeMenu.value = menu;
          } else {
            activeMenu.value = null;
          }
        };

        const hideSubMenu = () => {
          hideTimer.value = setTimeout(() => {
            activeMenu.value = null;
          }, 200);
        };

        const handleMenuClick = (menu) => {
          if (menu.sub_menus) {
            return;
          }
          window.trayContextMenuAPI.handleMenuClick({menu})
        };

        const handleSubMenuClick = (subMenu) => {
          clearTimeout(hideTimer.value);
          window.trayContextMenuAPI.handleMenuClick({
            menu: activeMenu.value,
            sub_menu: subMenu
          })
        };

        const handleSubMenuEnter = () => {
          clearTimeout(hideTimer.value);
        };

        const handleSubMenuLeave = () => {
          hideSubMenu();
        };

        const handleMenuEnter = () => {
          menu_data.value.isMouseEnter = true;
        };

        const handleMenuLeave = () => {
          menu_data.value.isMouseEnter = false;
        };

        // 生命周期钩子
        onMounted(() => {
          init();
        });

        onBeforeUnmount(() => {
          if (hideTimer.value) {
            clearTimeout(hideTimer.value);
          }
        });

        return {
          activeMenu,
          menu_data,
          menus,
          showSubMenu,
          hideSubMenu,
          handleMenuClick,
          handleSubMenuClick,
          handleSubMenuEnter,
          handleSubMenuLeave,
          handleMenuEnter,
          handleMenuLeave
        };
      }
    });

    app.mount('#app');
  </script>
</body>
</html>