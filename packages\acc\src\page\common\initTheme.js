function initSystemTheme() {
  let theme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  document.body.setAttribute('theme', theme)
  window.matchMedia('(prefers-color-scheme: dark)')
  .addEventListener('change', e => {
    console.log('主题已切换:', e.matches ? '深色' : '浅色')
    document.body.setAttribute('theme', e.matches ? 'dark' : 'light')
  });
}

initSystemTheme()
  