<template>
  <div
    class="cpt-hb-dialog-wrapper"
    :class="{ 'has-top-icon': topIcon }"
    @click.stop="handleClickWrapper"
  >
    <div
      class="hb-dialog-container"
      @click.stop
    >
      <div v-if="showCloseBtn" class="dialog-close-btn" @click="handleClickCloseBtn">
        <i class="iconfont icon-common-close-line"></i>
      </div>

      <div
        class="dialog-icon-wrapper"
        v-if="topIcon"
      >
        <img
          :src="topIcon"
          alt=""
        />
      </div>
      <div class="dialog-header-group">
        <div>
          <p
            v-if="!(config.title instanceof Object)"
            class="title"
            v-html="config.title"
          ></p>
          <p
            v-else
            class="title"
          >
            <span v-for="(frag, index) of Object.keys(config.title)">
              {{ frag }}
            </span>
          </p>
        </div>
        <p
          v-if="config.desc"
          class="desc"
          v-html="config.desc"
        ></p>
        <div v-if="config.input" class="input-wrapper">
          <input
            v-model="inputValue"
            :placeholder="config.input.placeholder"
          />
        </div>
        <slot name="header"> </slot>
      </div>
      <div class="slot-wrapper">
        <slot></slot>
      </div>
      <div
        class="bottom-wrapper"
        v-if="!$slots.bottom && (config.confirm || config.cancel)"
      >
        <div
          class="bottom-text-btn"
          v-if="config.textBtn"
          v-text="config.textBtn.text"
          @click="handleClickTextBtn"
        />
        <Button
          v-if="config.cancel"
          :text="config.cancel.text || '取消'"
          :type="config.cancel.type || 'cancel'"
          @click="handleClickCancelBtn"
        />
        <Button
          v-if="config.confirm"
          :text="config.confirm.text || '确认'"
          :type="config.confirm.type || 'primary'"
          @click="handleClickConfirmBtn"
        />
      </div>
    </div>
  </div>
</template>
<script setup name="HbDialog">
import Button from './Button.vue';
import { ref, defineProps, defineEmits, watch, onMounted } from 'vue';

const emit = defineEmits([
  'close',
  'confirm',
  'cancel',
  'textBtn',
  'update:value',
]);

const show = ref(false);
const inputValue = ref('');
const props = defineProps({
  value: {
    type: Boolean,
  },
  config: {
    type: Object,
  },
  topIcon: {
    type: String,
    default: undefined,
  },
  disableWrapperClose: {
    type: Boolean,
    default: false,
  },
  showCloseBtn: {
    type: Boolean,
    default: false,
  },
});

onMounted(() => {
  watch(show, (v) => {
    emit('update:value', v);
    emit('close');
  });
  watch(() => props.config, (v) => {
    if(v.input && v.input.value) {
      inputValue.value = v.input.value
    }
  })
});
// 初始化弹窗
const handleInitData = () => {
  updateShow(props.value.value);
};

const handleClickTextBtn = () => {
  emit('textBtn')
}

const handleClickCancelBtn = () => {
  updateShow(false)
  emit('cancel')
}

const handleClickConfirmBtn = () => {
  emit('confirm', inputValue.value)
}

/**
 * 更新show的值
 * @param value 是否显示
 */
const updateShow = (value) => {
  show.value = value;
};

const handleClickWrapper = () => {
  if (props.disableWrapperClose) return;

  updateShow(false);
};

const handleClickCloseBtn = () => {
  updateShow(false);
};

handleInitData();
</script>

<style lang="scss">
.cpt-hb-dialog-wrapper {
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 1;
  top: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;

  &.has-top-icon {
    .hb-dialog-container {
      padding-top: 50px;
    }
  }
  .hb-dialog-container {
    background-color: $general-color-bg-3;
    border-radius: 8px;
    width: 315px;
    max-width: 90vw;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    gap: 8px;

    position: relative;
  }

  .dialog-close-btn {
    position: absolute;
    top: 16px;
    right: 12px;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 10;
    color: var(---general-color-text-4, #C8CDD2);
  }

  .dialog-icon-wrapper {
    width: 80px;
    height: 80px;
    padding: 3px;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    top: -40px;
    border-radius: 40px;
    background-color: $general-color-bg-4;
    img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }

  .dialog-header-group {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 8px;

    .title {
      font-size: 20px;
      font-weight: 600;
      margin: 0;
      color: $general-color-text-1;
    }

    .desc {
      font-size: 14px;
      color: #8e8e93;
      margin: 0;
    }

    .input-wrapper {
      width: 100%;
      height: 40px;
      padding: 9px 10px;
      border: 1px solid $general-color-stroke-1;
      border-radius: 5px;
      background-color: $general-color-bg-4;
      margin-top: 8px;
      input {
        width: 100%;
        text-align: center;
        color: $general-color-text-1;
        font-size: 14px;
        font-weight: 400;
        line-height: 18px;
        background-color: transparent;
        &::placeholder {
          color: $general-color-text-4;
        }
      }
    }
  }

  .slot-wrapper {
    overflow-y: auto;
    overflow-y: overlay;
    display: flex;
    flex-direction: column;

    &::-webkit-scrollbar {
      display: none;
    }
  }

  .bottom-wrapper {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 42px;
    gap: 10px;
    margin-top: 8px;

    .bottom-text-btn {
      margin-right: auto;
      color: #8e8e93;
      cursor: pointer;
      font-size: 14px;
      transition: color 0.2s;

      &:hover {
        color: #fff;
      }
    }
  }
}
</style>
