import { axiosPostHandler } from './config.js'
const BASE_XHH = 'https://api.xiaoheihe.cn'
// const BASE_XHH = 'http://api.debugmode.cn'
// 通过box_data获取token
export function getSMToken (params, formdata) {
	let url = BASE_XHH + '/rc/box_data/callback'
	return axiosPostHandler(url, params, formdata)
}

// 测试lacktoken
// export function testLackToken (args) {
// 	let url = '/rc/test_lack_token'
// 	return axiosGetHandler(url, args)
// }