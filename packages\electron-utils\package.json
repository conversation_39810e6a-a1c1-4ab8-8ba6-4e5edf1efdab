{"name": "@heybox/electron-utils", "version": "1.0.0", "description": "electron utilities", "main": "src/index.js", "exports": {".": "./src/index.js", "./log": "./src/log.js", "./version": "./src/version.js", "./regedit": "./src/regedit.js", "./require_addon": "./src/require_addon.js", "./store": "./src/store.js", "./cdn": "./src/cdn.js", "./request": "./src/request.js", "./eventBus": "./src/eventBus.js", "./hardware_report": "./src/hardware_report.js", "./system_window_list": "./src/system_window_list.js"}, "files": ["src"], "peerDependencies": {"@sentry/electron": "^4.24.0", "electron": "33.2.0", "electron-store": "8.0.0"}, "dependencies": {"@heybox/mini-program": "workspace:*", "@heybox/node-inject": "workspace:*", "@heybox/steam-user": "^1.0.0", "adm-zip": "0.5.10", "cos-nodejs-sdk-v5": "^2.11.19", "dot-prop": "^6.0.1", "electron-log": "^4.4.8", "js-md5": "^0.8.3"}}