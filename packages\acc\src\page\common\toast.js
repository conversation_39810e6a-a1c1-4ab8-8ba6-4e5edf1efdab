// 创建全局Toast容器
const toastContainer = document.createElement('div')
toastContainer.className = 'toast-container'
document.body.appendChild(toastContainer)

// Toast函数
export const toastError = (message, duration = 3000) => {
  const toast = document.createElement('div')
  toast.className = 'toast'
  toast.innerHTML = `
    <i class="iconfont icon-toast-warn"></i>
    <p>${message}</p>
  `

  // 自动关闭
  const close = () => {
    toast.classList.add('out')
    toast.addEventListener('animationend', () => toast.remove())
  }

  // 点击关闭
  toast.addEventListener('click', close)
  
  // 添加进容器并设置自动消失
  toastContainer.appendChild(toast)
  setTimeout(close, duration)

  // 处理多个toast位置
  const toasts = document.querySelectorAll('.toast:not(.out)')
  toasts.forEach((t, index) => {
    t.style.transform = `translateY(10px)`
  })
} 

// Toast函数
export const toastSuccess = (message, duration = 3000) => {
  const toast = document.createElement('div')
  toast.className = 'toast'
  toast.innerHTML = `
    <i class="iconfont icon-toast-success"></i>
    <p>${message}</p>
  `

  // 自动关闭
  const close = () => {
    toast.classList.add('out')
    toast.addEventListener('animationend', () => toast.remove())
  }

  // 点击关闭
  toast.addEventListener('click', close)
  
  // 添加进容器并设置自动消失
  toastContainer.appendChild(toast)
  setTimeout(close, duration)

  // 处理多个toast位置
  const toasts = document.querySelectorAll('.toast:not(.out)')
  toasts.forEach((t, index) => {
    t.style.transform = `translateY(10px)`
  })
} 