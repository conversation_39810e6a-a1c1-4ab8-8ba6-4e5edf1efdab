{"name": "heybox-app-electron", "version": "1.0.0", "description": "基于Electron的多窗口应用", "workspaces": ["web/*", "packages/*", "electron/*"], "private": true, "scripts": {"dev": "node scripts/dev.js", "dev:electron": "cd electron/main && npm run dev", "dev:web": "node scripts/exec-web.js dev", "build": "yarn build:web && yarn build:electron", "build:electron": "node scripts/build-electron.js", "build:web": "node scripts/exec-web.js build && node scripts/copy-web-dist.js web", "build:shared": "yarn workspaces foreach --all -p --include \"@heybox-app-web-shared/*\" run build && node scripts/copy-web-dist.js share", "check:timers": "node scripts/check-timers.js"}, "author": "heybox", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3", "glob": "^10.3.10"}, "packageManager": "yarn@4.9.2", "resolutions": {"@electron/node-gyp": "npm:@electron/node-gyp@latest", "electron": "33.2.0", "electron-store": "8.2.0", "rollup": "4.44.1", "vite": "4.5.14", "vue": "^3.4.0"}}