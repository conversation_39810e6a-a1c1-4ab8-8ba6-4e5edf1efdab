<template>
  <div
    class="cpt-game-card"
    @click="handleClickCard"
    :class="{
      'gradient-border': showGradientBorder,
    }"
  >
    <div class="card-content">
      <div class="card-img" :class="{ 'no-image': !game.vertical_head_image }">
        <HbImage
          v-if="game.vertical_head_image"
          :src="game.vertical_head_image"
          :webp_quality="30"
        />
        <div v-else class="no-image-icon">
          <HbImage
            src="https://imgheybox.max-c.com/dev/bbs/2025/07/01/********************************.png"
            :webp_quality="30"
            alt="缺省图标"
          />
          <div class="game-name">{{ game.name }}</div>
        </div>
      </div>
    </div>
    <div class="card-bottom-mask"></div>
    <div class="card-bottom-bar">
      <div class="card-bottom-bar-time">
        <div class="card-bottom-bar-time-mine">{{ formatPlaytime(playtime_forever) }} h</div>
        <div class="card-bottom-bar-time-average">{{ formatPlaytime(playtime_2weeks) }} h</div>
      </div>
      <div class="card-bottom-bar-progress">
        <Progress
          :progress="playtime_percent"
          :color_start="playtime_start_color"
          :color_end="playtime_end_color"
        />
      </div>
    </div>
    <div v-if="achievement_total > 0" class="card-achievement">
      {{ game_achieved }} / {{ achievement_total }}
      <div v-if="game_achieved >= achievement_total" class="card-achievement-icon">
        <img
          src="https://imgheybox.max-c.com/dev/bbs/2025/06/04/********************************.png"
          alt=""
          loading="lazy"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="GameCard">
import { ref, defineProps, defineEmits, computed } from 'vue';
import Progress from '@/components/func/Progress.vue';
import { useStore } from 'vuex';

const store = useStore();

const props = defineProps({
  game: {
    type: Object,
    required: true,
  },
});

const game_achieved = computed(() => {
  return props.game.achievement_stat && props.game.achievement_stat.achieved !== undefined ? props.game.achievement_stat.achieved : '-'
})

const achievement_total = computed(() => {
  return props.game.achievement_stat && props.game.achievement_stat.total !== undefined ? props.game.achievement_stat.total : '-'
})

const showGradientBorder = computed(() => {
  return achievement_total.value > 0 && game_achieved.value >= achievement_total.value;
});

const playtime_2weeks = computed(() => {
  return props.game.playtime_stat.playtime_2weeks || 0
})

const playtime_percent = computed(() => {
  return (props.game.playtime_stat.playtime_percent || 0) * 100
})

const playtime_start_color = computed(() => {
  return props.game.playtime_stat.playtime_start_color || 'green'
})

const playtime_end_color = computed(() => {
  return props.game.playtime_stat.playtime_end_color || 'green'
})

const playtime_forever = computed(() => {
  return props.game.playtime_stat.playtime_forever || 0
})

const emit = defineEmits(['clickCard']);

const handleClickCard = () => {
  emit('clickCard', props.game);
};

const formatPlaytime = (playtime) => {
  return (playtime / 60).toFixed(1)
}
</script>

<style lang="scss">
.cpt-game-card {
  border-radius: 8px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.2);
  transition: transform 0.3s ease-in-out;
  cursor: pointer;
  z-index: 0;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-3px);
  }
  &.gradient-border {
    padding: 3px;
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: url('https://imgheybox.max-c.com/dev/bbs/2025/06/04/********************************.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      background-position: center;
      z-index: 1;
    }
  }
  .card-content {
    width: 100%;
    position: relative;
    aspect-ratio: 15/22;
    border-radius: inherit;
    .card-img {
      width: 100%;
      height: 100%;
      position: relative;

      &.no-image {
        background: linear-gradient(46deg, var(---greadient-color-primary-left, #464B50) -0.9%, var(---greadient-color-primary-right, #14191E) 100.9%);
      }

      img {
        width: 100%;
        height: 100%;
      }

      .no-image-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 10px;

        img {
          width: 80px;
          height: 80px;
        }

        .game-name {
          color: var(---general-color-text-3, #8C9196);
          text-align: center;
          font-family: "PingFang SC";
          font-size: 13px;
          font-style: normal;
          font-weight: 500;
          line-height: 17px;
          max-width: 120px;
          word-wrap: break-word;
          overflow-wrap: break-word;
        }
      }
    }
  }
  .card-bottom-mask {
    position: absolute;
    bottom: 0;
    left: 1px;
    width: calc(100% - 2px);
    height: 62px;
    background: linear-gradient(
      180deg,
      rgba(0, 0, 0, 0) 0%,
      rgba(0, 0, 0, 0.5) 100%
    );
    border-radius: 0 0 8px 8px;
    z-index: 1;
  }
  .card-bottom-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 46px;
    z-index: 2;
    display: flex;
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
    .card-bottom-bar-time {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      & > div {
        color: var(--white-gamerecord-color-white-100a, #fff);
        font-family: 'Helvetica Neue';
        font-size: 12px;
        line-height: 14px;
      }
    }
    .card-bottom-bar-progress {
      width: 100%;
    }
  }
  .card-achievement {
    position: absolute;
    top: 12px;
    right: 18px;
    display: flex;
    height: 18px;
    padding: 1px 4px 1px 4px;
    justify-content: flex-end;
    align-items: center;
    gap: 1px;
    border-radius: 3px;
    background-color: var(
      --black-gamerecord-color-black-50a,
      rgba(0, 0, 0, 0.5)
    );

    color: var(--white-gamerecord-color-white-100a, #fff);
    text-align: right;
    font-family: 'Helvetica Neue';
    font-size: 12px;
    font-weight: 700;
    line-height: 15px;

    .card-achievement-icon {
      width: 24px;
      height: 24px;
      position: absolute;
      top: 50%;
      right: -18px;
      transform: translateY(-50%);
      img {
        width: 100%;
        height: 100%;
      }
      // background: url('https://imgheybox.max-c.com/dev/bbs/2025/06/04/********************************.png')
      //   transparent / cover no-repeat;
    }
  }
}
</style>
