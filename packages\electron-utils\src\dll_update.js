const fs = require('fs');
const path = require('path');
const request = require('./request')
const log = require('./log')
const { app } = require('electron')
const {
  ensureDirExists,
  walkDir,
} = require('@heybox/mini-program/mini_sdk')
const {
  removeFile,
  extractZip
} = require('./fileHandler')
const heyboxdll_base_dir = path.join(app.getPath('userData'), 'heyboxdll')
let dllPath

// 获取dll所在文件夹路径
function getHeyboxDllPath() {
  // 客户端声明周期内只获取一次
  if (dllPath !== undefined) return dllPath
  
  return getDllVersion().then(async res => {
    if (res.status === 'ok' && res.result.file) {
      let { version, file } = res.result
      let versionPath = path.join(heyboxdll_base_dir, version)
      let needDownload = true

      try {
        // 如果存在版本文件夹，并且文件夹内存在dll和exe文件，无需要下载新版本
        if (await checkDirExit(versionPath)) {
          if (await checkPathContainFileType(versionPath, ['.dll', '.exe'])) {
            needDownload = false
          } else {
            // 如果文件夹内文件缺少，清空文件夹
            tryRemoveFile(versionPath)
          }
        }
      } catch (error) {
        log.error('[getHeyboxDllPath check]', error)
      }
      if (needDownload) {
        try {
          let zipPath = path.join(heyboxdll_base_dir, `${version}.zip`)
          await downloadDll(file, version, zipPath, versionPath)
          dllPath = versionPath.replace(/\//g, '\\')
        } catch (error) {
          // 如果下载dll失败，设置dllPath为'', 在注入时不传dllDir字段，会默认使用addon文件夹内dll
          log.error('[getHeyboxDllPath downloadDll error]', error)
          dllPath = ''
        }
      } else {
        dllPath = versionPath.replace(/\//g, '\\')
      }
      log.info('[getHeyboxDllPath]', dllPath)
      return dllPath
    } else {
      dllPath = ''
      return dllPath
    }
  }).catch((e) => {
    // 如果获取dll接口失败了, 优先使用heyboxdll文件夹内最新的版本号，没有则用默认addon内的dll
    log.error('[getHeyboxDllPath getDllVersion error]', e)
    dllPath = findMaxNumericFolder(heyboxdll_base_dir) || ''
    return dllPath
  })
}


// 接口获取最新的dll版本信息
async function getDllVersion() {
  return request.$post('/heyboxpc/client/overlay_version_check', {})
}

// 下载解压 dll 文件
async function downloadDll(file, verison, zipPath, versionPath) {
  return new Promise(async (resolve, reject) => {
    const { downloadFile } = require('./cdn')
    await ensureDirExists(versionPath)
    downloadFile(
      file,
      zipPath,
      undefined,
      verison,
      async (action) => {
        if (action === 'success') {
          try {
            await ensureDirExists(versionPath)
            await extractZip(zipPath, versionPath)
            await tryRemoveFile(zipPath)

            // 删除多余版本
            let { children: versions } = await walkDir(heyboxdll_base_dir);
            versions.forEach(async (v) => {
              if (v.name != verison) {
                let delPath = path.join(heyboxdll_base_dir, v.name)
                await tryRemoveFile(delPath)
              }
            })
            resolve()
          } catch (error) {
            reject(error)
          }

        } else if (action === 'error') {
          await tryRemoveFile(zipPath)
          reject(new Error('[downloadDll]: downloadFile error'))
        }
      }
    )
  })

}

async function tryRemoveFile(p) {
  try {
    await removeFile(p)
  } catch ({error}) {
    log.error(`[downloadDll]: rm ${p} error, ${error.message}`)
  }
}

// 检查路径是否存在且是目录
async function checkDirExit(dirPath) {
  try {
    const isDir = fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory();
    return !!isDir;
  } catch (error) {
    log.error("checkDirExit Error:", error);
    return false;
  }
}

// 读取目录内容并检查是否包含指定类型的文件
async function checkPathContainFileType(dirPath, fileTypes) {
  try {
    const files = fs.readdirSync(dirPath);
    console.log(path.extname(files[0]))
    return files.some(file => fileTypes.includes(path.extname(file)));
  } catch (error) {
    log.error("checkPathContainFileType Error:", error);
    return false;
  }
}

/**
 * 查找指定目录下，名称为纯数字且数值最大的文件夹路径
 * @param {string} dirPath - 需要查找的目录路径
 * @returns {string|null} - 数值最大的纯数字文件夹路径，若不存在返回 null
 */
function findMaxNumericFolder(dirPath) {
  if (!fs.existsSync(dirPath) || !fs.statSync(dirPath).isDirectory()) {
      return null;
  }

  const files = fs.readdirSync(dirPath);
  let maxNumericFolder = null;
  let maxNumber = -Infinity;

  files.forEach(file => {
      const fullPath = path.join(dirPath, file);
      if (fs.statSync(fullPath).isDirectory() && /^\d+$/.test(file)) {
          const number = parseInt(file, 10);
          if (number > maxNumber) {
              maxNumber = number;
              maxNumericFolder = fullPath;
          }
      }
  });

  return maxNumericFolder;
}

module.exports = {
  getHeyboxDllPath,
}