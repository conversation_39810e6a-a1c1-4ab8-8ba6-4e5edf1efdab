const { STEAM_INJECT_STATE } = require('./assets/constant')
const utils = require('@heybox/electron-utils')
const { log, eventBus: EventBus, store } = require('@heybox/electron-utils')
const request = require('@heybox/electron-utils/request')

class Steam_Inject {
  constructor() {
    this.isInjectSteamRegister = false
    this.steamInjectCrash = false
    this.steam_version = null
    EventBus.on('allow_steam_nethook_change', this.onAllowSteamNethookChange.bind(this))
  }

  // 注入steam hook
  async injectSteam() {
    if (
      !getSteamNethookConfig() ||
      this.isInjectSteamRegister ||
      this.steamInjectCrash ||
      !(await this.getSteamInjectCommend())
    ) return


    const {
      injectSteam,
      registerEvent,
      initSteamNethook,
    } = require("./addon");
    let res = await injectSteam()
    log.info("[steam inject event]: injectSteam", res)
    if (res.result) {
      this.isInjectSteamRegister = true
      registerEvent({
        "steam.connection.opened": async (payload) => {
          log.info("[steam inject event]: steam.connection.opened", payload.pid)
          initSteamNethook(await this.getSteamInjectCommend())
          this.updateSteamInjectState(STEAM_INJECT_STATE.SUCCESS)
          require("../steam/index")
        },
        "steam.connection.close": (payload) => {
          log.info('[steam.connection.close]', payload)
          // 如果reason是client_lost，认为是注入导致的steam崩溃，禁止steam注入，并上报崩溃
          if (payload.reason === 'client_lost') {
            this.steamInjectCrash = true
            this.updateSteamInjectState(STEAM_INJECT_STATE.FAILURE)
            // const { report } = require("../assets/js/report")
            // report('steam-inject-crash-risk', {
            //   version: this.steam_version
            // })
          } else {
            this.isInjectSteamRegister = false
            this.updateSteamInjectState(STEAM_INJECT_STATE.PENDING)
          }
        },
        "steam.nethook.pkg": (payload) => {
          if (!getSteamNethookConfig()) return
          main_config.steamLog && log.info("[steam.nethook.pkg]", payload, global.ws_id);
          // checkIsGameStateBuf过滤游戏状态相关的二进制数据
          if (global.ws_id && this.checkIsGameStateBuf(payload)) {
            this.reportSteamGameState(payload)
          }
          // steam nethook拦截到二进制数据时触发，小程序中处理，如dota2战绩
          EventBus.emit('steamNethook:pkg', payload)
        }
      })
    }
  }
  reportSteamGameState = utils.debounce(async function (payload) {
    let res = await request.$post('/chatroom/v2/common/steam/state', {}, {
      data: payload.pkg.toString('base64'),
      ws_id: global.ws_id
    })
    if (res.status === 'ok') {
      EventBus.emit('steam_hook:steam_game_state', 'steam', res.result)
    }
  }, 300)
  rejectSteam() {
    const { rejectSteam } = require("./addon");
    rejectSteam()
    this.isInjectSteamRegister = false
  }
  // 获取加密的steam注入JSON
  async getSteamInjectCommend() {
    if (this.steamInjectCommend !== undefined) {
      return this.steamInjectCommend
    }
    try {
      const { getSteamVersion } = require("./addon");
      this.steam_version = getSteamVersion()
      log.info('[getSteamVersion]', this.steam_version)
      // 如果getSteamVersion没获取到steam_version，说明steam没启动，不进行注入
      // 也不更新steamInjectCommend的数据
      if (!this.steam_version) return false

      let res = await request.$get('/chatroom/v2/common/steam/hook', {
        steam_version: this.steam_version
      })
      if (res.status === 'ok') {
        let { hook, game_state_msg = [] } = res.result
        log.info('[steamInjectCommend]', `${hook.substring(0, 5)}***${hook.substring(hook.length - 5)}`)
        this.steamInjectCommend = utils.base64ToBuffer(hook)
        this.steamGameStateFilters = game_state_msg
      } else {
        this.steamInjectCommend = false
        global.steam_inject_state = STEAM_INJECT_STATE.FAILURE
      }
      return this.steamInjectCommend
    } catch (e) {
      log.info('[getSteamInjectCommend error]', e)
    }
    return false
  }
  // 更新steam注入状态
  updateSteamInjectState(v) {
    global.steam_inject_state = v
    // 小程序中需要接收steam注入状态的变更
    EventBus.emit('Steam_Inject_State_Change', v)
  }
  checkIsGameStateBuf(payload) {
    // 检查是不是游戏状态的二进制数据，如果filter中的参数和payload里的参数完全一致，就认为是游戏状态数据
    for (let filter of this.steamGameStateFilters) {
      for (let k in filter) {
        if (payload[k] != filter[k]) {
          break
        }
        return true
      }
    }
    return false
  }
  onAllowSteamNethookChange(newV, oldV) {
    if (newV && !oldV) {
      // 如果开关切换，强制执行一次steam注入函数，
      // 如果已经成功注入了会在injectSteam函数内直接return，修改steam注入开关时触发
      this.injectSteam()
    } else if (!newV) {
      // 如果开关关闭了，清除本地储存的steam游戏状态
      this.rejectSteam()
      EventBus.emit('steam_hook:steam_game_state', 'steam', undefined)
    }
  }
}

function getSteamNethookConfig() {
  return store.get('user_account_settings')?.allow_steam_nethook
}

module.exports = new Steam_Inject()