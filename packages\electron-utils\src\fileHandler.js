const fs = require('fs');
const vdf = require('./vdf')
const path = require('path')

let AdmZip = null

function readIniFile(filePath) {
    const config = {};
    const lines = fs.readFileSync(filePath, 'utf-8').split(/\r?\n/);

    let section = null;
    for (const line of lines) {
        const trimmedLine = line.trim();
        // 跳过空行和注释行
        if (!trimmedLine || trimmedLine.startsWith(';') || trimmedLine.startsWith('#')) continue;

        // 处理节头部
        if (trimmedLine.startsWith('[') && trimmedLine.endsWith(']')) {
            section = trimmedLine.slice(1, -1);
            config[section] = config[section] || {};
            continue;
        }

        // 处理键值对
        const eqPos = trimmedLine.indexOf('=');
        if (eqPos === -1) continue; // 如果没有等号，跳过该行
        const key = trimmedLine.substring(0, eqPos).trim();
        const value = trimmedLine.substring(eqPos + 1).trim();
        config[section][key] = value;
    }
    return config;
}

function writeIniFile(filePath, config) {
    let content = '';
    for (const section in config) {
        if (config.hasOwnProperty(section)) {
            content += `[${section}]\n`;
            for (const key in config[section]) {
                if (config[section].hasOwnProperty(key)) {
                    content += `${key} = ${config[section][key]}\n`;
                }
            }
            content += '\n';
        }
    }
    fs.writeFileSync(filePath, content);
}

function readVdfFile(filePath) {
    const data = fs.readFileSync(filePath)
    const parsed = vdf.parse(data.toString())
    return parsed
}

function writeVdfFile(filePath, data) {
    const content = vdf.dump(data)
    fs.writeFileSync(filePath, content)
}

function removeFile(filePath) {
  const fs = require('original-fs')
  return new Promise((resolve, reject) => {
    try {
      if (fs.existsSync(filePath)) {
        fs.rmSync(filePath, {
          recursive: true,
          force: true
        })
        resolve()
      } else {
        reject(`${filePath} was NotExist`)
      }
    } catch (error) {
      reject({error, errorText: generateErrorText(error, '删除文件')})
    }
  })
}
function moveFile(filePath, targetPath) {
  return new Promise((resolve, reject) => {
    try {
      if (fs.existsSync(filePath)) {
        fs.renameSync(filePath, targetPath)
        resolve()
      } else {
        reject(`${filePath} was NotExist`)
      }
    } catch (error) {
      reject(error.message)
    }
  })
}
function getFileData(filePath) {
  return new Promise((resolve, reject) => {
    try {
      filePath = path.normalize(filePath)
      const ext = path.extname(filePath)
      switch (ext) {
        case '.vdf':
          const vdfData = readVdfFile(filePath)
          resolve(vdfData)
          break;
        case '.ini': 
          let iniData = readIniFile(filePath)
          resolve(iniData)
          break
        default:
          fs.readFile(filePath, (err, buffer) => {
            if (err) {
              reject(error.message || 'Error')
            } else {
              resolve({
                name: filePath.match(/([^\/]*)\/*$/)[1],
                buffer
              })
            }
          })
          break
      }
    } catch (error) {
      reject(error.message)
    }
  })
}

function writeFileData(filePath, data) {
  return new Promise((resolve, reject) => {
    try {
      filePath = path.normalize(filePath)
      switch (path.extname(filePath)) {
        case '.ini': 
          writeIniFile(filePath, data)
          resolve(true)
          break
        case '.vdf': 
          writeVdfFile(filePath, data)
          resolve(true)
          break
        default:
          reject('暂不支持此类型文件')
          break;
      }
    } catch (error) {
      reject(error.message)
    }
  })
}

function createZip(zipPath, files) {
  return new Promise((resolve, reject) => {
    try {
      if (!AdmZip) {
        AdmZip = require('adm-zip')
      }
      let zip = new AdmZip()
      files.forEach((file) => {
        if (typeof file === 'string') {
          zip.addLocalFile(file)
        } else if (file.type === 'folder') {
          zip.addLocalFolder(file.path, path.basename(file.path))
        } else {
          zip.addLocalFile(file.path)
        }
      })
      zip.writeZip(path.join(zipPath))
      resolve()
    } catch (error) {
      reject(error.message)
    }
  })
}
function extractZip(zipPath, targetPath) {
  return new Promise((resolve, reject) => {
    try {
      if (!AdmZip) {
        AdmZip = require('adm-zip')
      }
      let zip = new AdmZip(zipPath)
      zip.extractAllTo(targetPath, true)
      resolve()
    } catch (error) {
      reject({error, errorText: generateErrorText(error, '解压文件')})
    }
  })
}
// 获取文件夹内修改日期最新的文件路径
function getLatestFile(folderPath) {
  try {
      const files = fs.readdirSync(folderPath);
      if (files.length === 0) {
          return null;
      }
      // 获取每个文件的完整路径和修改时间
      const fileStats = files.map(file => {
          const filePath = path.join(folderPath, file);
          const stats = fs.statSync(filePath);
          return { file: filePath, mtime: stats.mtime };
      });
      // 找到修改时间最新的文件
      fileStats.sort((a, b) => b.mtime - a.mtime);
      return fileStats[0].file;

  } catch (error) {
      console.error('Error reading directory or files:', error);
      return null;
  }
}
function generateErrorText (error, action = '操作') {
  if (!error) {
    return `${action}失败：未知错误，请联系客服处理`;
  }
  switch (error.code) {
    case 'EACCES':
    case 'EPERM':
      return `${action}失败：权限不足，请使用管理员权限运行程序`;
    case 'ENOENT':
      return `${action}失败：文件或目录未找到`;
    case 'ENOSPC':
      return `${action}失败：磁盘空间不足`;
    default:
      return `${action}失败：请联系客服处理`;
  }
}

module.exports = {
  readIniFile,
  writeIniFile,
  readVdfFile,
  writeVdfFile,
  removeFile,
  moveFile,
  getFileData,
  writeFileData,
  createZip,
  extractZip,
  getLatestFile,
  generateErrorText
}