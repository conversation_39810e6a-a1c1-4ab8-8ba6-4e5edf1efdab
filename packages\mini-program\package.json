{"name": "@heybox/mini-program", "version": "1.0.0", "description": "mini program", "main": "src/index.js", "exports": {".": "./src/index.js", "./mini_sdk": "./src/mini_sdk.js", "./appid_process": "./src/appid_process.js", "./bind_eventbus": "./src/bind_eventbus.js", "./pre_require": "./src/pre_require.js"}, "files": ["src"], "dependencies": {"@heybox/electron-utils": "workspace:*", "@heybox/ingame-notice": "workspace:*", "@heybox/node-inject": "workspace:*"}, "peerDependencies": {"electron": "33.2.0"}}