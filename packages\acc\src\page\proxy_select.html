<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>小黑盒加速器插件</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/theme.css">
    <link rel="stylesheet" href="../assets/font/iconfont.css">
  </head>
  <body>
    <div class="proxy-select-cpt">
      <div class="shading"></div>
      <div class="title-wrapper">
        <div class="title">
          小黑盒加速器 加速设置
          <span class="sub-title"></span>
        </div>
        <svg id="close-button" class="close pointer" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="Dismiss">
            <path id="Shape" d="M4.08859 4.21569L4.14645 4.14645C4.32001 3.97288 4.58944 3.9536 4.78431 4.08859L4.85355 4.14645L10 9.293L15.1464 4.14645C15.32 3.97288 15.5894 3.9536 15.7843 4.08859L15.8536 4.14645C16.0271 4.32001 16.0464 4.58944 15.9114 4.78431L15.8536 4.85355L10.707 10L15.8536 15.1464C16.0271 15.32 16.0464 15.5894 15.9114 15.7843L15.8536 15.8536C15.68 16.0271 15.4106 16.0464 15.2157 15.9114L15.1464 15.8536L10 10.707L4.85355 15.8536C4.67999 16.0271 4.41056 16.0464 4.21569 15.9114L4.14645 15.8536C3.97288 15.68 3.9536 15.4106 4.08859 15.2157L4.14645 15.1464L9.293 10L4.14645 4.85355C3.97288 4.67999 3.9536 4.41056 4.08859 4.21569L4.14645 4.14645L4.08859 4.21569Z" fill="#424242"/>
          </g>
        </svg>
      </div>
      <div class="query-select-wrapper">
        <div class="query-select-item">
          
          <div class="query-select-title">
            模式:
          </div>
          <div class="mode-selector selector-wrapper unexpand">
            <div class="selector-tab"></div>
            <div class="selector-content"></div>
          </div>
          <!-- <select class="mode-select"></select> -->
          <div class="mode-tips pointer">
            <i class="iconfont icon-question-bold"></i>
            模式攻略
          </div>
        </div>
        <div class="query-select-item">
          <div class="query-select-title">
            区服:
          </div>
          <div class="district-list">
          </div>
        </div>
      </div>
      <div class="table-wrapper">
        <div class="table-header">
          <div class="node">
            节点
            <div class="server-selector selector-wrapper unexpand">
            </div>
          </div>
          <div class="delay">延迟</div>
          <div class="loss">丢包</div>
          <div class="status">状态</div>
        </div>
        <div class="proxy-list-body">
        </div>
      </div>
      <div class="button-wrapper">
        <button id="config-button" class="primary-button">
          确定
        </button>
        <button id="cancel-button" class="default-button">
          取消
        </button>
      </div>
    </div>
  </body>
  <script src="./common/initTheme.js"></script>
  <script type="module">
    import { getUrlParam, throttle } from './common/utils.js'
    // import './assets/js/sm.js'
    
    let acc_district_list = JSON.parse(getUrlParam('acc_district_list'))
    let acc_mode_list = JSON.parse(getUrlParam('acc_mode_list'))
    let isp_list = []
    let acc_mode_id = Number(getUrlParam('acc_mode_id'))
    let name = getUrlParam('name')
    let selected_node_name = ''
    let default_isp = getUrlParam('isp') === 'undefined' ? '' : getUrlParam('isp') // 默认节点的isp
    let isLoading = false
    let isLoadAll = false
    let offset = 0
    let limit = 10
    let isModeSelectorExpand = false
    let isServerSelectorExpand = false
    const config = {
      acc_id: Number(getUrlParam('acc_id')),
      isp: getUrlParam('isp'), // 节点的isp
      server_region: Number(getUrlParam('server_region')),
    }
    
    function init() {
      initDom()
      getProxyNodeList()
      window.electronAPI.setOptions((events, params) => {
        console.log('setOptions', params)
        config.acc_id = params.acc_id
        config.isp = params.isp
        config.server_region = params.server_region
        name = params.name
        acc_mode_id = params.acc_mode_id
        acc_district_list = JSON.parse(params.acc_district_list)
        acc_mode_list = JSON.parse(params.acc_mode_list)
        default_isp = params.isp
        initDom(false)
        getProxyNodeList()
      })
    }
    async function getProxyNodeList(isInit = true) {
      if(isInit) {
        offset = 0
        isLoadAll = false
        const scrollbarEl = document.querySelector('.proxy-list-body')
        scrollbarEl.scrollTop = 0
      }
      let params = {
        ...config,
        timestamp: new Date().getTime(),
        offset,
        limit,
        isp: default_isp,
        isp_filter: '1',
      }
      console.log('getProxyNodeList', params)
      isLoading = true
      let res = await window.electronAPI.getProxyNodeList(params)
      console.log('getProxyNodeList', res)
      if(res.status === 'ok') {
        let proxyList = document.querySelector('.proxy-list-body')
        if(isInit) {
          default_isp = res.result.cur_isp
          isp_list = res.result.isp_filter
          proxyList.innerHTML = ''
          let defaultRow = document.createElement('div')
          defaultRow.classList.add('row', 'pointer')
          defaultRow.innerHTML = `
            <div class="node">
              <input type="radio" name="node" value=""/>
              默认
              <img src="https://imgheybox.max-c.com/oa/2025/04/27/********************************.png"/>
            </div>
            <div class="delay">-</div>
            <div class="loss">-</div>
            <div class="status"></div>
          `
          
          let radio = defaultRow.querySelector('input[type="radio"]')
          defaultRow.addEventListener('click', async (e) => {
            radio.checked = true
            selected_node_name = radio.value
            config.isp = default_isp
          })
          radio.checked = true
          proxyList.appendChild(defaultRow)
          initServerSelector()
        }
        let node_list = res.result.node_list || []
        node_list.forEach(item => {
          let row = document.createElement('div')
          row.classList.add('row')
          row.innerHTML = `
            <div class="node">
              <input type="radio" name="node" value="${item.name}"/>
              ${(item.first_hop_location ? `${item.first_hop_location} - ` : ``) + item.name}
            </div>
            <div class="delay">${item.rtt_avg}</div>
            <div class="loss">${item.packet_loss_rate}</div>
            <div class="status ${item.state === '空闲' ? '' : 'fail'}">${item.state}</div>
          `
          row.style.cursor = 'pointer'
          row.addEventListener('click', async (e) => {
            const radio = e.currentTarget.querySelector('input[type="radio"]')
            radio.checked = true
            selected_node_name = radio.value
            config.isp = item.isp
          })
          proxyList.appendChild(row)
        })
        if(node_list.length < limit) {
          isLoadAll = true
        } else {
          offset += limit
        }
      }
      isLoading = false
    }
    async function startAcc(params) {
      console.log('startAcc')
      let res = await window.electronAPI.startAcc(params)
      console.log('startAcc', res)
    }
    function initDom(isInit = true) {
      console.log('acc_mode_list', acc_mode_list)
      console.log('acc_district_list', acc_district_list)
      let subTitle = document.querySelector('.sub-title')
      subTitle.innerHTML = name

      let modeSelector = document.querySelector('.mode-selector')
      let modeSelectorTab = modeSelector.querySelector('.selector-tab')
      let modeSelectorContent = modeSelector.querySelector('.selector-content')
      modeSelectorContent.innerHTML = acc_mode_list.map(item => `<div class="option" value="${item.acc_mode_id}">${item.acc_mode_name}</div>`).join('')
      modeSelectorTab.innerHTML = `
        ${acc_mode_list.find(item => item.acc_mode_id === acc_mode_id).acc_mode_name}
        <i class="iconfont icon-arrow-up-bold"></i>
      `
      const unexpandModeSelector = (e) => {
        if(!modeSelector.contains(e.target)) {
          modeSelector.classList.add('unexpand')
          isModeSelectorExpand = false
        }
      }
      if(isInit) {
        // 初始化点击事件
        modeSelectorTab.addEventListener('click', () => {
          if(isModeSelectorExpand) {
            modeSelector.classList.add('unexpand')
            document.removeEventListener('click', unexpandModeSelector)
          } else {
            modeSelector.classList.remove('unexpand')
            document.addEventListener('click', unexpandModeSelector)
          }
          isModeSelectorExpand = !isModeSelectorExpand
        })
        modeSelectorContent.addEventListener('click', (e) => {
          console.log('e', e.target.getAttribute('value'))
          if(e.target.getAttribute('value')) {
            acc_mode_id = Number(e.target.getAttribute('value'))
            modeSelectorTab.innerHTML = `
              ${acc_mode_list.find(item => item.acc_mode_id === acc_mode_id).acc_mode_name}
              <i class="iconfont icon-arrow-up-bold"></i>
            `
            modeSelectorContent.querySelectorAll('.option').forEach(item => {
              item.classList.remove('selected')
            })
            e.target.classList.add('selected')
            modeSelector.classList.add('unexpand')
            document.removeEventListener('click', unexpandModeSelector)
            isModeSelectorExpand = false
          }
        })
      }

      let districtList = document.querySelector('.district-list')
      districtList.innerHTML = ''
      acc_district_list.forEach(item => {
        let el = document.createElement('el')
        el.innerHTML = item.acc_district_name
        el.classList.add('district-item')
        if(item.acc_district_id === config.server_region) {
          el.classList.add('selected')
        }
        el.addEventListener('click', () => {
          config.server_region = item.acc_district_id
          districtList.querySelectorAll('.district-item').forEach(item => {
            item.classList.remove('selected')
          })
          el.classList.add('selected')
          getProxyNodeList()
        })
        districtList.appendChild(el)
      })
      setTimeout(() => {
        const districtListHeight = districtList.offsetHeight
        console.log('districtListHeight', districtListHeight)
        let tableWrapperEl = document.querySelector('.table-wrapper')
        tableWrapperEl.style.height = `calc(100% - ${districtListHeight}px - 198px)`
      })

      if(isInit) {
        // 初始化点击事件
        let closeButton = document.getElementById('close-button')
        closeButton.addEventListener('click', close)
        let modeTips = document.querySelector('.mode-tips')
        modeTips.addEventListener('click', () => {
          window.electronAPI.openInBrowser('https://acc.xiaoheihe.cn/help/detail/0/3')
        })

        let configButton = document.getElementById('config-button')
        configButton.addEventListener('click', () => {
          startAcc({
            ...config,
            acc_mode_id,
            node_name: selected_node_name,
            lock_region: 0,
          })
        })

        let cancelButton = document.getElementById('cancel-button')
        cancelButton.addEventListener('click', close)

        let proxyListBody = document.querySelector('.proxy-list-body')
        proxyListBody.addEventListener('scroll', handleScroll)
      }
    }
    function close() {
      window.electronAPI.close('proxy_select')
    }
    const handleScroll = throttle(function() {
      if(isLoading || isLoadAll) return
      const scrollbarEl = document.querySelector('.proxy-list-body')
      if(scrollbarEl.scrollTop + scrollbarEl.clientHeight >= scrollbarEl.scrollHeight - 100) {
        getProxyNodeList(false)
      }
    }, 100)
    function initServerSelector() {
      let serverSelector = document.querySelector('.server-selector')
      serverSelector.innerHTML = `
        <div class="selector-tab">
          ${isp_list.find(item => item.value === default_isp).key}
          <i class="iconfont icon-arrow-up-bold"></i>
        </div>
        <div class="selector-content">
          ${isp_list.map(item => `<div class="option" value="${item.value}">${item.key}</div>`).join('')}
        </div>
      `
      let serverSelectorTab = serverSelector.querySelector('.selector-tab')
      let serverSelectorContent = serverSelector.querySelector('.selector-content')
      const unexpandServerSelector = (e) => {
        if(!serverSelector.contains(e.target)) {
          serverSelector.classList.add('unexpand')
          isServerSelectorExpand = false
        }
      }
      serverSelectorTab.addEventListener('click', () => {
        if(isServerSelectorExpand) {
          serverSelector.classList.add('unexpand')
          document.removeEventListener('click', unexpandServerSelector)
        } else {
          serverSelector.classList.remove('unexpand')
          document.addEventListener('click', unexpandServerSelector)
        }
        isServerSelectorExpand = !isServerSelectorExpand
      })
      serverSelectorContent.addEventListener('click', (e) => {
        console.log('e', e.target.getAttribute('value'))
        if(e.target.getAttribute('value')) {
          default_isp = e.target.getAttribute('value')
          serverSelectorTab.innerHTML = `
            ${isp_list.find(item => item.value === default_isp).key}
            <i class="iconfont icon-arrow-up-bold"></i>
          `
          serverSelectorContent.querySelectorAll('.option').forEach(item => {
            item.classList.remove('selected')
          })
          e.target.classList.add('selected')
          serverSelector.classList.add('unexpand')
          document.removeEventListener('click', unexpandServerSelector)
          isServerSelectorExpand = false
          getProxyNodeList()
        }
      })
    }
    init()
  </script>
  <style>
    .proxy-select-cpt {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 24px 24px;
      background-color: var(--nb1r);
      border-radius: 16px;
      overflow: hidden;
      .query-select-wrapper {
        border-bottom: 1px solid var(--ns3r);
        margin-bottom: 10px;
        .query-select-item {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: flex-start;
          margin-bottom: 18px;
          .query-select-title {
            width: 32px;
            flex-shrink: 0;
            font-size: 14px;
            font-weight: 400;
            color: var(--nf3r);
            margin-right: 24px;
          }
          .mode-selector {
            margin-right: 12px;
          }
          .mode-tips {
            font-size: 12px;
            color: var(--nf4r);
            line-height: 20px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            gap: 4px;
            .iconfont {
              font-size: 16px;
            }
          }
          .district-list {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;
            flex-wrap: wrap;
            gap: 8px;
            .district-item {
              padding: 0 12px;
              height: 32px;
              border-radius: 4px;
              background-color: rgba(255, 255, 255, 0.03);
              color: var(--nf1r);
              font-size: 14px;
              line-height: 32px;
              cursor: pointer;
            }
            .district-item.selected, .district-item:hover {
              background-color: var(--nbsh);
            }
          }
        }
      }
      .table-wrapper {
        margin-bottom: 12px;
      }
      .table-header {
        height: 44px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        border-bottom: 1px solid var(--ns3r);
        div {
          font-size: 14px;
          color: var(--nf3r);
          line-height: 44px;
          flex-shrink: 0;
        }
        .node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 10px;
          color: var(--nf1r);
          font-size: 14px;
          .server-selector {
            .selector-tab {
              color: var(--bf2r);
              height: 20px;
              line-height: 20px;
              font-size: 12px;
              padding: 0;
              background: none;
              .iconfont {
                font-size: 12px;
              }
            }
            .selector-content {
              width: 50px;
              top: 30px;
              text-align: center;
              .option {
                
                line-height: 20px;
              }
            }
          }
        }
        .delay {
          width: 113px;
          text-align: center;
        }
        .loss {
          width: 113px;
          text-align: center;
        }
        .status {
          width: 113px;
          text-align: center;
        }
      }
      .proxy-list-body {
        width: calc(100% + 8px);
        height: calc(100% - 44px);
        margin-right: -8px;
        overflow-y: scroll;
        .row {
          height: 44px;
          border-bottom: 1px solid var(--ns3r);
          display: flex;
          align-items: center;
          justify-content: flex-start;
        }
        .row:last-child {
          border-bottom: none;
        }
        .node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          gap: 6px;
          color: var(--nf1r);
          font-size: 14px;
          img {
            width: 53px;
            height: 16px;
          }
        }
        .delay, .loss {
          width: 113px;
          font-size: 14px;
          color: var(--nf1r);
          font-family: 'Roboto';
          line-height: 44px;
          text-align: center;
        }
        .status {
          width: 113px;
          font-size: 14px;
          color: var(--success);
          line-height: 44px;
          text-align: center;
        }
        .status.fail {
          color: var(--danger);
        }
      }
    }
  </style>
</html>
