<template>
  <div class="chat-app">
    <div class="chat-container">
      <header class="app-header">
        <h1 class="app-title">HeyBox 语音</h1>
      </header>
      
      <main class="app-main">
        <div class="chat-controls">
          <div class="control-status">
            <div class="status-indicator" :class="{ active: isActive }">
              {{ isActive ? '已连接' : '未连接' }}
            </div>
            <button class="toggle-button" @click="toggleVoice">
              {{ isActive ? '断开连接' : '连接语音' }}
            </button>
          </div>
          
          <div class="control-options" :class="{ disabled: !isActive }">
            <div class="control-option">
              <label class="option-label">麦克风</label>
              <div class="option-controls">
                <button class="mic-button" :class="{ muted: isMuted }" @click="toggleMute">
                  <div class="mic-icon">{{ isMuted ? '🔇' : '🎤' }}</div>
                  {{ isMuted ? '取消静音' : '静音' }}
                </button>
              </div>
            </div>
            
            <div class="control-option">
              <label class="option-label">音量</label>
              <div class="option-controls">
                <input 
                  type="range" 
                  class="volume-slider" 
                  min="0" 
                  max="100" 
                  :value="volume" 
                  @input="updateVolume"
                  :disabled="!isActive"
                >
                <span class="volume-value">{{ volume }}%</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="channels-list">
          <h3 class="section-title">可用频道</h3>
          <ul class="channels">
            <li 
              v-for="channel in channels" 
              :key="channel.id" 
              class="channel-item"
              :class="{ active: currentChannelId === channel.id }"
              @click="connectToChannel(channel.id)"
            >
              <div class="channel-icon">{{ channel.icon }}</div>
              <div class="channel-info">
                <div class="channel-name">{{ channel.name }}</div>
                <div class="channel-users">{{ channel.users }} 用户在线</div>
              </div>
              <div class="channel-status">
                {{ currentChannelId === channel.id ? '已连接' : '点击连接' }}
              </div>
            </li>
          </ul>
        </div>
        
        <div class="audio-devices" v-if="isActive">
          <h3 class="section-title">音频设备</h3>
          
          <div class="devices-section">
            <h4 class="device-type">麦克风</h4>
            <select class="device-select" v-model="selectedInputDevice">
              <option v-for="device in inputDevices" :key="device.id" :value="device.id">
                {{ device.name }}
              </option>
            </select>
          </div>
          
          <div class="devices-section">
            <h4 class="device-type">扬声器</h4>
            <select class="device-select" v-model="selectedOutputDevice">
              <option v-for="device in outputDevices" :key="device.id" :value="device.id">
                {{ device.name }}
              </option>
            </select>
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script>
import chat from '@heybox/rtc';
import { ref, onMounted, onUnmounted, computed, watch } from 'vue';

export default {
  name: 'App',
  setup() {
    const isActive = ref(chat.isActive);
    const isMuted = ref(chat.isMuted);
    const volume = ref(chat.volume);
    const currentChannelId = ref(chat.currentChannelId);
    const audioDevices = ref([]);
    const selectedInputDevice = ref('default');
    const selectedOutputDevice = ref('default-out');
    
    // 示例频道列表
    const channels = ref([
      {
        id: 'general',
        name: '通用聊天',
        icon: '💬',
        users: 24
      },
      {
        id: 'gaming',
        name: '游戏频道',
        icon: '🎮',
        users: 18
      },
      {
        id: 'music',
        name: '音乐频道',
        icon: '🎵',
        users: 12
      },
      {
        id: 'help',
        name: '帮助频道',
        icon: '❓',
        users: 5
      }
    ]);
    
    // 筛选输入设备
    const inputDevices = computed(() => {
      return audioDevices.value.filter(device => device.type === 'input');
    });
    
    // 筛选输出设备
    const outputDevices = computed(() => {
      return audioDevices.value.filter(device => device.type === 'output');
    });
    
    // 切换语音开关
    const toggleVoice = () => {
      chat.toggle();
      handleStatusChange();
    };
    
    // 切换静音
    const toggleMute = () => {
      chat.toggleMute();
      isMuted.value = chat.isMuted;
    };
    
    // 更新音量
    const updateVolume = (event) => {
      const newVolume = parseInt(event.target.value);
      chat.setVolume(newVolume);
      volume.value = chat.volume;
    };
    
    // 连接到频道
    const connectToChannel = (channelId) => {
      if (!isActive.value) {
        isActive.value = true;
      }
      chat.connect(channelId);
      currentChannelId.value = channelId;
    };
    
    // 状态变化处理
    const handleStatusChange = () => {
      isActive.value = chat.isActive;
      isMuted.value = chat.isMuted;
      volume.value = chat.volume;
      currentChannelId.value = chat.currentChannelId;
    };
    
    // 获取音频设备
    const loadAudioDevices = async () => {
      audioDevices.value = await chat.getAudioDevices();
    };
    
    onMounted(async () => {
      // 加载音频设备
      await loadAudioDevices();
      
      // 设置轮询检查状态变化
      const statusInterval = setInterval(handleStatusChange, 1000);
      
      onUnmounted(() => {
        // 清理轮询
        clearInterval(statusInterval);
      });
    });
    
    return {
      isActive,
      isMuted,
      volume,
      currentChannelId,
      channels,
      audioDevices,
      inputDevices,
      outputDevices,
      selectedInputDevice,
      selectedOutputDevice,
      toggleVoice,
      toggleMute,
      updateVolume,
      connectToChannel
    };
  }
};
</script>

<style>
.chat-app {
  padding: 20px;
  background-color: #f5f7fa;
  display: flex;
  justify-content: center;
  align-items: center;
}

.chat-container {
  width: 100%;
  max-width: 800px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.app-header {
  padding: 20px;
  background-color: #2b2b2b;
  color: #fff;
  text-align: center;
}

.app-title {
  font-size: 24px;
  font-weight: 500;
}

.app-main {
  padding: 30px;
}

.chat-controls {
  margin-bottom: 30px;
}

.control-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.status-indicator {
  font-size: 16px;
  padding: 6px 12px;
  border-radius: 4px;
  background-color: #f56c6c;
  color: #fff;
}

.status-indicator.active {
  background-color: #67c23a;
}

.toggle-button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #409eff;
  color: #fff;
  cursor: pointer;
  transition: background-color 0.3s;
}

.toggle-button:hover {
  background-color: #66b1ff;
}

.control-options {
  background-color: #f9f9f9;
  border-radius: 4px;
  padding: 16px;
  margin-top: 16px;
}

.control-options.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.control-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.control-option:last-child {
  margin-bottom: 0;
}

.option-label {
  font-weight: 500;
  color: #333;
}

.option-controls {
  display: flex;
  align-items: center;
}

.mic-button {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  background-color: #67c23a;
  color: #fff;
  cursor: pointer;
  transition: background-color 0.3s;
}

.mic-button.muted {
  background-color: #f56c6c;
}

.mic-icon {
  margin-right: 6px;
  font-size: 18px;
}

.volume-slider {
  width: 150px;
  margin-right: 12px;
  accent-color: #409eff;
}

.volume-value {
  min-width: 40px;
  text-align: right;
  font-weight: 500;
  color: #333;
}

.channels-list {
  margin-bottom: 30px;
}

.section-title {
  font-size: 18px;
  margin-bottom: 16px;
  color: #333;
}

.channels {
  background-color: #f9f9f9;
  border-radius: 4px;
  overflow: hidden;
}

.channel-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  border-bottom: 1px solid #eee;
}

.channel-item:last-child {
  border-bottom: none;
}

.channel-item:hover {
  background-color: #f0f0f0;
}

.channel-item.active {
  background-color: #ecf5ff;
}

.channel-icon {
  font-size: 24px;
  margin-right: 16px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.channel-info {
  flex: 1;
}

.channel-name {
  font-weight: 500;
  margin-bottom: 4px;
  color: #333;
}

.channel-users {
  font-size: 12px;
  color: #666;
}

.channel-status {
  font-size: 12px;
  color: #409eff;
}

.audio-devices {
  margin-top: 30px;
}

.devices-section {
  margin-bottom: 16px;
}

.device-type {
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
}

.device-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  color: #333;
}
</style> 