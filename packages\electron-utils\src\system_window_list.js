const { SYSTEM_WINDOW_TYPE } = require('./assets/constant')
const requireAddon = require('./require_addon')

const WINDOW_INTERVAL_TIMEOUT = 1000 * 5 // 定义窗口检查的时间间隔为5秒
let getWinInterval = null, intervalCbs = [] // 保存定时器引用和回调列表

/**
 * 注册一个定时器，用于周期性获取窗口信息
 * @param {Array|string} types - 需要监听的窗口类型
 * @param {Function} cb - 回调函数，用于接收窗口信息
 * @param {boolean} immediate - 是否立即执行回调函数
 */
function registerInterval(types, cb, immediate) {
  if (!cb || !types) return

  // 过滤掉无效的窗口类型
  types = filterWindowType(types)

  // 检查回调是否已经存在，如果存在则更新其类型
  let exitCb = intervalCbs.find((data) => data.cb === cb)
  if (!exitCb) {
    intervalCbs.push({cb, types})
  } else {
    exitCb.types = Array.from(new Set([...exitCb.types, ...types])) // 合并去重的类型数组
  }

  // 如果设置了 immediate，清除现有的定时器（用于立即重新开始）
  if (immediate) {
    clearWinListInterval()
  }

  // 如果当前没有定时器，则创建一个新的定时器
  if (!getWinInterval) {
    let func = async () => {
      let win_data = {}
      
      // 获取不同类型的窗口信息
      if (isTypeExit(SYSTEM_WINDOW_TYPE.COMMON)) {
        win_data[SYSTEM_WINDOW_TYPE.COMMON] = await getFilteredAppWindowInfo()
      }
      if (isTypeExit(SYSTEM_WINDOW_TYPE.TOP)) {
        win_data[SYSTEM_WINDOW_TYPE.TOP] = getTopWindows()
      }
      if (isTypeExit(SYSTEM_WINDOW_TYPE.FULLSCREEN)) {
        win_data[SYSTEM_WINDOW_TYPE.FULLSCREEN] = await getFullScreenWindowInfo()
      }

      // 调用所有注册的回调函数
      intervalCbs.forEach((data) => {
        if (data.types.length === 1) {
          // 如果只监听一种类型，直接传递该类型的窗口数据
          data.cb(win_data[data.types[0]] || [])
        } else {
          // 如果监听多种类型，构造包含所有类型数据的对象并传递
          let res = {}
          data.types.forEach((type) => {
            res[type] = win_data[type] || []
          })
          data.cb(res)
        }
      })
    }

    // 创建定时器，周期性调用上述逻辑
    getWinInterval = setInterval(func, WINDOW_INTERVAL_TIMEOUT)
    func() // 立即调用一次
  }
}

/**
 * 注销定时器中的某个回调函数
 * @param {Array|string} types - 要移除监听的窗口类型
 * @param {Function} cb - 要移除的回调函数
 */
function unregisterInterval(types, cb) {
  let index = intervalCbs.findIndex((data) => data.cb === cb)
  if (index >= 0) {
    intervalCbs[index].types = intervalCbs[index].types.filter((t) => {
      if (typeof types === 'string') {
        return t !== types
      } else {
        return !types.includes(t)
      }
    })
    // 如果回调不再监听任何类型，则移除该回调
    if (intervalCbs[index].types.length === 0) {
      intervalCbs.splice(index, 1)
    }
  }
}

/**
 * 获取顶部窗口信息
 * @returns {Array} 顶部窗口信息的数组，其中每个元素包含以下字段：
 * 
 * - `windowId` (Number): 窗口的唯一标识符。
 * - `processId` (Number): 与窗口关联的进程 ID。
 * - `title` (String): 窗口名。
 * - `processPath` (String): 进程的完整路径。例如："C:\\Windows\\explorer.exe"。
 * - `processName` (String): 进程的名称。例如："explorer.exe"。
 */
function getTopWindows() {
  const { getTopWindows } = require('@heybox/node-inject').addon
  return getTopWindows()
}

/**
 * 获取进程列表信息，返回的数组包含进程的名称、PID、进程路径和架构信息。
 * 每个对象包含以下字段：
 * - process_name: 进程的名称 (例如 "Feishu.exe")
 * - pid: 进程 ID (例如 15192)
 * - process_path: 进程的路径 (例如 "D:\\Feishu\\app\\Feishu.exe")
 * - arch: 进程架构
 * 
 * @returns {Array<{process_name: string, pid: number, process_path: string, arch: number}>}
 * 返回一个对象数组，每个对象表示一个进程信息。
 */
function getProcessList() {
  return new Promise((resolve, reject) => {
    requireAddon("get_endpoint_info").getProcessList((list) => {
      // log.info('[getProcessList]', list)
      resolve(list);
    })
  })
}

/**
 * 获取过滤的窗口列表信息，不包括文件夹，浏览器等系统默认进程
 * 每个对象包含以下字段：
 * - window_name: 窗口的名称 (例如 "飞书")
 * - process_name: 进程的名称 (例如 "Feishu.exe")
 * - pid: 进程 ID (例如 5960)
 * - process_path: 进程的路径 (例如 "D:\\Feishu\\app\\Feishu.exe")
 * - window_handle: 窗口的句柄 (例如 66956)
 * 
 * @returns {Array<{process_name: string, pid: number, process_path: string, window_handle: number}>}
 * 返回一个对象数组，每个对象表示一个窗口信息。
 */
function getFilteredAppWindowInfo() {
  return new Promise((resolve, reject) => {
    requireAddon("get_endpoint_info").getFilteredAppWindowInfo((list) => {
      // log.info('[getFilteredAppWindowInfo]', JSON.stringify(list))
      resolve(list);
    })
  })
}

/**
 * 获取窗口列表信息，返回的数组包含窗口的名称、PID、进程路径和窗口句柄。
 * 每个对象包含以下字段：
 * - window_name: 窗口的名称 (例如 "飞书")
 * - process_name: 进程的名称 (例如 "Feishu.exe")
 * - pid: 进程 ID (例如 5960)
 * - process_path: 进程的路径 (例如 "D:\\Feishu\\app\\Feishu.exe")
 * - window_handle: 窗口的句柄 (例如 66956)
 * 
 * @returns {Array<{process_name: string, pid: number, process_path: string, window_handle: number}>}
 * 返回一个对象数组，每个对象表示一个窗口信息。
 */
function getAppWindowInfo() {
  return new Promise((resolve, reject) => {
    requireAddon("get_endpoint_info").getAppWindowInfo((list) => {
      resolve(list);
    })
  })
}

/**
 * 获取聚焦的全屏窗口列表信息。
 * 每个对象包含以下字段：
 * - window_text: 窗口的名称 (例如 "飞书")
 * - process_name: 进程的名称 (例如 "Feishu.exe")
 * - pid: 进程 ID (例如 5960)
 * - process_path: 进程的路径 (例如 "D:\\Feishu\\app\\Feishu.exe")
 * 返回一个对象数组，每个对象表示一个窗口信息。
 */
function getFullScreenWindowInfo() {
  return new Promise((resolve, reject) => {
    requireAddon("get_endpoint_info").checkFullScreenWindow((list) => {
      resolve(list);
    })
  })
}


/**
 * 清除当前的定时器
 * 停止周期性获取窗口信息
 */
function clearWinListInterval() {
  if (getWinInterval) {
    clearInterval(getWinInterval)
    getWinInterval = null
  }
}

/**
 * 过滤有效的窗口类型
 * @param {Array|string} types - 输入的窗口类型
 * @returns {Array} 过滤后的有效窗口类型
 */
function filterWindowType(types) {
  let window_types = Object.values(SYSTEM_WINDOW_TYPE)
  if (typeof types === 'string') {
    if (window_types.includes(types)) {
      return [types]
    } else {
      return []
    }
  } else {
    return types.filter(t => window_types.includes(t))
  }
}

/**
 * 检查是否有回调函数监听指定的窗口类型
 * @param {string} type - 窗口类型
 * @returns {boolean} 是否存在监听该类型的回调
 */
function isTypeExit(type) {
  return intervalCbs.some(({types}) => types.includes(type))
}

module.exports = {
  registerInterval, // 注册窗口监听定时器
  unregisterInterval, // 注销窗口监听定时器
  getTopWindows, // 获取顶部窗口信息
  getFilteredAppWindowInfo, // 获取过滤后的窗口信息
  getFullScreenWindowInfo,
  getProcessList,
  getAppWindowInfo,

  SYSTEM_WINDOW_TYPE // 窗口类型常量
}
