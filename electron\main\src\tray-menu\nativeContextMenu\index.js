const { app, Menu, nativeImage, ipcMain, screen } = require('electron')
const path = require('path')
const utils = require('@heybox/electron-utils')
class NativeContextMenu {
  constructor(tray) {
    this.tray = tray
    this.menu = null
    this.setupIpcHandlers();
  }
  setupIpcHandlers() {
    ipcMain.on('set-tray-context-menu-data', (e, data) => {
      this.setMenuData(data)
    })
  }
  setMenuData (data) {
    this.menu = this.buildMenu()
    this.tray.popUpContextMenu(this.menu)
  }
  buildMenu() {

    const template = [
      {
        label: '显示主菜单',
        click: () => { 
          this.handleMenuClick({ menu: { key: 'show_main_window' } })
        }
      },
      {
        label: '退出',
        click: () => { 
          this.handleMenuClick({ menu: { key: 'exit_app' } })
        }
      }
    ]

    return Menu.buildFromTemplate(template)
  }
  handleMenuClick ({ menu }) {
    // 根据主菜单的逻辑处理
    if (menu.key === 'show_main_window') {
      if (!mainWindow) {
        return
      }
      if (mainWindow.isMinimized()) {
        mainWindow.restore()
      }
      utils.focusWindow(mainWindow)
    } else if (menu.key === 'exit_app') {
      if(global.kmEvent) {
        global.kmEvent.stopWatch()
        app.exit()
      }
    }
  }
}

module.exports = NativeContextMenu