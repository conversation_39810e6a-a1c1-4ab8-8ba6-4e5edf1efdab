# @heybox-app-web-shared/eventbus

基于 [mitt](https://github.com/developit/mitt) 的Vue3事件总线封装包。

## 特性

- ✅ 基于 mitt 的轻量级事件总线
- ✅ Vue3 组合式API 支持
- ✅ 自动清理事件监听（防止内存泄漏）

## 安装

```bash
yarn add @heybox-app-web-shared/eventbus
```

## 基本使用

### 1. 在Vue组件中使用（推荐）

```javascript
import { useEventBus } from '@heybox-app-web-shared/eventbus'

export default {
  setup() {
    const { emit, on, off, once, cleanup } = useEventBus()

    // 发射事件
    const handleClick = () => {
      emit('user:click', { userId: 123 })
    }

    // 监听事件（组件卸载时自动清理）
    on('user:login', (userData) => {
      console.log('用户登录:', userData)
    })

    // 一次性监听
    once('app:ready', () => {
      console.log('应用准备就绪')
    })

    return {
      handleClick
    }
  }
}
```

### 2. 在普通JavaScript中使用

```javascript
import { eventBus } from '@heybox-app-web-shared/eventbus'

// 发射事件
eventBus.emit('notification:show', {
  type: 'success',
  message: '操作成功'
})

// 监听事件
const handleNotification = (data) => {
  console.log('收到通知:', data)
}

eventBus.on('notification:show', handleNotification)

// 移除监听
eventBus.off('notification:show', handleNotification)
```

## API 参考

### useEventBus()

Vue组合式API，提供自动清理功能。

```javascript
const {
  emit,      // 发射事件
  on,        // 监听事件（自动清理）
  off,       // 移除事件监听
  once,      // 一次性监听
  cleanup,   // 手动清理当前组件的所有监听
  eventBus   // 访问原始事件总线实例
} = useEventBus()
```

#### 参数

- `on(type, handler, autoCleanup = true)` - 第三个参数控制是否自动清理
- `off(type, handler)` - 移除指定的事件监听器
- `once(type, handler)` - 监听一次性事件
- `emit(type, data)` - 发射事件
- `cleanup()` - 手动清理当前组件的所有事件监听

### eventBus

全局事件总线实例。

#### 方法

- `emit(type, data)` - 发射事件
- `on(type, handler)` - 监听事件
- `off(type, handler)` - 移除事件监听
- `once(type, handler)` - 一次性事件监听
- `clear()` - 清除所有事件监听
- `getAllEvents()` - 获取所有已注册事件类型
- `getListenerCount(type)` - 获取指定事件的监听器数量

### EventBus 类

如果需要创建独立的事件总线实例：

```javascript
import { EventBus } from '@heybox-app-web-shared/eventbus'

const myEventBus = new EventBus()
```

## 事件命名规范

推荐使用命名空间的方式组织事件：

```javascript
// 用户相关事件
emit('user:login', userData)
emit('user:logout')

// UI相关事件
emit('ui:modal:open', modalData)
emit('ui:notification:show', notificationData)

// 系统级事件
emit('SYSTEM_ERROR', errorData)  // 全大写用于重要事件
```

## 最佳实践

### 1. 在组件中使用自动清理

```javascript
import { useEventBus } from '@heybox-app-web-shared/eventbus'

export default {
  setup() {
    const { emit, on } = useEventBus()

    // 自动清理，组件卸载时自动移除监听
    on('data:update', handleDataUpdate)

    // 如果不需要自动清理
    on('global:event', handleGlobalEvent, false)

    return {}
  }
}
```

### 2. 在外部模块中手动管理

```javascript
import { eventBus } from '@heybox-app-web-shared/eventbus'

class DataManager {
  constructor() {
    this.handler = () => {}
    this.init()
  }

  init() {
    eventBus.on('data:change', this.handler)
  }

  destroy() {
    eventBus.off('data:change', this.handler)
  }
}
```

## 注意事项

1. 在Vue组件中推荐使用 `useEventBus()`，它会自动清理事件监听
2. 在非Vue环境中使用 `eventBus` 实例，记得手动清理事件监听
3. 避免在事件处理函数中发射同名事件，防止无限循环
4. 合理使用事件命名空间，保持代码组织清晰