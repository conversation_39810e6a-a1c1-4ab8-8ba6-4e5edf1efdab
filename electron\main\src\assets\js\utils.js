const path = require('path');

function getWebUrl(type) {
  const {
    MAIN_LOCAL_URL,
    CHAT_LOCAL_URL,
    LIBRARY_LOCAL_URL,
    POPUP_LOCAL_URL,
  } = require('../constant/global');
  if (ELECTRON_ENV.includes('local') && (LOCAL_WEB_PATHS.includes(type) || LOCAL_WEB_PATHS.length === 0)) {
    if (type === 'main') {
      return MAIN_LOCAL_URL;
    } else if (type === 'chat') {
      return CHAT_LOCAL_URL;
    } else if (type === 'library') {
      return LIBRARY_LOCAL_URL
    } else if (type === 'popup') {
      return POPUP_LOCAL_URL
    }
  } else {
    return path.join(appDir, `../webapp/${type}/index.html`);
  }
}

function updateWebContentsUa(webContent) {
  let origin_userAgent = webContent.getUserAgent()
  let new_userAgent = 
    origin_userAgent + 
    ` BASE_API/${encodeURIComponent(global.BASE_API)}` + 
    ` BASE_XHH/${encodeURIComponent(global.BASE_HEYBOX_API)}` + 
    ` BASE_ACC/${encodeURIComponent(global.BASE_HEYACC_API)}` +
    (global.ENV_TAG ? ` ${global.ENV_TAG}` : '')
  if (global.CLIENT_VERSION) {
    new_userAgent += ` EXE_VERSION/${global.CLIENT_VERSION}`
  }
  if (global.ELECTRON_VERSION) {
    new_userAgent += ` ELECTRON_VERSION/${global.ELECTRON_VERSION}`
  }
  webContent.setUserAgent(new_userAgent)
}
  
module.exports = {
  getWebUrl,
  updateWebContentsUa,
}
