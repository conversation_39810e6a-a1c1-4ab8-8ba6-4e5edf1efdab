<template>
  <div v-if="$slots.default">
    <component 
      :is="$slots.default[0]" 
      v-bind="$slots.default[0].props || {}"
      @mouseenter="handleMouseEnter"
      @mouseleave="handleMouseLeave"
    />
  </div>
</template>

<script setup>
import { ref, onBeforeUnmount, nextTick, watch } from 'vue';
import { createApp, h } from 'vue';
import DirectiveTooltip from './DirectiveTooltip.vue';

// Props
const props = defineProps({
  placement: {
    type: String,
    default: 'top'
  },
  pointer: {
    type: String,
    default: 'center'
  },
  popperClass: {
    type: String,
    default: ''
  },
  manual: {
    type: Boolean,
    default: false
  },
  show: {
    type: Boolean,
    default: false
  },
  disable: {
    type: <PERSON>olean,
    default: false,
  },
  maxWidth: {
    type: Number,
    default: null
  },
  offset: {
    type: Object,
    default: () => ({ x: 0, y: 0 })
  }
});

// Refs
const tooltipInstance = ref(null);
const mountEl = ref(null);

// Methods
const createTooltip = (event) => {
  if (tooltipInstance.value) {
    destroyTooltip()
  }
  
  const rect = event.target.getBoundingClientRect()
  const targetRect = {
    x: rect.x,
    y: rect.y,
    width: rect.width,
    height: rect.height,
    top: rect.top,
    right: rect.right,
    bottom: rect.bottom,
    left: rect.left
  };
  
  const app = createApp({
    render() {
      return h(DirectiveTooltip, {
        show: true,
        targetRect: targetRect,
        placement: props.placement,
        pointer: props.pointer,
        popperClass: props.popperClass,
        maxWidth: props.maxWidth,
        offset: props.offset
      })
    }
  });

  mountEl.value = document.createElement('div')
  tooltipInstance.value = app.mount(mountEl.value)
  document.body.appendChild(mountEl.value)
};

const destroyTooltip = () => {
  if (tooltipInstance.value && mountEl.value) {
    mountEl.value.remove()
    tooltipInstance.value = null
    mountEl.value = null
  }
};

const handleMouseEnter = (event) => {
  if (props.disable || props.manual) return
  createTooltip(event)
};

const handleMouseLeave = () => {
  if (!props.manual) {
    destroyTooltip()
  }
};

// Lifecycle
onBeforeUnmount(() => {
  destroyTooltip()
});

// Watchers
watch(() => props.show, (newVal) => {
  if (props.manual) {
    if (newVal) {
      if (props.disable) return
      nextTick(() => {
        createTooltip({target: document.querySelector('[data-tooltip-target]')})
      })
    } else {
      destroyTooltip()
    }
  }
}, { immediate: true });

watch(() => props.manual, (newVal) => {
  if (!newVal) {
    destroyTooltip()
  }
});
</script>
