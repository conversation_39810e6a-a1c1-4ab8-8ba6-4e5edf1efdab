const { ipcMain } = require('electron')
const { requireAddon } = require('@heybox/electron-utils')
const { log } = require('@heybox/electron-utils')
let GAME_HELPRT_ADDON

class Continuous_Key_Press {
  constructor() {
    this.startId = null
    ipcMain.handle('continue-keypress:stop', this.forceStop.bind(this))
    global.kmEvent.addNodeShortcut({
      key_config: 'continuous_key_list',
      km_list: true,
    }, this.switchState.bind(this))
  }
  switchState (state) {
    let {executed_key, trigger_key, mode, interval, random_interval, key} = state
    if (mode === 0) { // 长按
      random_interval = 0
    }
    let id = key
    if (this.startId === id) {
      log.info('game_helper:Stop', JSON.stringify(state))
      addonRequire().Stop()
      this.startId = null
    } else {
      log.info('game_helper:SetKeyboard', JSON.stringify(state))
      this.startId = key
      addonRequire().SetKeyboard(executed_key[0], interval, 1, mode, random_interval) // executed_key[0] 目前仅支持单个按键
    }
  }
  forceStop(_, key) {
    if (key) {
      if (this.startId) {
        let id = key
        if (this.startId === id) {
          log.info('game_helper:forceStop', JSON.stringify(state))
          addonRequire().Stop()
          this.startId = null
        }
      }
    } else {
      log.info('game_helper:forceStopAll', state)
      addonRequire().Stop()
    }
  }
}

function addonRequire() {
  return requireAddon("game_helper")
}

module.exports = new Continuous_Key_Press()