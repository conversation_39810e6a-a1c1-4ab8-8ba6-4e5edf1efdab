const requireAddon= require('./require_addon')
const log = require('./log')

function getHardwareInfo() {
  return new Promise((resolve, reject) => {
    log.info('[require hardware_info]')
    const HARDWARE_HELPER_ADD_ON = requireAddon("acc_hardware_helper");
  
    if (HARDWARE_HELPER_ADD_ON.getHardwareInfo) {
      HARDWARE_HELPER_ADD_ON.getHardwareInfo((result) => {
        resolve(result)
      });
    } else {
      reject(new Error('no getHardwareInfo function'))
    }
  })
}

function showHardwareInfo(cb, is_cpu_temp_show) {
  global.HARDWARE_HELPER_ADD_ON = requireAddon("acc_hardware_helper");
  global.HARDWARE_HELPER_ADD_ON.startSystemResourceMonitor((res) => {
    cb && cb(res)
  }, is_cpu_temp_show);
}

function stopShowHardwareInfo() {
  if(!global.HARDWARE_HELPER_ADD_ON) return
  global.HARDWARE_HELPER_ADD_ON.stopSystemResourceMonitor()
}

module.exports = {
  getHardwareInfo,
  showHardwareInfo,
  stopShowHardwareInfo
}