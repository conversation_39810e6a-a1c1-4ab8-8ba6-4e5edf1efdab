const fs = require('fs');
const path = require('path');

// 解析命令行参数
const args = process.argv.slice(2);
const mode = args.find(arg => arg === 'web');
const pathArgs = args
  .filter(arg => arg.startsWith('--path='))
  .map(arg => arg.split('=')[1]);

if (!mode || !['web'].includes(mode)) {
  process.exit(1);
}

console.log('Copy web dist mode:', mode, 'paths:', pathArgs);

// 读取electron/main/package.json
const electronPackageJsonPath = path.join(__dirname, '../electron/main/package.json');
let electronPackageJson;

try {
  electronPackageJson = JSON.parse(fs.readFileSync(electronPackageJsonPath, 'utf8'));
} catch (error) {
  console.error('无法读取electron/main/package.json:', error.message);
  process.exit(1);
}

// 递归复制目录
function copyDirectory(sourceDir, targetDir) {
  try {
    // 确保目标目录存在
    fs.mkdirSync(targetDir, { recursive: true });
    
    const items = fs.readdirSync(sourceDir, { withFileTypes: true });
    
    for (const item of items) {
      const sourcePath = path.join(sourceDir, item.name);
      const targetPath = path.join(targetDir, item.name);
      
      if (item.isDirectory()) {
        copyDirectory(sourcePath, targetPath);
      } else {
        fs.copyFileSync(sourcePath, targetPath);
      }
    }
    
    console.log(`Copied directory: ${sourceDir} -> ${targetDir}`);
    return true;
  } catch (error) {
    console.error(`Error copying directory: ${sourceDir} -> ${targetDir}`, error.message);
    return false;
  }
}

// 处理build:web模式
async function handleBuildWeb() {
  if (pathArgs.length > 0) {
    // 有--path参数，复制指定的项目
    console.log('Processing specified web projects:', pathArgs);
    
    for (const pathArg of pathArgs) {
      try {
        // 通过require.resolve获取包的dist文件夹路径
        const packageName = `@heybox-app-web/${pathArg}`;
        const sourceDistDir = path.dirname(require.resolve(`${packageName}`));
        const targetDir = path.join(__dirname, '../electron/main/webapp', pathArg);
        
        if (fs.existsSync(sourceDistDir)) {
          copyDirectory(sourceDistDir, targetDir);
        } else {
          console.warn(`Source dist directory not found: ${sourceDistDir}`);
        }
      } catch (error) {
        console.warn(`Package @heybox-app-web/${pathArg} dist not found:`, error.message);
      }
    }
  } else {
    // 没有--path参数，检测devDependencies中的@heybox-app-web/*依赖
    console.log('Processing all @heybox-app-web/* dependencies');
    
    const devDependencies = electronPackageJson.devDependencies || {};
    const webPackages = Object.keys(devDependencies).filter(packageName => 
      packageName.startsWith('@heybox-app-web/')
    );
    
    console.log('Found web packages:', webPackages);
    
    for (const packageName of webPackages) {
      try {
        // 通过require.resolve获取包的dist文件夹路径
        const sourceDistDir = path.dirname(require.resolve(`${packageName}`));
        
        // 从包名中提取项目名称，例如 @heybox-app-web/main -> main
        const projectName = packageName.replace('@heybox-app-web/', '');
        const targetDir = path.join(__dirname, '../electron/main/webapp', projectName);
        
        if (fs.existsSync(sourceDistDir)) {
          copyDirectory(sourceDistDir, targetDir);
        } else {
          console.warn(`Source dist directory not found for ${packageName}: ${sourceDistDir}`);
        }
      } catch (error) {
        console.warn(`Package ${packageName} dist not found:`, error.message);
      }
    }
  }
}

// 主执行函数
async function main() {
  try {
    if (mode === 'web') {
      await handleBuildWeb();
    }
    
    console.log('✅ Copy web dist completed successfully');
  } catch (error) {
    console.error('❌ Copy web dist failed:', error.message);
    process.exit(1);
  }
}

main();
