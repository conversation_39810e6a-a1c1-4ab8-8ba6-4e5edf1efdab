const crypto = require('crypto');

module.exports = {
  // 加密函数
  // key生成方式 crypto.randomBytes(32).toString('hex')
  encrypt: function (text, key) {
    const algorithm = 'aes-256-cbc'; // 加密算法
    const iv = crypto.randomBytes(16); // 生成16字节的随机初始化向量

    // 创建加密器
    const cipher = crypto.createCipheriv(algorithm, Buffer.from(key, 'hex'), iv);

    let encrypted = cipher.update(text, 'utf8', 'hex'); // 加密
    encrypted += cipher.final('hex'); // 完成加密

    // 将iv和加密的内容拼接在一起返回
    return iv.toString('hex') + ':' + encrypted;
  },

  // 解密函数
  decrypt: function (encryptedText, key) {
    try {
      const algorithm = 'aes-256-cbc'; // 加密算法
      const textParts = encryptedText.split(':');
      if (textParts.length < 2) {
        return encryptedText
      }
      const iv = Buffer.from(textParts[0], 'hex'); // 获取加密时用的iv
      const encrypted = textParts[1]; // 获取加密的内容

      // 创建解密器
      const decipher = crypto.createDecipheriv(algorithm, Buffer.from(key, 'hex'), iv);

      let decrypted = decipher.update(encrypted, 'hex', 'utf8'); // 解密
      decrypted += decipher.final('utf8'); // 完成解密

      return decrypted;
    } catch (e) {
      console.error(e)
      return encryptedText
    }

  }
}