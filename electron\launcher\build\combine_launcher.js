const fs = require('fs-extra');
const path = require('path');
const archiver = require('archiver');
const { execSync } = require('child_process');

console.log('Starting combine_launcher.js script');

// 解析命令行参数
const args = process.argv.slice(2);
const noOpenFolder = args.includes('--no-open');
const generateZip = args.includes('--zip');
const generateVersionZip = args.includes('--version-zip');

console.log('Command line arguments:', {
    noOpenFolder,
    generateZip,
    generateVersionZip
});

// 获取版本号和环境变量
const mainPackageJson = require('../../main/package.json');
const launcherEnv = require('../env.js');
const version = mainPackageJson.version;

console.log('Environment information:', {
    version,
    ELECTRON_ENV: launcherEnv.ELECTRON_ENV,
    NODE_BIT: launcherEnv.NODE_BIT
});

// 定义路径
const launcherOutPath = path.join(__dirname, '../../launcher/out');
const mainOutPath = path.join(__dirname, '../../main/out');

console.log('Path information:', {
    launcherOutPath,
    mainOutPath
});

// 构建输出目录名称
const buildFolderName = `HeyboxApp_${launcherEnv.ELECTRON_ENV || 'prod'}_${launcherEnv.NODE_BIT}_${version}`;
console.log('Build folder name:', buildFolderName);

// 查找构建文件夹
function findBuildFolder(basePath) {
    const folderPath = path.join(basePath, buildFolderName);
    console.log(`Checking build folder: ${folderPath}`);
    if (!fs.existsSync(folderPath)) {
        throw new Error(`Build folder not found: ${folderPath}`);
    }
    console.log(`Found build folder: ${folderPath}`);
    return buildFolderName;
}

// 创建zip文件
async function createZip(sourcePath, zipPath) {
    console.log(`Starting to create zip file: ${zipPath}`);
    console.log(`Source folder: ${sourcePath}`);
    
    return new Promise((resolve, reject) => {
        const output = fs.createWriteStream(zipPath);
        const archive = archiver('zip', {
            zlib: { level: 9 }
        });

        output.on('close', () => {
            console.log(`Zip file created successfully: ${zipPath}`);
            console.log(`Zip file size: ${(archive.pointer() / 1024 / 1024).toFixed(2)} MB`);
            resolve();
        });

        archive.on('error', (err) => {
            console.error('Error creating zip file:', err);
            reject(err);
        });

        archive.pipe(output);
        archive.directory(sourcePath, false);
        archive.finalize();
    });
}

async function main() {
    try {
        console.log('Starting main task...');

        // 获取构建文件夹
        console.log('Looking for launcher build folder...');
        const launcherBuildFolder = findBuildFolder(launcherOutPath);
        console.log('Looking for main build folder...');
        const mainBuildFolder = findBuildFolder(mainOutPath);

        // 构建完整路径
        const launcherResourcesPath = path.join(launcherOutPath, launcherBuildFolder, 'win-unpacked', 'resources');
        const mainResourcesPath = path.join(mainOutPath, mainBuildFolder, 'win-unpacked', 'resources');
        const versionFolderPath = path.join(launcherResourcesPath, 'versions', version);

        console.log('Path information:', {
            launcherResourcesPath,
            mainResourcesPath,
            versionFolderPath
        });

        // 创建版本文件夹并复制resources文件夹内容
        console.log(`Creating version folder: ${versionFolderPath}`);
        await fs.ensureDir(versionFolderPath);
        console.log(`Copying resources folder contents from ${mainResourcesPath} to ${versionFolderPath}`);
        await fs.copy(mainResourcesPath, versionFolderPath);
        console.log('Resources folder contents copy completed');

        // 生成zip文件
        if (generateZip) {
            const zipPath = path.join(launcherOutPath, launcherBuildFolder, `${launcherBuildFolder}.zip`);
            console.log(`Preparing to create full zip file: ${zipPath}`);
            await createZip(
                path.join(launcherOutPath, launcherBuildFolder, 'win-unpacked'),
                zipPath
            );
        }

        // 生成version zip文件
        if (generateVersionZip) {
            const versionZipPath = path.join(launcherOutPath, launcherBuildFolder, `${version}.zip`);
            console.log(`Preparing to create version zip file: ${versionZipPath}`);
            // 创建临时目录
            const tempDir = path.join(launcherOutPath, launcherBuildFolder, 'temp_version');
            await fs.ensureDir(tempDir);
            // 复制版本文件夹到临时目录
            await fs.copy(versionFolderPath, path.join(tempDir, version));
            // 创建zip
            await createZip(tempDir, versionZipPath);
            // 清理临时目录
            await fs.remove(tempDir);
        }

        // 打开文件夹
        if (!noOpenFolder) {
            const folderPath = path.join(launcherOutPath, launcherBuildFolder);
            console.log(`Attempting to open folder: ${folderPath}`);
            try {
                execSync(`explorer "${folderPath}"`);
                console.log('Folder opened successfully');
            } catch (error) {
                // console.log('Failed to open folder, but continuing with other operations');
            }
        }

        console.log('All operations completed successfully');
    } catch (error) {
        console.error('Error during execution:', error);
        process.exit(1);
    }
}

main();
