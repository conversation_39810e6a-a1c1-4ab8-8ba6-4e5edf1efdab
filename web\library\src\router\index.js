import { createRouter, createWebHistory } from 'vue-router';

const Home = () => import('@/views/Home/index.vue');
const Detail = () => import('@/views/Detail/index.vue');

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/app/home'
    },
    {
      path: '/app',
      children: [
        {
          path: 'home',
          component: Home,
        },
        {
          path: 'detail/:appid',
          component: Detail,
        },
      ],
    },
  ],
});

export default router;
