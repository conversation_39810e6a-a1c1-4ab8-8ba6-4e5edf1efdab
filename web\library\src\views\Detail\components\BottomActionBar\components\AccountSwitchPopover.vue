<template>
  <Popover
    :triggerRef="triggerRef"
    :triggerType="'click'"
    :hideDelay="0"
    :showDelay="0"
    :clickHidden="true"
    placement="top-end"
    :offset="{ x: -1, y: 4 }"
    @updateShow="handleUpdateShow"
  >
    <div class="account-switch-popover">
      <div class="account-list">
        <div
          v-for="account in accountOptions"
          :key="account.id"
          class="account-option"
          :class="{ 'active-account': account.active }"
          @click="handleSelectAccount(account)"
        >
          <div v-if="account.active" class="check-icon">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <circle cx="8" cy="8" r="8" fill="#14191E"/>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M11.8047 5.52859C12.0651 5.78894 12.0651 6.21106 11.8047 6.47141L7.80474 10.4714C7.54439 10.7318 7.12227 10.7318 6.86193 10.4714L4.86193 8.47141C4.60158 8.21106 4.60158 7.78894 4.86193 7.52859C5.12228 7.26824 5.5444 7.26824 5.80474 7.52859L7.33333 9.05719L10.8619 5.52859C11.1223 5.26824 11.5444 5.26824 11.8047 5.52859Z" fill="white"/>
            </svg>
          </div>
          <div class="actions-button-main">
            <div class="multiple-img">
              <img
                class="account-avatar"
                :src="account.avatar"
                alt=""
              />
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M15.1696 8.00078C15.1696 11.9772 11.946 15.2008 7.9696 15.2008C4.65161 15.2008 1.85781 12.9564 1.02355 9.90301L3.82413 11.1277C4.01894 12.0169 4.78794 12.6808 5.70691 12.6808C6.7678 12.6808 7.62882 11.796 7.6373 10.7012L10.1469 8.8266L10.1669 8.82668C11.6375 8.82668 12.8296 7.59414 12.8296 6.07374C12.8296 4.55333 11.6375 3.32079 10.1669 3.32079C8.69635 3.32079 7.50423 4.55333 7.50423 6.07374C7.50423 6.10524 7.50474 6.13662 7.50575 6.16786L5.83094 8.69308C5.78993 8.69039 5.74858 8.68903 5.70691 8.68903C5.33541 8.68903 4.98842 8.79752 4.69397 8.98554L0.800049 7.33414C1.13642 3.67021 4.21791 0.800781 7.9696 0.800781C11.946 0.800781 15.1696 4.02433 15.1696 8.00078ZM11.8976 6.07369C11.8976 7.06196 11.1227 7.86311 10.1669 7.86311C9.21099 7.86311 8.43611 7.06196 8.43611 6.07369C8.43611 5.08543 9.21099 4.28428 10.1669 4.28428C11.1227 4.28428 11.8976 5.08543 11.8976 6.07369ZM5.70694 12.2679C6.55251 12.2679 7.23798 11.5592 7.23798 10.6849C7.23798 9.8107 6.55251 9.10199 5.70694 9.10199C5.52485 9.10199 5.35019 9.13486 5.1882 9.19516L6.14673 9.60167C6.55259 9.78304 6.83858 10.1995 6.83858 10.6849C6.83858 11.3311 6.33192 11.8549 5.70694 11.8549C5.60301 11.8549 5.50236 11.8405 5.40677 11.8133L5.35598 11.7976C5.29487 11.777 5.23605 11.7512 5.18001 11.7206L4.31348 11.3417C4.5548 11.8881 5.08798 12.2679 5.70694 12.2679ZM8.83561 6.07374C8.83561 6.83394 9.43167 7.45021 10.167 7.45021C10.9022 7.45021 11.4983 6.83394 11.4983 6.07374C11.4983 5.31353 10.9022 4.69727 10.167 4.69727C9.43167 4.69727 8.83561 5.31353 8.83561 6.07374Z" fill="#8C9196"/>
                </svg>
            </div>
            <div class="actions-button-text" :class="{ 'active': account.active }">{{ account.nickname }}</div>
          </div>
          <div class="account-time">
            <svg
              v-if="!account.owned"
              v-tooltip="{ text: '不在库中', placement: 'top' }"
              xmlns="http://www.w3.org/2000/svg"
              width="12"
              height="12"
              viewBox="0 0 12 12"
              fill="none"
              class="warning-icon"
            >
              <g clip-path="url(#clip0_2419_127138)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M2.11091 2.11091C3.10568 1.11616 4.48137 0.5 6 0.5C7.51863 0.5 8.89432 1.11616 9.88908 2.11091C10.8838 3.10568 11.5 4.48136 11.5 6C11.5 7.51864 10.8838 8.89432 9.88908 9.88908C8.89432 10.8838 7.51864 11.5 6 11.5C4.48136 11.5 3.10568 10.8838 2.11091 9.88908C1.11616 8.89432 0.5 7.51863 0.5 6C0.5 4.48137 1.11616 3.10568 2.11091 2.11091ZM6 1.5C4.75723 1.5 3.63292 2.00313 2.81802 2.81802C2.00313 3.63292 1.5 4.75723 1.5 6C1.5 7.24277 2.00313 8.36708 2.81802 9.18197C3.63292 9.99686 4.75724 10.5 6 10.5C7.24276 10.5 8.36708 9.99686 9.18197 9.18197C9.99686 8.36708 10.5 7.24276 10.5 6C10.5 4.75724 9.99686 3.63292 9.18197 2.81802C8.36708 2.00313 7.24277 1.5 6 1.5Z" fill="#F67010"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6 9.25C6.34517 9.25 6.625 8.97017 6.625 8.625C6.625 8.27983 6.34517 8 6 8C5.65483 8 5.375 8.27983 5.375 8.625C5.375 8.97017 5.65483 9.25 6 9.25Z" fill="#F67010"/>
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6 2.5C6.27614 2.5 6.5 2.72386 6.5 3V7C6.5 7.27614 6.27614 7.5 6 7.5C5.72386 7.5 5.5 7.27614 5.5 7V3C5.5 2.72386 5.72386 2.5 6 2.5Z" fill="#F67010"/>
              </g>
              <defs>
                <clipPath id="clip0_2419_127138">
                  <rect width="12" height="12" fill="white"/>
                </clipPath>
              </defs>
            </svg>
            {{ formatPlaytime(account.playtime_forever || 0) }}h
          </div>
        </div>
      </div>
      <div v-if="accounts && accounts.length > 0" class="popover-divider"></div>
      <div class="popover-footer">
        <div class="add-account-section" @click="handleAddLocalFile">
          <div class="add-account-text">
            <div class="add-title">
              <i class="iconfont icon-common-add-line"></i>
              添加本地文件.exe
            </div>
            <div class="add-subtitle">或者将文件拖到这里</div>
          </div>
        </div>
        <input
          ref="fileInputRef"
          type="file"
          accept=".exe"
          style="display: none"
          @change="handleFileSelect"
        />
      </div>
    </div>
  </Popover>
</template>

<script setup name="AccountSwitchPopover">
import { defineProps, defineEmits } from 'vue';
import { useAccountSwitch } from '../composables/useAccountSwitch.js';
import { formatPlaytime } from '@heybox-app-web-shared/utils';

const props = defineProps({
  triggerRef: {
    type: Object,
    required: true,
  },
  accounts: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits(['selectAccount', 'updateShow', 'addLocalFile']);

const {
  fileInputRef,
  accountOptions,
  handleSelectAccount,
  handleUpdateShow,
  handleAddLocalFile,
  handleFileSelect,
} = useAccountSwitch(emit, props.accounts);
</script>

<style lang="scss">
.account-switch-popover {
  width: 234px;
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
  box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.10);
  background: var(---general-color-primary-0, #FFF);
  padding: 8px 0;


  .popover-header {
    margin-bottom: 12px;

    .popover-title {
      font-size: 14px;
      font-weight: 500;
      color: #8C9196;
    }
  }

  .account-list {

    .account-option {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 4px 8px;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.2s ease;
      margin-right: 8px;
      margin-left: 8px;

      &:hover {
        background: linear-gradient(46deg, var(---greadient-color-primary-left, #464B50) -0.9%, var(---greadient-color-primary-right, #14191E) 100.9%);

        .actions-button-text {
          color: #FFFFFF !important;
        }

        .check-icon svg circle {
          fill: #FFFFFF !important;
        }

        .check-icon svg path {
          fill: #14191E !important;
        }

        .steam-icon svg path {
          fill: #FFFFFF !important;
        }

        .multiple-img svg path {
          fill: #FFFFFF !important;
        }
      }

      .actions-button-main {
        display: flex;
        align-items: center;
        flex: 1;

        .multiple-img {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-left: 24px;

          .account-avatar {
            width: 24px;
            height: 24px;
            border-radius: 5px;
            object-fit: cover;
          }

        }

        .actions-button-text {
          color: $general-color-text-2;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 90px;

          &.active {
            color: var(---general-color-primary-100, #14191E);
            font-weight: 500;
          }
        }
      }

      // 选中状态下的 multiple-img 中的 SVG 颜色加深
      &.active-account .multiple-img svg path {
        fill: #14191E;
      }

      .check-icon {
        position: absolute;
        width: 16px;
        height: 16px;
        flex-shrink: 0;
      }

      .steam-icon {
        width: 16px;
        height: 16px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .account-time {
        color: $general-color-text-3;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
        display: flex;
        align-items: center;
        gap: 2px;

        .warning-icon {
          width: 12px;
          height: 12px;
          aspect-ratio: 1/1;
          flex-shrink: 0;
          cursor: pointer;
        }
      }
    }
  }

  .popover-divider {
    display: flex;
    min-width: 160px;
    max-width: 348px;
    padding: 7.5px 0;
    align-items: center;
    justify-content: center;

    &::after {
      content: '';
      width: 100%;
      height: 1px;
      background-color: var(---general-color-stroke-1, #F1F2F3);
    }
  }

  .popover-footer {
    padding: 8px 16px;

    .add-account-section {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 16px 24px;
      border: 1px dashed var(---general-color-text-4, #C8CDD2);
      border-radius: 8px;
      cursor: pointer;
      transition: border-color 0.2s ease;

      &:hover {
        border-color: #8C9196;
      }

      .add-account-text {
        text-align: center;

        .add-title {
          font-family: "PingFang SC";
          font-size: 14px;
          font-weight: 500;
          color: #111111;
          margin-bottom: 2px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;

          .iconfont {
            font-size: 16px;
          }
        }

        .add-subtitle {
          font-size: 12px;
          color: var(---general-color-text-3, #8C9196);
          line-height: 16px;
          font-weight: 400;
          font-family: "PingFang SC";
        }
      }
    }
  }
}
</style>
