<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <title>小黑盒加速器插件</title>
    <link rel="stylesheet" href="../assets/css/common.css">
    <link rel="stylesheet" href="../assets/css/theme.css">
    <link rel="stylesheet" href="../assets/font/iconfont.css">
    <link rel="stylesheet" href="../assets/font/font.css">
  </head>
  <body>
    <div class="shading"></div>
    <div class="coupon-cpt">
      <div class="title-wrapper">
        <div class="title">
          输入兑换卡密
        </div>
        <svg id="close-button" class="close pointer" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g id="Dismiss">
            <path id="Shape" d="M4.08859 4.21569L4.14645 4.14645C4.32001 3.97288 4.58944 3.9536 4.78431 4.08859L4.85355 4.14645L10 9.293L15.1464 4.14645C15.32 3.97288 15.5894 3.9536 15.7843 4.08859L15.8536 4.14645C16.0271 4.32001 16.0464 4.58944 15.9114 4.78431L15.8536 4.85355L10.707 10L15.8536 15.1464C16.0271 15.32 16.0464 15.5894 15.9114 15.7843L15.8536 15.8536C15.68 16.0271 15.4106 16.0464 15.2157 15.9114L15.1464 15.8536L10 10.707L4.85355 15.8536C4.67999 16.0271 4.41056 16.0464 4.21569 15.9114L4.14645 15.8536C3.97288 15.68 3.9536 15.4106 4.08859 15.2157L4.14645 15.1464L9.293 10L4.14645 4.85355C3.97288 4.67999 3.9536 4.41056 4.08859 4.21569L4.14645 4.14645L4.08859 4.21569Z" fill="#424242"/>
          </g>
        </svg>
      </div>
      <div class="coupon-input-wrapper">
        <div class="input-wrapper">
          <input type="text disabled" id="coupon-input" placeholder="请输入卡密">
        </div>
      </div>
      <div class="button-wrapper">
        <button id="coupon-button" class="primary-button">兑换</button>
      </div>
      <div class="coupon-list-wrapper">
        <p class="label">小黑盒加速器卡密/优惠券</p>
        <div class="coupon-list">
        </div>
      </div>
    </div>
  </body>
  <script src="./common/initTheme.js"></script>
  <script type="module">
    import { getKeyList, getActivateVipKey } from './api/index.js'
    import { formatDate, getUrlParam, copyToClipboard } from './common/utils.js'
    import { toastError } from './common/toast.js'
    let heybox_id = getUrlParam('heybox_id')
    let code = getUrlParam('code')
    function init() {
      initCouponButton()
      updateCouponList()
    }
    function initCouponButton() {
      let couponInput = document.getElementById('coupon-input')
      let heyboxIdInput = document.getElementById('heybox-id-input')
      let button = document.getElementById('coupon-button')
      document.getElementById('close-button').addEventListener('click', close)
      if(code) {
        couponInput.value = code
        button.classList.remove('disabled')
      }
      couponInput.addEventListener('input', () => {
        if(couponInput.value.length > 0) {
          button.classList.remove('disabled')
        } else {
          button.classList.add('disabled')
        }
      })
      button.addEventListener('click', useCoupon)
      document.addEventListener('keydown', (e) => {
        if(e.key === 'Enter') {
          useCoupon()
        }
      })
    }
    function useCoupon() {
      let couponInput = document.getElementById('coupon-input')
      let coupon = couponInput.value
      console.log('coupon', coupon, heybox_id)
      if(coupon && heybox_id) {
        getActivateVipKey({
          key: coupon,
          activate_heybox_id: heybox_id
        }).then(res => {
          console.log(res)
          if(res.status == 'ok') {
            updateCouponList()
          } else {
            toastError(res.msg)
          }
        })
      }
    }
    function close() {
      window.electronAPI.close('coupon')
    }
    function updateCouponList() {
      let couponListEl = document.querySelector('.coupon-list')
      couponListEl.innerHTML = ''
      getKeyList().then(res => {
        console.log(res)
        if (res.status == 'ok') {
          let couponList = res.result.list || []
          if(couponList.length === 0) {
            couponListEl.innerHTML = '<p class="none-wrapper">空空如也</p>'
          } else {
            couponList.forEach(item => {
              let el = document.createElement('div')
              let can_not_use = item.state === 1 || item.expire_time * 1000 < Date.now()
              if(can_not_use) {
                el.classList.add('disabled')
              }
              el.classList.add('coupon-item')
              console.log('time', new Date(item.expire_time * 1000))
              let button = document.createElement('button')
              button.classList.add('opacity-button')
              if(item.key) {
                el.innerHTML = `
                  <div class="name">${item.desc}</div>
                  <div class="info">
                    <p class="key">${item.key}</p>
                    <p class="time">有效期至: <span>${formatDate(item.expire_time * 1000, 'YYYY-MM-DD HH:mm:ss')}</span></p>
                  </div>
                `
                button.innerHTML = item.state === 1 ? '已激活' : item.expire_time * 1000 < Date.now() ? '已过期' : '使用'
                if(!can_not_use) {
                  button.addEventListener('click', () => {
                    let couponInput = document.getElementById('coupon-input')
                    couponInput.value = item.key
                    let couponButton = document.getElementById('coupon-button')
                    couponButton.classList.remove('disabled')
                  })
                  let copyButton = document.createElement('button')
                  copyButton.classList.add('opacity-button')
                  copyButton.innerHTML = '复制'
                  copyButton.addEventListener('click', () => {
                    copyToClipboard(item.key)
                  })
                  el.appendChild(copyButton)
                }
              } else {
                el.innerHTML = `
                  <div class="name">优惠券</div>
                  <div class="info">
                    <p class="key">
                      <span>${item.discount / 10}折 </span>
                      <span>${item.name}</span>
                    </p>
                    <p class="time">有效期至: <span>${formatDate(item.expire_time * 1000, 'YYYY-MM-DD HH:mm:ss')}</span></p>
                    <p class="desc">${item.desc}</p>
                  </div>
                `
                button.innerHTML = item.state === 1 ? '已使用' : item.expire_time * 1000 < Date.now() ? '已过期' : '使用'
                if(!can_not_use) {
                  button.addEventListener('click', () => {
                    window.electronAPI.openPage('recharge', {heybox_id})
                  })
                }
              }
              el.appendChild(button)
              couponListEl.appendChild(el)
            })
          }
        } else{
          console.log(res.msg)
        }
      })
    }
    init()
  </script>
  <style>
    .coupon-cpt {
      box-sizing: border-box;
      width: 100%;
      height: 100%;
      padding: 0 24px 24px;
      background-color: var(--nb1r);
      border-radius: 16px;
      overflow: hidden;
      .input-wrapper {
        margin-bottom: 22px;
        position: relative;
        z-index: 1;
      }
      .button-wrapper {
        margin-bottom: 8px;
        button {
          height: 32px;
          line-height: 32px;
        }
      }
      .coupon-list-wrapper {
        .label {
          color: var(--nf3r);
          font-size: 14px;
          line-height: 20px;  
          margin-bottom: 8px;
        }
        .coupon-list {
          height: 206px;
          width: calc(100% + 8px);
          margin-right: -8px;
          overflow-y: scroll;
          .none-wrapper {
            margin-top: 108px;
            transform: translateY(-50%);
            text-align: center;
            color: var(--nf3r);
            font-size: 16px;
            line-height: 18px;
          }
        }
      }
      .disabled {
        opacity: 0.4;
        button {
          cursor: default;
        }
      }
      .coupon-item {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        padding: 12px;
        gap: 12px;
        background-color: var(--nb2r);
        border-radius: 10px;
        margin-top: 8px;
        .name {
          color: var(--coupon-label);
          font-size: 14px;
          min-width: 53px;
          line-height: 20px;
          font-weight: 600;
        }
        .info {
          flex: 1;
        }
        .key {
          font-size: 14px;
          line-height: 20px;
          color: var(--nf1r);
          margin-bottom: 4px;
        }
        .time {
          font-size: 12px;
          line-height: 16px;
          color: var(--nf3r);
          
        }
        .desc {
          font-size: 12px;
          line-height: 16px;
          color: var(--nf3r);
        }
        .state {
          font-size: 12px;
          line-height: 16px;
          color: var(--nf3r);
        }
      }
      .coupon-item:first-child {
        margin-top: 0;
      }
    }
  </style>
</html>
