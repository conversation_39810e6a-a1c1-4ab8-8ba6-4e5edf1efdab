<template>
  <div class="empty-news">
    <div class="empty-content">
      <div class="empty-icon">
        <img src="https://imgheybox.max-c.com/dev/bbs/2025/07/14/********************************.jpeg" alt="暂无动态" />
      </div>
      <div class="empty-title">暂无动态</div>
      <div class="empty-description">动态功能将在后续版本推出</div>
    </div>
  </div>
</template>

<script setup>
</script>

<style lang="scss" scoped>
.empty-news {
  display: flex;
  padding: 50px 36px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  align-self: stretch;
  background: $general-color-bg-4;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 12px;
}

.empty-icon {
  img {
    width: 50x;
    height: 50px;
    object-fit: contain;
  }
}

.empty-title {
  color: 1111111;
  text-align: center;
  font-feature-settings: 'liga' off, 'clig' off;
  font-family: "PingFang SC";
  font-size: 18px;
  font-style: normal;
  font-weight: 500;
  line-height: normal;
}

.empty-description {
  align-self: stretch;
  color: $general-color-text-3;
  text-align: center;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: normal;
}
</style>
