const { screen, BrowserWindow } = require("electron");
const { getHeyboxDllPath } = require('@heybox/electron-utils')
const { requireAddon } = require('@heybox/electron-utils')
const { log, store } = require('@heybox/electron-utils')
const request = require('@heybox/electron-utils/request')
const utils = require('@heybox/electron-utils')
const DEFAULT_OVERLAY_WINDOW_CONFIG = {
  name: '',
  mouse: 'none',
  keyboard: 'none',
  transparent: true,
  visible: true,
  minWidth: 100,
  minHeight: 100,
  autoMoveInGamwWindow: true,
}

let isStarted = false;
let gameProcessPids = []
let evnet_callbacks = {}
let scaleFactorCache = {}
function getWindowPhysicalBounds(win) {
  if (!win || win.isDestroyed()) return 
  return getOverlayAddon().getWindowBounds(win.getNativeWindowHandle().readUInt32LE(0));
}

// do not use screen.dipToScreenPoint, it is less precise
function dipToScreen(px, {processId, windowId, scaleFactor} = {}) {
  if (!scaleFactor) {
    scaleFactor = cacheScaleFactor({processId, windowId})
  }
  if (!px) return 0
  if (typeof px === "number") {
    return Math.floor(px * scaleFactor);
  } else {
    let pointOrRect = {};
    for (const key in px) {
      pointOrRect[key] = Math.floor(px[key] * scaleFactor);
    }
    return pointOrRect;
  }
}

function screenToDip(px, {processId, windowId, scaleFactor} = {}) {
  if (!scaleFactor) {
    scaleFactor = cacheScaleFactor({processId, windowId})
  }
  if (!px) return 0
  if (typeof px === "number") {
    return Math.round(px / scaleFactor);
  } else {
    let pointOrRect = {};
    for (const key in px) {
      pointOrRect[key] = Math.round(px[key] / scaleFactor);
    }
    return pointOrRect;
  }
}

function cacheScaleFactor({processId, windowId}) {
  let scaleFactorCacheKey = processId || 'primaryScreen'
  if (!scaleFactorCache[scaleFactorCacheKey]) {
    // TODO 1.36.0 注入addon重构后，住副屏分辨率不一致时，使用主屏的转换因子，在副屏游戏注入不会有大小问题
    // 如果用副屏的转换因子，注入大小会错乱，先临时解决只用主屏的转换因子
    // 后续确定解决方案后，如果只用主屏的转换因子，去掉转换因子缓存
    let scaleFactor = screen.getPrimaryDisplay().scaleFactor    
    // let scaleFactor = windowId 
    // ? getOverlayAddon().getScaleFactorForWindow(windowId)?.scaleFactor
    // : screen.getPrimaryDisplay().scaleFactor
    scaleFactorCache[scaleFactorCacheKey] = scaleFactor
  }
  // console.log('cacheScaleFactor', processId, windowId, scaleFactorCache[scaleFactorCacheKey], scaleFactorCache, screen.getAllDisplays())
  return scaleFactorCache[scaleFactorCacheKey]
}

function clearScaleFactorCache({processId}) {
  let scaleFactorCacheKey = processId || 'primaryScreen'
  delete scaleFactorCache[scaleFactorCacheKey]
}

function startNode() {
  // getOverlayAddon().start只需要调用一次
  if (isStarted) return
  getOverlayAddon().start();

  getOverlayAddon().setEventCallback(function (event, payload) {
    try {
      // console.log('[getOverlayAddon().setEventCallback]', event, payload)
      if (event === "game.input") {
        const win = BrowserWindow.fromId(payload.windowId);
        if (win && !win.ignoreMouseEvents) {
          const inputEvent = getOverlayAddon().translateInputEvent(payload);
          if (inputEvent) {
            // if (inputEvent.type !== "mouseMove") {
            //   console.log("inputEvent:", inputEvent);
            // }

            if ("x" in inputEvent) inputEvent.x = screenToDip(inputEvent.x, {processId: payload.pid});
            if ("y" in inputEvent) inputEvent.y = screenToDip(inputEvent.y, {processId: payload.pid});
            if (inputEvent.type === "mouseWheel") {
              inputEvent.wheelTicksY = inputEvent.deltaY > 0 ? 1 : -1;
              inputEvent.wheelTicksX = inputEvent.deltaX > 0 ? 1 : -1;
            }
            win.webContents?.sendInputEvent(inputEvent);
          }
        }
      } else if (event === "game.window.focused") {
        // console.log("focusWindowId", payload.focusWindowId);

        BrowserWindow.getAllWindows().forEach((window) => {
          window.blurWebView();
        });

        const focusWin = BrowserWindow.fromId(payload.focusWindowId);
        if (focusWin) {
          focusWin.focusOnWebView();
        }
      } else if (event === "game.process") {
        let pid = payload.pid
        // 注入的进程默认enableOverlay位false，不展示，在injectWindow中的resolveFunc中设置成true
        // 防止语音重连导致所有注入过的进程都会显示overlay
        enableOverlay(pid, false)

        // 同一个pid只会触发一次game.process，记录pid，同一个pid如果触发过了就不再等待触发
        if (!gameProcessPids.includes(pid)) {
          gameProcessPids.push(pid);
        }
        if (payload.path?.includes('\\MonsterHunterWilds')) {
          // v1.37.0 防止 steam hook 导致 怪物猎人荒野启动时崩溃 
          // 由于傻逼卡普空的bug，使用steam注入会导致 街霸6和怪物猎人荒野启动时直接崩溃
          if (store.get('user_account_settings.allow_steam_nethook') == 1) {
            log.info('[closeSteamNethook wilds]', event, payload, main_config.wilds_close_steam_nethook)
            store.set('user_account_settings.allow_steam_nethook', 0)
            store.setLocalConfig('wilds_close_steam_nethook', 1)
            mainWindow?.webContents?.send('localstorage:refresh', 'main_config', global.main_config)
            request.$post('/chatroom/v2/settings/account/update', {}, {
              setting: 'allow_steam_nethook',
              value: 0
            })
          }
        }
      } else if (event === "window.bounds.drag") {
        try {
          const data = JSON.parse(payload.message)
          for (let k in data.rect) {
            data.rect[k] = screenToDip(data.rect[k], {processId: payload.pid, windowId: data.windowId})
          }
          payload = {
            ...payload,
            ...data,
          } 
        } catch(e) {
          console.error(e)
        }
      }
      // else if (event === "game.hotkey.down") {
      //   let cb = hotkeys.find((hk) => hk.name === payload.name)?.callback;
      //   if (cb) {
      //     cb(payload);
      //   }
      // } 
      /**
       * event
       * game.process 注入成功回调
       * game.connection.close 注入的窗口关闭回调
       * graphics.window.event.resize 注入的窗口size变化回调
       */
      evnet_callbacks[event] && evnet_callbacks[event](payload)
    } catch (e) {
      console.error(e)
    }
  });
  // getOverlayAddon().setHotkeys(hotkeys);
  isStarted = true
}

function registerEvent (callbacks) {
  evnet_callbacks = {
    ...evnet_callbacks,
    ...callbacks
  }
}

// config参数同injectWindow函数注释
function addOverlayWindow(config) {
  startNode()
  const win = config.window
  utils.fixWindowId(win)
  const scaleFactor = cacheScaleFactor(config.gameWin)

  if (!config.rect) config.rect = {}
  for(let key in config.rect) {
    config.rect[key] = dipToScreen(config.rect[key], {scaleFactor})
  }

  if (config.rect.width === undefined || config.rect.height === undefined || config.rect.x === undefined || config.rect.y === undefined) {
    const windowPhysicalBounds = getWindowPhysicalBounds(win)
    config.rect = {
      ...windowPhysicalBounds,
      ...config.rect,
    }
  }

  if (config.maxHeight === undefined || config.minWidth === undefined) {
    let node_inject = require('./overlay')
    const rect = node_inject.getGameWindowSize(config.gameWin)
    const display = screen.getDisplayMatching(rect)
    config.maxWidth ?? (config.maxWidth = Math.floor(display.bounds.width * display.scaleFactor))
    config.maxHeight ?? (config.maxHeight = Math.floor(display.bounds.height * display.scaleFactor))
  }

  config.caption = {
    left: dipToScreen(config.caption?.left, {scaleFactor}),
    right: dipToScreen(config.caption?.right, {scaleFactor}),
    top: dipToScreen(config.caption?.top, {scaleFactor}),
    height: dipToScreen(config.caption?.height, {scaleFactor}),
  }
  config.dragBorderWidth = dipToScreen(config.dragBorderWidth, {scaleFactor})
  config.nativeHandle = win.getNativeWindowHandle().readUInt32LE(0)

  config = {
    ...DEFAULT_OVERLAY_WINDOW_CONFIG,
    ...config
  }

  if (config.autoMoveInGamwWindow) {
    const gameWinPhysicalBounds = getOverlayAddon().getWindowBounds(config.gameWin.windowId)
    config.rect.x = Math.min(Math.max(config.rect.x, 0), Math.max(gameWinPhysicalBounds.width - config.rect.width, 0))
    config.rect.y = Math.min(Math.max(config.rect.y, 0), Math.max(gameWinPhysicalBounds.height - config.rect.height, 0))
  }

  log.info('[addWindow]', config)
  getOverlayAddon().addWindow(win.id, config);

  const gpuCompositingEnable = utils.getGpuCompositingEnable()
  log.info('[inject addWindow]', win.id, gpuCompositingEnable)

  win.on("ready-to-show", () => {
    win.focusOnWebView();
  });

  winOnPaint(win)

  if (!win.ignoreMouseEvents) {
    win.webContents.on("cursor-changed", (event, type) => {
      let cursor;
      switch (type) {
        case "pointer":
          cursor = "IDC_ARROW";
          break;
        case "crosshair":
          cursor = "IDC_CROSS";
          break;
        case "hand":
          cursor = "IDC_HAND";
          break;
        case "text":
        case "vertical-text":
          cursor = "IDC_IBEAM";
          break;
        case "wait":
          cursor = "IDC_WAIT";
          break;
        case "help":
          cursor = "IDC_HELP";
          break;
        case "e-resize":
        case "w-resize":
        case "ew-resize":
        case "col-resize":
          cursor = "IDC_SIZEWE";
          break;
        case "n-resize":
        case "s-resize":
        case "ns-resize":
        case "row-resize":
          cursor = "IDC_SIZENS";
          break;
        case "nw-resize":
        case "se-resize":
        case "nwse-resize":
          cursor = "IDC_SIZENWSE";
          break;
        case "ne-resize":
        case "sw-resize":
        case "nesw-resize":
          cursor = "IDC_SIZENESW";
          break;
        case "move":
          cursor = "IDC_SIZEALL";
          break;
        case "progress":
          cursor = "IDC_APPSTARTING";
          break;
        case "nodrop":
        case "not-allowed":
          cursor = "IDC_NO";
          break;
        case "none":
          cursor = "";
          break;
        default:
          cursor = "IDC_ARROW";
      }
      getOverlayAddon().sendCommand({ command: "cursor", cursor });
    });
  }

  win.on("resize", () => {
    const rect = getWindowPhysicalBounds(win);
    if (!rect) return
    getOverlayAddon().sendWindowBounds(win.id, { 
      width: rect.width,
      height: rect.height
     });

    //  resize可能是由于窗口分辨率变化导致，需要重新计算缩放因子
    clearScaleFactorCache(config.gameWin)
    cacheScaleFactor(config.gameWin)
  });

  win.on("move", () => {
    console.warn("[addOverlayWindow move event] Window movement will not affect the injection window position. Please use the change function to modify the injection window coordinates(1.36.0+)")
  });

  win.on("closed", () => {
    log.info('[inject closeWindow]', win.id)
    getOverlayAddon().closeWindow(win.id);
  });
}

// 动态修改窗口状态，可用config包括 caption、dragBorderWidth、mouse、keyboard、visible
function updateOverlayWindow(win, config) {
  startNode()
  getOverlayAddon().updateWindow(win.id, config);
}

function winOnPaint(win) {
  if (!win || win._injectOnPaint || win.isDestroyed()) return
  const useSharedTexture = utils.getUseSharedTexture()
  win.webContents.on("paint", async (event, dirty, image) => {
    if (win.isDestroyed()) return
    if (useSharedTexture) {
      const textureInfo = event?.texture?.textureInfo;
      if (textureInfo) {
        getOverlayAddon().sendFrameBuffer2(win.id, textureInfo);
        // 延迟 1 帧再释放
        setTimeout(() => {
          event.texture.release();
        }, 33)
      }
    } else {
      getOverlayAddon().sendFrameBuffer(
        win.id,
        image.getBitmap(),
        image.getSize().width,
        image.getSize().height,
        dirty
      );
      }
  });
  win._injectOnPaint = true
}

async function injectByWindow(win) {
  startNode()
  let dllDir = await getHeyboxDllPath()
  if (dllDir) {
    win.dllDir = dllDir
  }
  log.info('[injectByWindow]', win)
  const res = getOverlayAddon().injectProcess(win);
  return res
}

function enableOverlay(pid, enable) {
  startNode()
  log.info("[enableOverlay]", pid, enable)
  return getOverlayAddon().sendCommand({ command: "overlay.enable", pid, enable });
}

function initSteamNethook(buf) {
  startNode()
  try {
    return getOverlayAddon().sendCommand({
      command: "steam.init",
      nethook: buf
    });
  } catch (error) {
    log.info('[initSteamNethook error]', error);
  }
}

function rejectSteam() {
  startNode()
  const res = getOverlayAddon().sendCommand({
    command: "steam.init",
    enableNetHook: false,
  });
  log.info('[rejectSteam]', res);
  return res
}

function getOverlayAddon() {
  return requireAddon("heybox-overlay-server")
}

module.exports = {
  getWindowBounds() {
    return getOverlayAddon().getWindowBounds(...arguments)
  },
  screenToDip,
  dipToScreen,
  cacheScaleFactor,
  clearScaleFactorCache,
  startNode,
  registerEvent,
  stopOverlay() {
    return getOverlayAddon().stopOverlay(...arguments)
  },
  addOverlayWindow,
  updateOverlayWindow,
  injectByWindow,
  getTopWindows() {
    return getOverlayAddon().getTopWindows(...arguments)
  },
  async injectSteam() {
    startNode()
    let dllDir = await getHeyboxDllPath(), params = {}
    if (dllDir) {
      params.dllDir = dllDir
    }
    log.info('[injectSteam]', params)
    return getOverlayAddon().injectSteam(params)
  },
  translateInputEvent() {
    return getOverlayAddon().translateInputEvent(...arguments)
  },
  initSteamNethook,
  rejectSteam,
  interceptInput(show = true, onlyMouse = false) {
    startNode()
    log.info("[interceptInput]", `{ command: "input.intercept", intercept: ${show} }`);
    return getOverlayAddon().sendCommand({ command: "input.intercept", intercept: show, onlyMouse });
  },
  enableOverlay,
  getSteamVersion() {
    return getOverlayAddon().getSteamVersion(...arguments)
  },
  gameProcessPids,
  getOverlayAddon,
  sendCommand() {
    startNode()
    log.info("[sendCommand]", ...arguments)
    return getOverlayAddon().sendCommand(...arguments)
  },
  getForegroundWindow() {
    return getOverlayAddon().getForegroundWindow(...arguments)
  },
  sendWindowBounds() {
    return getOverlayAddon().sendWindowBounds(...arguments)
  },
  getWindowPhysicalBounds,
};