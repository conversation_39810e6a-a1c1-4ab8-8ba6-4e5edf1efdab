{"name": "@heybox-app/launcher", "description": "Heybox App Launcher", "version": "1.0.0", "icon": "icon.ico", "main": "src/main.js", "build": {"appId": "com.heybox.app"}, "scripts": {"dev": "electron .", "build": "node build/set_env.js prod && electron-builder --config builder.config.js", "build:combine": "node build/set_env.js prod && electron-builder --config builder.config.js && node build/combine_launcher.js"}, "keywords": [], "author": "heybox", "license": "MIT", "dependencies": {"@heybox/electron-build-resources": "workspace:*", "@heybox/electron-utils": "workspace:*", "electron-log": "^4.4.8"}, "devDependencies": {"archiver": "^6.0.1", "electron": "33.2.0", "electron-builder": "26.0.16", "fs-extra": "^11.2.0"}}